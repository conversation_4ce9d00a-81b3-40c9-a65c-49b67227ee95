# 塔金数据平台服务端架构
## 架构主要功能
1. 具备基础的注册中心、基础的网关分发、各个微服务的负载均衡、服务间调用。
2. 公共的common模块工具类、基础常用配置
3. 数据库表CRUD基础dao、基础service、基础serviceImpl实现类、数据库model(属性驼峰形式)、基础controllor等代码的自动生成
4. base-service模块实现通过数据库数据模型逻辑计算出对应的关键字段的集合，使开发的同事在涉及到相关权限时不需关注权限逻辑。

## 项目优势
1. 具备基础的注册中心、基础的网关分发、各个微服务的负载均衡、服务间调用。
2. 公共的common模块工具类、基础常用配置
3. 数据库表CRUD基础dao、基础service、基础serviceImpl实现类、数据库model(属性驼峰形式)、基础controllor等代码的自动生成
4. base-service模块实现通过数据库数据模型逻辑计算出对应的关键字段的集合，使开发的同事在涉及到相关权限时不需关注权限逻辑。

## 使用注意事项
1. 具备基础的注册中心、基础的网关分发、各个微服务的负载均衡、服务间调用。
2. 公共的common模块工具类、基础常用配置
3. 数据库表CRUD基础dao、基础service、基础serviceImpl实现类、数据库model(属性驼峰形式)、基础controllor等代码的自动生成
4. base-service模块实现通过数据库数据模型逻辑计算出对应的关键字段的集合，使开发的同事在涉及到相关权限时不需关注权限逻辑。

## 重要更新记录

- 2021年06月15日  新增数据库查询工具类和方法包括：单表多条件查询、多条件分布于多表的多表联合查询并示例代码

- 2021年05月15日  调整项目：改变项目POM文件依赖关系、调整公共模块依赖关系

- 2021年04月25日  创建第一版：具备基础的注册中心、基础的网关分发、各个微服务的负载均衡、服务间调用

## 参考资料
* 其他参考：[塔金数据JAVA技术规范]


