{"properties": [{"name": "tarkin.quartz.enabled", "type": "java.lang.String", "description": "Description for tarkin.quartz.enabled."}, {"name": "scheduler.cron.sync-cloud-to-local", "type": "java.lang.String", "description": "Description for scheduler.cron.sync-cloud-to-local."}, {"name": "scheduler.cron.heartbeat-test", "type": "java.lang.String", "description": "Description for scheduler.cron.heartbeat-test."}, {"name": "scheduler.cron.fixFinancialTableData", "type": "java.lang.String", "description": "Description for scheduler.cron.fixFinancialTableData."}, {"name": "scheduler.cron.manageQuartzJob", "type": "java.lang.String", "description": "Description for scheduler.cron.manageQuartzJob."}, {"name": "sync.auto.pull-batch-size", "type": "java.lang.String", "description": "Description for sync.auto.pull-batch-size."}, {"name": "sync.auto.insert-batch-size", "type": "java.lang.String", "description": "Description for sync.auto.insert-batch-size."}, {"name": "sync.auto.retry-delay-millis", "type": "java.lang.String", "description": "Description for sync.auto.retry-delay-millis."}, {"name": "sync.auto.max-retry-times", "type": "java.lang.String", "description": "Description for sync.auto.max-retry-times."}, {"name": "sync.financial-table-fix.pull-batch-size", "type": "java.lang.String", "description": "Description for sync.financial-table-fix.pull-batch-size."}, {"name": "sync.financial-table-fix.insert-batch-size", "type": "java.lang.String", "description": "Description for sync.financial-table-fix.insert-batch-size."}, {"name": "scheduler.cron.sync-fix-data", "type": "java.lang.String", "description": "Description for scheduler.cron.sync-fix-data."}, {"name": "scheduler.cron.sync-official-website-data", "type": "java.lang.String", "description": "Description for scheduler.cron.sync-official-website-data."}, {"name": "tarkin.project_id", "type": "java.lang.String", "description": "Description for tarkin.project_id."}, {"name": "tarkin.scheduler.cron.sync-cloud-to-local", "type": "java.lang.String", "description": "Description for tarkin.scheduler.cron.sync-cloud-to-local."}, {"name": "tarkin.scheduler.cron.heartbeat-test", "type": "java.lang.String", "description": "Description for tarkin.scheduler.cron.heartbeat-test."}, {"name": "tarkin.scheduler.cron.sync-fix-data", "type": "java.lang.String", "description": "Description for tarkin.scheduler.cron.sync-fix-data."}, {"name": "tarkin.scheduler.cron.manage-quartz-job", "type": "java.lang.String", "description": "Description for tarkin.scheduler.cron.manage-quartz-job."}, {"name": "tarkin.scheduler.cron.sync-official-website-data", "type": "java.lang.String", "description": "Description for tarkin.scheduler.cron.sync-official-website-data."}, {"name": "tarkin.scheduler.enabled", "type": "java.lang.String", "description": "Description for tarkin.scheduler.enabled."}]}