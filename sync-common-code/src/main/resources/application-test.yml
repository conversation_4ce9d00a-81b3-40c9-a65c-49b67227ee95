# 用于在测试环境中本地使用
server:
  port: 8870

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    dynamic:
      primary: source
      datasource:
        source: #数据源1
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *******************************************************************************************************************************************************************************************************************************************************************************************
          username: syncdata_user
          password: ENC(HYxcLPlqP4nqTYrzc2yqFGGFBWoNp0SnhhG6aQCSJso=)
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
        target: #数据源2 (测试用本地mysql)
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ********************************************************************************************************************************************************************************************************************************************************************************************
          username: root
          password: 123456
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
    druid:
      initial-size: 50
      max-active: 6000
      min-idle: 10
      max-wait: 600000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
      filter:
        wall:
          enabled: false
          config:
        stat:
          log-slow-sql: true
          slow-sql-millis: 10
          merge-sql: false
      driver-class-name: com.mysql.cj.jdbc.Driver
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 去除druid配置
  redis:
    database: 0
    host: localhost
    port: 6379
    password: tarkinData1qaz2wsx
    timeout: 10000ms
    jedis:
      pool:
        max-active: 5
        max-idle: 25
        min-idle: 10
# mybatis-plus配置
mybatis-plus:
  # mapper文件的扫描路径
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.tjsj.**.modules.**.model
  # 全局配置
  global-config:
    banner: false
    # 表主键默认自增类型
    db-config:
      id-type: auto
  # 原生配置
  configuration:
    # 下划线转驼峰
    map-underscore-to-camel-case: false
    cache-enabled: false
    # 指定当查询结果中的字段值为 NULL 时，调用对应实体类的 setter 方法
    call-setters-on-nulls: true
    # mybatis-plus日志打印实现类
    # log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 驼峰下划线转换，默认Partial,开启后，会将数据库字段下划线命名转换为驼峰命名,比如user_name转换为userName
    default-enum-type-handler: org.apache.ibatis.type.EnumTypeHandler
    auto-mapping-behavior: full


# 日志打印配置
logging:
  level:
    root: INFO

# 项目自定义配置
tarkin:
  env: tarkin   # 项目运行环境
  dbType: tarkin   # 券商本地数据库类型
  profile: dev    # 项目环境配置
  # quartz定时任务配置
  quartz:
    enabled: true

# 自动同步云端数据到本地定时任务配置
scheduler:
  cron:
    sync-cloud-to-local: "0 0 0 31 2 ?" # 同步阿里云数据库数据到本地数据库
    #    sync-cloud-to-local: "0 30 0 * * ?"
    # 数据库心跳测试
    #heartbeat-test: "0/5 * * * * ?"
    heartbeat-test: "0 0 0 31 2 ?"
    # 同步修复财务表相关配置
    sync-fix-data: "0 0 0 31 2 ?"
    # 管理Quartz定时任务配置
    manage-quartz-job: "0 0 0 31 2 ?"
    #manage-quartz-job: "*/5 * * * * ?"
    # 同步兴业官网数据
    sync-official-website-data: "0 0 0 31 2 ?"

sync:
  auto:
    pull-batch-size: 10000     # 每批拉取数据量，默认10000条
    insert-batch-size: 10000    # 每批插入数据量，默认10000条
    retry-delay-millis: 2000       # 重试间隔时间，单位毫秒。默认2000毫秒
    max-retry-times: 2           # 最大重试次数，默认2次
  # 同步修复表相关配置
  financial-table-fix:
    pull-batch-size: 10000     # 每批拉取数据量，默认10000条
    insert-batch-size: 10000      # 每批插入数据量，默认10000条

# jasypt加密配置，用来加密和解密配置文件中的敏感信息
jasypt:
  encryptor:
    password: yky2000  # 这是用来加密和解密的密钥
    algorithm: PBEWithMD5AndDES  # 加密算法