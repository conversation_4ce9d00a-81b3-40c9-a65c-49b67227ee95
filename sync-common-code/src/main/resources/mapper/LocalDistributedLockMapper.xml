<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.common.lock.mapper.LocalDistributedLockMapper">
    <!-- 尝试获取锁（INSERT ... ON DUPLICATE KEY UPDATE） -->
    <insert id="tryLock">
        INSERT INTO tj_middle_ground.distributed_lock (lock_key, instance_id, expire_time, create_time)
        VALUES (#{lockKey}, #{instanceId}, #{expireTime}, #{createTime})
        ON DUPLICATE KEY UPDATE
        instance_id = CASE
        WHEN expire_time &lt; NOW() THEN VALUES(instance_id)
        ELSE instance_id
        END,
        expire_time = CASE
        WHEN expire_time &lt; NOW() THEN VALUES(expire_time)
        ELSE expire_time
        END,
        create_time = CASE
        WHEN expire_time &lt; NOW() THEN VALUES(create_time)
        ELSE create_time
        END
    </insert>

    <!-- 释放锁 -->
    <delete id="releaseLock">
        DELETE FROM tj_middle_ground.distributed_lock
        WHERE lock_key = #{lockKey} AND instance_id = #{instanceId}
    </delete>

    <!-- 验证锁的所有权 -->
    <select id="verifyLockOwnership" resultType="int">
        SELECT COUNT(*) FROM tj_middle_ground.distributed_lock
        WHERE lock_key = #{lockKey} AND instance_id = #{instanceId} AND expire_time > NOW()
    </select>

    <!-- 清理过期的锁 -->
    <delete id="cleanExpiredLocks">
        DELETE FROM tj_middle_ground.distributed_lock WHERE expire_time &lt; NOW()
    </delete>

    <delete id="testDeleteDistributedLock">
        delete
        from tj_middle_ground.distributed_lock
        where id = 1;
    </delete>
</mapper>