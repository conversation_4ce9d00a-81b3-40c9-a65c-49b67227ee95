<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modules.sync.mapper.CloudDatabaseSyncManualMapper">
    <resultMap id="BaseResultMap" type="com.tjsj.modules.sync.mapper.CloudDatabaseSyncManualMapper">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="info" column="info" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        info,
        create_time,
        update_time
    </sql>
</mapper>
