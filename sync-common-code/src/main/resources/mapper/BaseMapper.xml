<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modules.base.mapper.MyBaseMapper">

<!--  根据权限逻辑查询出当前登陆用户的menu_id集合  -->
    <select id="getMenuIds" resultType="java.lang.String">
        <if test="authorityScope == 0">
            SELECT t_menu.menu_id FROM t_menu
        </if>
        <if test="authorityScope != 0">
            SELECT t_menu_role.menu_id FROM t_user_role
            left join t_menu_role on t_user_role.role_id = t_menu_role.role_id
            where t_user_role.role_id = t_menu_role.role_id
            and t_user_role.user_id = #{uid}
        </if>
    </select>


    <!--  根据权限逻辑查询出当前登陆用户的权限范围数值最小的值，也即权限最大  -->
    <select id="getAuthorityScope" resultType="java.lang.Integer">
        SELECT t_role.authority_scope FROM t_user_role
        left join t_role on t_role.role_id = t_user_role.role_id
        where t_user_role.role_id = t_role.role_id
        and t_user_role.user_id = #{uid}
    </select>
    <!--  根据权限逻辑查询出当前登陆用户的权限下能查询到的uid集合  -->
    <select id="getUserIds" resultType="java.lang.String">
        <if test="authorityScope == 0">
            SELECT t_user.user_id FROM t_user
        </if>
        <if test="authorityScope != 0">
            SELECT t_user_role.user_id FROM t_user_role
            left join t_menu_role on t_user_role.role_id = t_menu_role.role_id
            where t_user_role.role_id = t_menu_role.role_id
            and t_user_role.user_id = #{uid}
        </if>
    </select>
    <!--  根据当前登陆用户的uid查询出当前登陆用户的权限下roleId集合  -->
    <select id="getRoleIds" resultType="java.lang.String">
        SELECT t_role.role_id FROM
            tj_middle_ground.t_role
        <if test="authorityScope != 0">
            where t_role.create_user = #{uid}
        </if>
    </select>



    <!--  example工具包单个example参数情况下的where条件部分  例如：query(Example example) -->
    <sql id="Where_Clause">
        <if test="_parameter != null">
            <where>
                <foreach collection="oredCriteria" item="criteria">
                    <if test="criteria.valid"> ${criteria.andOr} </if>
                    <include refid="com.tjsj.modules.base.mapper.MyBaseMapper.example_common_sql"/>
                </foreach>
            </where>
        </if>
    </sql>
    <!--  example工具包单个参数名称为example参数和多个其他参数情况下的where条件部分 例如：query(Example example,String userId) -->
    <sql id="Where_Clause_Multi_Parameter">
        <if test="_parameter != null">
            <where>
                <foreach collection="example.oredCriteria" item="criteria">
                    <if test="criteria.valid"> ${criteria.andOr} </if><include refid="com.tjsj.modules.base.mapper.MyBaseMapper.example_common_sql"/>
                </foreach>
            </where>
        </if>
    </sql>
    <!--  example工具包参数名称为examples的List<Example>参数和多个其他参数情况下的where条件部分 例如：query(List<Example> examples,String userId) -->
    <sql id="Where_Clause_Multi_Parameter0">
        <if test="_parameter != null">
            <where>
                <foreach collection="examples[0].oredCriteria" item="criteria">
                    <if test="criteria.valid"> ${criteria.andOr} </if> <include refid="com.tjsj.modules.base.mapper.MyBaseMapper.example_common_sql"/>
                </foreach>
            </where>
        </if>
    </sql>
    <sql id="Where_Clause_Multi_Parameter1">
        <if test="_parameter != null">
            <where>
                <foreach collection="examples[1].oredCriteria" item="criteria">
                    <if test="criteria.valid"> ${criteria.andOr} </if><include refid="com.tjsj.modules.base.mapper.MyBaseMapper.example_common_sql"/>
                </foreach>
            </where>
        </if>
    </sql>
    <sql id="Where_Clause_Multi_Parameter2">
        <if test="_parameter != null">
            <where>
                <foreach collection="examples[2].oredCriteria" item="criteria">
                    <if test="criteria.valid"> ${criteria.andOr} </if> <include refid="com.tjsj.modules.base.mapper.MyBaseMapper.example_common_sql"/>
                </foreach>
            </where>
        </if>
    </sql>
    <sql id="Where_Clause_Multi_Parameter3">
        <if test="_parameter != null">
            <where>
                <foreach collection="examples[3].oredCriteria" item="criteria">
                    <if test="criteria.valid"> ${criteria.andOr} </if><include refid="com.tjsj.modules.base.mapper.MyBaseMapper.example_common_sql"/>
                </foreach>
            </where>
        </if>
    </sql>
    <!--  example抽出的共同的sql部分  -->
    <sql id="example_common_sql">
        <if test="criteria.valid">
            <trim prefix="(" prefixOverrides="and" suffix=")">
                #{criteria.criteria[0].andOr} = 'and'
                <foreach collection="criteria.criteria" item="criterion">
                    <choose>
                        <when test="criterion.noValue">
                            ${criterion.andOr} ${criterion.condition}
                        </when>
                        <when test="criterion.singleValue">
                            ${criterion.andOr} ${criterion.condition} #{criterion.value}
                        </when>
                        <when test="criterion.betweenValue">
                            ${criterion.andOr} ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                        </when>
                        <when test="criterion.listValue">
                            ${criterion.andOr} ${criterion.condition}
                            <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                #{listItem}
                            </foreach>
                        </when>
                    </choose>
                </foreach>
            </trim>
        </if>
    </sql>
</mapper>
