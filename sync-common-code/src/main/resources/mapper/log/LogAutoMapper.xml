<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modules.log.mapper.LogAutoMapper">

<!--    <select id="listApiLog" resultType="LogAutoDO">-->
<!--        select *-->
<!--            from-->
<!--            tj_middle_ground.t_log_auto l-->
<!--        <where>-->
<!--            <if test=" null != request and request.date != null and request.date != '' ">-->
<!--                    and l.date = #{request.date}-->
<!--            </if>-->
<!--            <if test=" null != request and request.taskStatus != null ">-->
<!--                and l.task_status = #{request.taskStatus}-->
<!--            </if>-->
<!--            <if test=" null != request and request.method != null and request.method != '' ">-->
<!--                and l.method = #{request.method}-->
<!--            </if>-->
<!--        </where>-->
<!--        order by-->
<!--        <choose>-->
<!--            <when test="  null != request and request.orderBy != null and request.orderBy != '' ">-->
<!--                l.${request.orderBy} ${request.ascOrDesc}-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                l.update_time desc-->
<!--            </otherwise>-->
<!--        </choose>-->

<!--    </select>-->

</mapper>