<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modules.log.mapper.LogExceptionMapper">

<!--    <select id="listApiLogException" resultType="LogExceptionDO">-->
<!--        select *-->
<!--            from-->
<!--                tj_middle_ground.t_log_exception le-->
<!--        <where>-->
<!--            <if test=" null != request and request.date != null and request.date != '' ">-->
<!--                and le.date = #{request.date}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->
</mapper>