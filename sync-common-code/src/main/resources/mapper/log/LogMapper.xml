<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.modules.log.mapper.LogMapper">

<!--    <select id="listApiLog" resultType="LogDO">-->
<!--        select l.*,u.user_name as user-->
<!--        from tj_middle_ground.t_log l-->
<!--        LEFT JOIN-->
<!--        tj_middle_ground.t_user u-->
<!--            on l.user_name = u.user_id-->
<!--        <where>-->
<!--            <if test=" null != request and null != request.date and request.date != '' ">-->
<!--                and l.date = #{request.date}-->
<!--            </if>-->
<!--            <if test=" null != request and null != request.logLevel and request.logLevel != '' ">-->
<!--                and l.log_level = #{request.logLevel}-->
<!--            </if>-->
<!--            <if test=" null != request and null != request.userName and request.userName != '' ">-->
<!--                and l.user_name = #{request.userName}-->
<!--            </if>-->
<!--            <if test=" null != request and null != request.ipAddress and request.ipAddress != '' ">-->
<!--                and l.ip = #{request.ipAddress}-->
<!--            </if>-->
<!--            <if test=" request.operation != null and request.operation != '' ">-->
<!--                and l.operation like concat('%', #{request.operation}, '%')-->
<!--            </if>-->
<!--            <if test=" request.method != null and request.method != '' ">-->
<!--                and l.method like concat('%', #{request.method}, '%')-->
<!--            </if>-->
<!--        </where>-->
<!--        order by-->
<!--        <choose>-->
<!--            <when test=" request.orderBy == null or request.orderBy == '' ">-->
<!--                l.create_time desc-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                l.${request.orderBy} ${request.ascOrDesc}-->
<!--            </otherwise>-->
<!--        </choose>-->

<!--    </select>-->

<!--    <select id="listApiLogTypeCount" resultType="LogTypeVO">-->
<!--        <choose>-->
<!--            <when test=" null != request and null != request.queryType and request.queryType == 'userName' ">-->
<!--                select u.user_id,u.user_name,c.count-->
<!--                from-->
<!--                (select user_name,count(0) as count-->
<!--                from tj_middle_ground.t_log-->
<!--                <where>-->
<!--                    <if test=" null != request and null != request.date and request.date != '' ">-->
<!--                        and date = #{request.date}-->
<!--                    </if>-->
<!--                </where>-->
<!--                group by user_name )c-->
<!--                INNER JOIN-->
<!--                tj_middle_ground.t_user u-->
<!--                on c.user_name = u.user_id-->
<!--                order by c.count desc-->
<!--            </when>-->
<!--            <when test=" null != request and null != request.queryType and request.queryType == 'ipAddress' ">-->
<!--                select ip,count(0) as count-->
<!--                from tj_middle_ground.t_log-->
<!--                <where>-->
<!--                    <if test=" null != request and null != request.date and request.date != '' ">-->
<!--                        and date = #{request.date}-->
<!--                    </if>-->
<!--                </where>-->
<!--                group by ip-->
<!--                order by count desc-->
<!--            </when>-->
<!--        </choose>-->

<!--    </select>-->
</mapper>