# 兴业证券测试数据库同步数据配置

spring:
  datasource:
    dynamic:
      primary: target
      datasource:
        target: #数据源1
          url: **************************************************************************************************************************************************************************************************************************************
          username: xyyw@xyyw#bhtsobb02
          password: XYyw_2024!
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
        source: #数据源2
          url: *********************************************************************************************************************************************************************************************************************************************
          username: syncdata_user
          password: ENC(HYxcLPlqP4nqTYrzc2yqFGGFBWoNp0SnhhG6aQCSJso=)
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
    druid:
      initial-size: 50
      max-active: 6000
      min-idle: 10
      max-wait: 600000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
      filter:
        wall:
          enabled: false
        stat:
          log-slow-sql: true
          slow-sql-millis: 10
          merge-sql: false
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 去除druid配置
scheduling:
  enabled: false


dbType:
  xyzq
env: xyzq

scheduler:
  cron:
    sync-cloud-to-local: "0 30 0 * * ?"

sync:
  auto:
    pull-batch-size: 20000
    insert-batch-size: 20000