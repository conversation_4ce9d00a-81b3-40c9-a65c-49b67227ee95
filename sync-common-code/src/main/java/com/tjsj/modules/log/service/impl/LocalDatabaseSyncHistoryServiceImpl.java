package com.tjsj.modules.log.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.log.mapper.LocalDatabaseSyncHistoryMapper;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.log.service.LocalDatabaseSyncHistoryService;
import org.springframework.stereotype.Service;

/**
 * DatabaseSyncHistoryServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/26
 * @description 数据同步历史记录服务实现
 */
@Service
@DS(value = DataSourceNames.TARGET_DB)
public class LocalDatabaseSyncHistoryServiceImpl
        extends ServiceImpl<LocalDatabaseSyncHistoryMapper, DatabaseSyncHistoryDO>
        implements LocalDatabaseSyncHistoryService {


}




