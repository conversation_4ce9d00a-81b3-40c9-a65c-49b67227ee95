package com.tjsj.modules.log.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tjsj.common.utils.data.MyPageInfo;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.log.mapper.LogExceptionMapper;
import com.tjsj.modules.log.model.entity.LogException;
import com.tjsj.modules.log.model.request.LogRequest;
import com.tjsj.modules.log.service.LogExceptionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * LogExceptionServiceImpl
 *
 * <AUTHOR>
 * @date 2024/7/9 16:07
 * @description
 */
@Service
@DS(DataSourceNames.SOURCE_DB)
public class LogExceptionServiceImpl extends ServiceImpl<LogExceptionMapper, LogException>
    implements LogExceptionService {

    @Override
    public MyPageInfo<LogException> listApiLogException(LogRequest request) {
        Page<LogException> page = PageHelper.startPage(request.getCurrent(), request.getSize());
        List<LogException> logExceptionList = this.baseMapper.listApiLogException(request);
        return new MyPageInfo<>(page).setRecords(logExceptionList);
    }
}
