package com.tjsj.modules.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.common.utils.data.MyPageInfo;
import com.tjsj.modules.log.model.entity.LogDO;
import com.tjsj.modules.log.model.entity.LogException;
import com.tjsj.modules.log.model.request.LogRequest;
import org.apache.ibatis.annotations.Param;

/**
 * LogExceptionService
 *
 * <AUTHOR>
 * @date 2024/7/9 16:07
 * @description
 */
public interface LogExceptionService extends IService<LogException> {
    /**
     * 列出api日志异常
     *
     * @param request 日志异常
     * @return {@link MyPageInfo }<{@link LogDO }>
     * <AUTHOR>
     * @date 2024/07/09
     */
    MyPageInfo<LogException> listApiLogException(@Param("request") LogRequest request);
}
