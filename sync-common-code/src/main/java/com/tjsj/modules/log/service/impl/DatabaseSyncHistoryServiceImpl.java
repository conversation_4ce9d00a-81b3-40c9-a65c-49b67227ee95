package com.tjsj.modules.log.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.log.mapper.DatabaseSyncHistoryMapper;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.log.service.DatabaseSyncHistoryService;
import org.springframework.stereotype.Service;

/**
 * DatabaseSyncHistoryServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/26
 * @description 数据同步历史记录服务实现
 */
@Service
@DS(value = DataSourceNames.SOURCE_DB)
public class DatabaseSyncHistoryServiceImpl extends ServiceImpl<DatabaseSyncHistoryMapper, DatabaseSyncHistoryDO>
        implements DatabaseSyncHistoryService {


}




