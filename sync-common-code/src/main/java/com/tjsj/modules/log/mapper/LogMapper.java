package com.tjsj.modules.log.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.log.model.entity.LogDO;
import com.tjsj.modules.log.model.request.LogRequest;
import com.tjsj.modules.log.model.vo.LogTypeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 操作日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@Mapper
@DS(DataSourceNames.SOURCE_DB)
public interface LogMapper extends BaseMapper<LogDO> {


}
