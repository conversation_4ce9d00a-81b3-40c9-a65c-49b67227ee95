package com.tjsj.modules.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.common.utils.data.MyPageInfo;
import com.tjsj.modules.log.model.entity.LogAutoDO;
import com.tjsj.modules.log.model.request.LogRequest;
import org.aspectj.lang.JoinPoint;

/**
 * LogAutoService
 *
 * <AUTHOR>
 * @date 2024/07/09
 * @description 日志自动服务
 */
public interface LogAutoService extends IService<LogAutoDO> {


    /**
     * 做日志
     *
     * @param joinPoint  连接点
     * @param start      开始
     * @param end        结束
     * @param taskStatus 任务状态
     * @param e          e
     * <AUTHOR>
     * @date 2024/09/20
     */
    void doLog(JoinPoint joinPoint, long start, long end, TaskExecuteStatusEnum taskStatus, Throwable e);
}
