package com.tjsj.modules.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.modules.log.model.entity.LogAutoDO;
import com.tjsj.modules.log.model.request.LogRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * LogAutoMapper
 *
 * <AUTHOR>
 * @date 2024/07/09
 * @description 日志自动映射器
 */
@Mapper
public interface LogAutoMapper extends BaseMapper<LogAutoDO> {

}
