package com.tjsj.modules.log.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.common.utils.data.MyPageInfo;
import com.tjsj.common.utils.date.DateUtils;
import com.tjsj.common.utils.johnye.CustomUtils;
import com.tjsj.common.utils.string.StringUtils;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.log.mapper.LogAutoMapper;
import com.tjsj.modules.log.model.entity.LogAutoDO;
import com.tjsj.modules.log.model.request.LogRequest;
import com.tjsj.modules.log.service.LogAutoService;
import org.aspectj.lang.JoinPoint;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * LogAutoServiceImpl
 *
 * <AUTHOR> Ye
 * @date 2024/07/09
 * @description 日志汽车服务实现类
 */
@Service
@DS(DataSourceNames.SOURCE_DB)
public class LogAutoServiceImpl extends ServiceImpl<LogAutoMapper, LogAutoDO> implements LogAutoService {


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void doLog(JoinPoint joinPoint, long start, long end, TaskExecuteStatusEnum taskStatus, Throwable e) {
        LogAutoDO logAutoDO = new LogAutoDO();

        // 方法名和类名
        logAutoDO.setMethod(joinPoint.getSignature().getName())
                .setClassName(joinPoint.getSignature().getDeclaringTypeName());

        // 日期格式化器
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = new Date(start);
        Date endTime = new Date(end);
        long duration = (end - start) / 1000;

        String info;
        if (e != null) {
            // 记录错误信息
            String errorMsg = StringUtils.substring(e.toString(), 0, 1200);
            info = String.format("%s %s 方法执行失败，错误信息：%s，开始时间：%s，结束时间：%s，执行时长：%d秒",
                    dateFormat.format(new Date()), logAutoDO.getMethod(), errorMsg,
                    dateFormat.format(startTime), dateFormat.format(endTime), duration);
        } else {
            // 记录成功信息
            info = String.format("%s %s 自动更新成功，开始时间：%s，结束时间：%s，执行时长：%d秒",
                    dateFormat.format(new Date()), logAutoDO.getMethod(),
                    dateFormat.format(startTime), dateFormat.format(endTime), duration);
        }

        logAutoDO.setInfo(info)
                .setDate(dateFormat.format(new Date()))
                .setDuration(duration)
                .setStartTime(startTime)
                .setEndTime(endTime)
                .setTaskStatus(taskStatus);

        this.save(logAutoDO);
    }
}
