package com.tjsj.modules.log.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

/**
 * <AUTHOR>
 * @date 2024/7/9 14:00
 * @description - 日志调用类型返回类
 */
@Data
@Accessors(chain = true)
@Alias(value = "LogTypeVo")
public class LogTypeVo {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "调用ip")
    private String ip;

    @Schema(description = "数量")
    private Integer count;

}
