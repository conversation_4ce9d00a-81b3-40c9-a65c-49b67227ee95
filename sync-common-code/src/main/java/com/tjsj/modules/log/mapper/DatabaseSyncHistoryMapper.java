package com.tjsj.modules.log.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.base.model.vo.TypeCountVO;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.log.model.request.LogRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DatabaseSyncHistoryMapper
 *
 * <AUTHOR> Ye
 * @date 2025/07/30
 * @version 1.0.0
 * @description 针对表【t_database_sync_history(同步数据库日志表)】的数据库操作Mapper
 *
 */
@Mapper
@DS(DataSourceNames.SOURCE_DB)
public interface DatabaseSyncHistoryMapper extends BaseMapper<DatabaseSyncHistoryDO> {

    /**
     * 列表同步数据库日志
     *
     * @param request 请求
     * @return {@link List }<{@link DatabaseSyncHistoryDO }>
     * <AUTHOR>
     * @date 2024/07/09
     */
    List<DatabaseSyncHistoryDO> listSyncDatabaseLog(@Param("request") LogRequest request);

    /**
     * 列表同步数据库日志类型统计
     *
     * @param request 请求 queryType 查询类型
     * @return {@link List }<{@link TypeCountVO }>
     * <AUTHOR> Ye
     * @date 2024/07/11
     */
    List<TypeCountVO> listSyncDatabaseLogTypeCount(@Param("request") LogRequest request);

    /**
     * 查询心跳测试状态
     *
     * @return {@link Integer }
     * <AUTHOR> Ye
     * @date 2024/07/17
     */
    Integer queryHeartBeatStatus();
}




