package com.tjsj.modules.log.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.base.model.vo.TypeCountVO;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.log.model.request.LogRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DatabaseSyncHistoryMapper
 *
 * <AUTHOR>
 * @date 2025/07/30
 * @version 1.0.0
 * @description 针对表【t_database_sync_history(同步数据库日志表)】的数据库操作Mapper
 *
 */
@Mapper
@DS(DataSourceNames.TARGET_DB)
public interface LocalDatabaseSyncHistoryMapper extends BaseMapper<DatabaseSyncHistoryDO> {

}




