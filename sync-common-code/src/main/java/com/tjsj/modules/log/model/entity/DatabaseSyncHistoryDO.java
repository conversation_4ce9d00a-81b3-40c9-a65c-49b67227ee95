package com.tjsj.modules.log.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * DatabaseSyncHistoryDO
 *
 * <AUTHOR>
 * @date 2024/07/09
 * @version 1.0.0
 * @description 同步数据库日志表
 */
@TableName(value = "tarkin.t_database_sync_history")
@Data
@Accessors(chain = true)
@FieldNameConstants
@Schema(description = "同步数据库日志表")
@Alias(value = "DatabaseSyncHistoryDO")
public class DatabaseSyncHistoryDO implements Serializable {

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 同步任务id
     */
    @Schema(description = "同步任务id")
    @TableField(value = "task_id")
    private Integer taskId;

    /**
     * 执行语句
     */
    @Schema(description = "执行语句")
    @TableField(value = "execute_sql")
    private String executeSql;

    /**
     * 数据库名称
     */
    @Schema(description = "数据库名称")
    @TableField(value = "schema_name")
    private String schemaName;

    /**
     * 表名称
     */
    @Schema(description = "表名称")
    @TableField(value = "table_name")
    private String tableName;

    /**
     * 数据源的库名称
     */
    @Schema(description = "数据源的库名称")
    @TableField(value = "data_schema_name")
    private String dataSchemaName;

    /**
     * 数据源的表名称
     */
    @Schema(description = "数据源的表名称")
    @TableField(value = "data_table_name")
    private String dataTableName;

    /**
     * 同步日期
     */
    @Schema(description = "同步日期")
    @TableField(value = "date")
    @JSONField(format = "yyyy-MM-dd")
    private String date;

    /**
     * 更新数据的条数
     */
    @Schema(description = "更新数据的条数")
    @TableField(value = "data_num")
    private Integer dataNum;

    /**
     * 更新当前表所花时间(单位：毫秒)
     */
    @Schema(description = "更新当前表所花时间(单位：毫秒)")
    @TableField(value = "time_duration")
    private Integer timeDuration;

    /**
     * 项目id
     */
    @Schema(description = "项目id")
    @TableField(value = "project_id")
    private String projectId;

    /**
     * 数据库类型
     */
    @Schema(description = "数据库类型")
    @TableField(value = "db_type")
    private EnvironmentTypeEnum dbType;

    /**
     * 环境配置
     */
    @Schema(description = "环境配置")
    @TableField(value = "profile_type")
    private String profileType;

    /**
     * 同步类型：0自动同步，1手动同步，2Quartz定时任务，3表数据修复
     */
    @Schema(description = "同步类型：0自动同步，1手动同步，2Quartz定时任务，3表数据修复")
    @TableField(value = "sync_type")
    private SyncTypeEnum syncType;

    /**
     * 任务类型：1云到本地，2本地到云
     */
    @Schema(description = "任务类型")
    @TableField(value = "task_type")
    private SyncTaskTypeEnum taskType;

    /**
     * 任务开始时间
     */
    @Schema(description = "任务开始时间")
    @TableField(value = "start_time")
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    @Schema(description = "任务结束时间")
    @TableField(value = "end_time")
    private LocalDateTime endTime;

    /**
     * 任务执行状态：0成功，1失败
     */
    @Schema(description = "执行状态")
    @TableField(value = "task_status")
    private TaskExecuteStatusEnum taskExecuteStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @TableField(value = "remark")
    private String remark;


    /**
     * 重试次数
     */
    @Schema(description = "重试次数")
    @TableField(value = "retry_count")
    private Integer retryCount;


    /**
     *
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     *
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}