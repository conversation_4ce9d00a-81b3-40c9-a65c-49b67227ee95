package com.tjsj.modules.log.model.request;

import com.baomidou.mybatisplus.annotation.TableField;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.modules.base.model.request.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/7/9 17:48
 * @description - 日志请求参数类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "日志请求参数类")
public class LogRequest extends BaseRequest {

    @Schema(description = "查询类型")
    private String queryType;

    @Schema(description = "查询日期")
    private String date;

    @Schema(description = "日志级别")
    private String logLevel;

    @Schema(description = "操作人")
    private String userName;

    @Schema(description = "ip地址")
    private String ipAddress;

    @Schema(description = "任务执行状态")
    private TaskExecuteStatusEnum taskStatus;

    @Schema(description = "数据库名")
    private String schemaName;

    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "同步方法类型")
    private SyncTypeEnum syncType;

    @Schema(description = "调用接口")
    @TableField("operation")
    private String operation;

    @Schema(description = "调用方法")
    @TableField("method")
    private String method;

    @Schema(description = "排序字段")
    private String orderBy;

    @Schema(description = "排序方式")
    private String ascOrDesc;

}
