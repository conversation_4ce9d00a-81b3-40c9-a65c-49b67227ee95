package com.tjsj.modules.log.model.entity;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * LogDO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/09/20
 * @description 接口调用日志表
 */
@Data
@Accessors(chain = true)
@TableName("tj_middle_ground.t_log")
@Alias(value = "LogDO")
@Schema(name = "LogDO", description = "接口调用日志表")
public class LogDO {

    /**
     * 日志ID
     */
    @TableId(value = "log_id", type = IdType.AUTO)
    @Schema(description = "日志ID")
    private Long logId;

    @Schema(description = "用户")
    @TableField("user_name")
    private String userName;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    @TableField(exist = false)
    private String user;

    @Schema(description = "用户操作")
    @TableField("operation")
    private String operation;

    @Schema(description = "请求方法")
    @TableField("method")
    private String method;

    @Schema(description = "请求参数")
    @TableField("params")
    private String params;

    @Schema(description = "执行时长(毫秒)")
    @TableField("time")
    private Long time;

    @Schema(description = "日期")
    @TableField(value = "date")
    private LocalDate date;

    @Schema(description = "IP地址")
    @TableField("ip")
    private String ip;

    @Schema(description = "")
    @TableField(value = "exception")
    private String exception;

    @Schema(description = "")
    @TableField(value = "log_level")
    private String logLevel;

    @Schema(description = "")
    @TableField(value = "log_category")
    private String logCategory;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime createTime;


}
