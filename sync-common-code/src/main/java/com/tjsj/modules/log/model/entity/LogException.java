package com.tjsj.modules.log.model.entity;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.util.Date;

/**
 * LogException
 *
 * <AUTHOR>
 * @date 2024/07/09
 * @description 日志异常
 */
@Data
@Accessors(chain = true)
@TableName("tj_middle_ground.t_log_exception")
@Schema(name = "LogException对象", description = "异常信息表")
@Alias(value = "LogException")
public class LogException {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "用户名")
    @TableField("user_name")
    private String userName;

    @Schema(description = "用户操作")
    @TableField("operation")
    private String operation;

    @Schema(description = "请求方法")
    @TableField("method")
    private String method;

    @Schema(description = "请求参数")
    @TableField("params")
    private String params;

    @Schema(description = "执行时长(毫秒)")
    @TableField("time")
    private Long time;

    @TableField(value = "exception")
    private String exception;

    @TableField(value = "date", fill = FieldFill.INSERT)
    private Date date;

    @TableField(value = "type")
    private String type;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
