package com.tjsj.modules.log.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.time.LocalDateTime;
import java.util.Date;


/**
 * LogAutoDO
 *
 * <AUTHOR>
 * @date 2024/07/09
 * @description 日志自动
 */
@Data
@Accessors(chain = true)
@TableName("tj_middle_ground.t_log_auto")
@Schema(name = "TLogAuto对象", description = "自动更新信息日志表")
@Alias(value = "LogAutoDO")
public class LogAutoDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("info")
    private String info;

    @TableField(value = "date")
    private String date;

    @Schema(description = "方法名")
    @TableField(value = "method")
    private String method;

    @Schema(description = "任务执行状态")
    @TableField(value = "task_status")
    private TaskExecuteStatusEnum taskStatus;

    @Schema(description = "类名")
    @TableField(value = "class_name")
    private String className;

    @Schema(description = "执行开始时间")
    @TableField(value = "start_time")
    private Date startTime;

    @Schema(description = "执行结束时间")
    @TableField(value = "end_time")
    private Date endTime;

    @Schema(description = "执行时间")
    @TableField(value = "duration")
    private Long duration;

    /**
     * 执行环境
     */
    @Schema(description = "执行环境")
    @TableField(value = "env", fill = FieldFill.INSERT)
    private String env;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}