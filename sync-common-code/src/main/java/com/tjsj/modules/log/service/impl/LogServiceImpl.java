package com.tjsj.modules.log.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tjsj.common.annotation.PassLogInfo;
import com.tjsj.common.constants.LogConsts;
import com.tjsj.common.utils.crypto.TokenUtil;
import com.tjsj.common.utils.data.MyPageInfo;
import com.tjsj.common.utils.network.HttpContextUtils;
import com.tjsj.common.utils.network.IPUtils;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.log.mapper.LogMapper;
import com.tjsj.modules.log.model.entity.LogDO;
import com.tjsj.modules.log.model.entity.LogException;
import com.tjsj.modules.log.model.request.LogRequest;
import com.tjsj.modules.log.model.vo.LogTypeVo;
import com.tjsj.modules.log.service.LogExceptionService;
import com.tjsj.modules.log.service.LogService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@Service
@DS(DataSourceNames.SOURCE_DB)
@Slf4j
public class LogServiceImpl extends ServiceImpl<LogMapper, LogDO> implements LogService {

    @Resource
    private LogExceptionService logExceptionService;


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void saveSysLog(ProceedingJoinPoint joinPoint, long time, Object result, String exception, String logLevel) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            // 如果方法上有PassLogInfo注解，则不记录日志
            if (method.isAnnotationPresent(PassLogInfo.class)) {
                return;
            }

            LogDO tLog = new LogDO();

            addOperationInfo(tLog, joinPoint, method);

            tLog.setMethod(joinPoint.getTarget().getClass().getName() + "." + signature.getName() + "()");

            //请求的参数
            String params = Arrays.toString(joinPoint.getArgs());
            tLog.setParams(params);
            //返回结果
            /*String resultStr = new Gson().toJson(result);
            tLog.setResult(resultStr);*/

            //异常信息
            tLog.setException(exception)
                    .setUserName(TokenUtil.getCurrentUID());

            //获取request
            HttpServletRequest request = HttpContextUtils.getHttpServletRequest();
            //设置IP地址
            tLog.setIp(IPUtils.getIpAddr(request))
                    .setTime(time)
                    .setDate(LocalDate.now())
                    .setLogLevel(logLevel)
                    .setLogCategory(LogConsts.T_LOG_CATEGORY_API);

            //保存系统日志
            this.save(tLog);

            // 保存异常日志
            if (LogConsts.T_LOG_ERROR.equals(logLevel)) {
                LogException logException = BeanUtil.copyProperties(tLog, LogException.class)
                        .setDate(new Date());
                logExceptionService.save(logException);
            }

        } catch (Exception e) {
            log.error("❌ 保存系统日志异常", e);
        }
    }

    /**
     * 添加操作信息
     *
     * @param tLog      t日志
     * @param joinPoint 连接点
     * @param method    方法
     * <AUTHOR> Ye
     * @date 2024/07/25
     */
    private void addOperationInfo(LogDO tLog, ProceedingJoinPoint joinPoint, Method method) {
        Operation operationAnnotation = method.getAnnotation(Operation.class);
        if (operationAnnotation != null) {
            String tagInfo = "";
            String[] tags = operationAnnotation.tags();
            if (tags.length > 0) {
                tagInfo = "{" + String.join(", ", tags) + "} ";
            }
            tLog.setOperation(tagInfo + operationAnnotation.summary());
        }
    }

}
