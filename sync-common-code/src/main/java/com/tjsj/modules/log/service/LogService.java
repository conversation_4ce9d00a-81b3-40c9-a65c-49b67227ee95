package com.tjsj.modules.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.common.utils.data.MyPageInfo;
import com.tjsj.modules.log.model.entity.LogDO;
import com.tjsj.modules.log.model.request.LogRequest;
import com.tjsj.modules.log.model.vo.LogTypeVo;
import org.aspectj.lang.ProceedingJoinPoint;

import java.util.List;

/**
 * LogService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/09/20
 * @description 操作日志表 服务类
 */
public interface LogService extends IService<LogDO> {

    /**
     * 保存系统日志
     *
     * @param joinPoint 连接点
     * @param time      时间
     * @param result    结果
     * @param exception 异常
     * @param logLevel  日志评级
     * <AUTHOR>
     * @date 2024/09/20
     */
    void saveSysLog(ProceedingJoinPoint joinPoint, long time, Object result, String exception, String logLevel);


}
