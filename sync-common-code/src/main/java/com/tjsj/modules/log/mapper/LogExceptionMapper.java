package com.tjsj.modules.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.modules.log.model.entity.LogException;
import com.tjsj.modules.log.model.request.LogRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * LogExceptionMapper
 *
 * <AUTHOR>
 * @date 2024/07/09
 * @description 日志异常映射器
 */
@Mapper
public interface LogExceptionMapper extends BaseMapper<LogException> {
    /**
     * 列出api日志异常
     *
     * @param request 日志异常
     * @return {@link List }<{@link LogException }>
     * <AUTHOR>
     * @date 2024/07/09
     */
    List<LogException> listApiLogException(@Param("request") LogRequest request);
}
