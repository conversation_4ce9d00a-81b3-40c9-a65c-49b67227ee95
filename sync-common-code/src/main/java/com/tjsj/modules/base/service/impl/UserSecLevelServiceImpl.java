package com.tjsj.modules.base.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.utils.string.StrUtils;
import com.tjsj.modules.base.mapper.UserSecLevelMapper;
import com.tjsj.modules.base.model.entity.UserSecLevelDO;
import com.tjsj.modules.base.service.UserSecLevelService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * UserSecLevelServiceImpl
 *
 * <AUTHOR>
 * @date 2024/07/24
 * @description 用户自定义评级服务实现类
 */
@Service
public class UserSecLevelServiceImpl extends ServiceImpl<UserSecLevelMapper, UserSecLevelDO>
        implements UserSecLevelService {

    @Override
    public List<UserSecLevelDO> queryUserLevelList(String userId) {
        return this.list(
                Wrappers.<UserSecLevelDO>lambdaQuery()
                        .select(UserSecLevelDO::getSecCode, UserSecLevelDO::getUserId,
                                UserSecLevelDO::getLevel, UserSecLevelDO::getStrategyId)
                        .eq(!StrUtils.nullOrEmpty(userId), UserSecLevelDO::getUserId, userId));
    }

    @Override
    public List<UserSecLevelDO> queryUserSecSrtInfos(String userId, List<String> secCodes) {
        return this.baseMapper.queryUserSecSrtInfos(userId, secCodes);
    }

    @Override
    public void saveBatchUserLevel(String userId) {
        this.baseMapper.saveBatchUserLevel(userId);
    }
}
