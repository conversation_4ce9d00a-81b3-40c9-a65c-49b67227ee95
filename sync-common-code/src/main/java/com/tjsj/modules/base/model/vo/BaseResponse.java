package com.tjsj.modules.base.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.google.common.base.CaseFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BaseResponseVo
 *
 * <AUTHOR>
 * @date 2024/7/19 16:24
 * @description
 */
@Data
@Accessors(chain = true)
@Schema(name = "BaseResponseVo", description = "基础响应类")
public class BaseResponse {

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    private Integer current = 1;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    private Integer size = 10;

    /**
     * 驼峰转下划线(即获取真正的数据库字段名)
     */
    public static String camelToUnderline(String name) {
        //统一都转小写
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, name);
    }
}
