package com.tjsj.modules.base.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.utils.date.DateUtils;
import com.tjsj.modules.base.mapper.TradingDayMapper;
import com.tjsj.modules.base.model.entity.TradingDayDO;
import com.tjsj.modules.base.service.TradingDayService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/13 15:25
 * @description
 */
@Service
public class TradingDayServiceImpl extends ServiceImpl<TradingDayMapper, TradingDayDO>
        implements TradingDayService {

    @Override
    public String queryDesignateTradingDay(Integer beforeDays) {
        List<TradingDayDO> list = this.list(Wrappers.<TradingDayDO>lambdaQuery()
                .le(TradingDayDO::getDate, DateUtils.format(new Date(), DateUtils.DATE_PATTERN))
                .orderByDesc(TradingDayDO::getDate)
                .last("limit " + beforeDays));

        if (list.isEmpty()) {
            return null;
        }
        // 返回指定的第beforeDays个交易日
        return list.get(beforeDays - 1).getDate();
    }

    @Override
    public List<String> queryDateRangeTradingDays(String startDate, String endDate) {
        return this.list(Wrappers.<TradingDayDO>lambdaQuery()
                        .select(TradingDayDO::getDate)
                        .between(TradingDayDO::getDate, startDate, endDate)
                        .orderByAsc(TradingDayDO::getDate))
                .stream()
                .map(TradingDayDO::getDate)
                .collect(Collectors.toList());
    }

}
