package com.tjsj.modules.base.model.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.tjsj.common.config.converter.serialization.CustomBigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * ValueRangeVO
 *
 * <AUTHOR>
 * @date 2024/8/30 11:59
 * @description 值范围返回类
 */
@Data
@Accessors(chain = true)
@Schema(name = "ValueRangeVO",description = "值范围返回类")
public class ValueRangeVO {

    @Schema(description = "最小值")
    @JSONField(serializeUsing = CustomBigDecimalSerializer.class)
    public BigDecimal minValue;

    @Schema(description = "最大值")
    @JSONField(serializeUsing = CustomBigDecimalSerializer.class)
    public BigDecimal maxValue;

}
