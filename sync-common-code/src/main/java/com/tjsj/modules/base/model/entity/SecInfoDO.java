package com.tjsj.modules.base.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.base.SecTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SecInfoDO
 *
 * <AUTHOR>
 * @date 2024/8/8 18:50
 * @description 证券基本信息表
 */

@Schema(description = "证券基本信息表")
@Data
@Accessors(chain = true)
@TableName(value = "tj_middle_ground.t_sec_info")
@Alias(value = "SecInfoDO")
public class SecInfoDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 证券代码
     */
    @TableField(value = "sec_code")
    @Schema(description = "证券代码")
    private String secCode;

    /**
     * 证券名称
     */
    @TableField(value = "sec_name")
    @Schema(description = "证券名称")
    private String secName;

    /**
     * 证券类型
     */
    @TableField(value = "sec_type")
    @Schema(description = "证券类型")
    private SecTypeEnum secType;

    @TableField(value = "is_exchange")
    @Schema(description = "是否交易所")
    private CommonStatus ifExchange;

    @TableField(value = "peer_level_range")
    @Schema(description = "同业评级范围")
    private String peerLevelRange;

    @TableField(value = "peer_concentration_range")
    @Schema(description = "同业集中度范围")
    private String peerConcentrationRange;


    @TableField(value = "peer_haircut_range")
    @Schema(description = "同业折算率范围")
    private String peerHaircutRange;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}