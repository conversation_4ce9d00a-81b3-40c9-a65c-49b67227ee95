package com.tjsj.modules.base.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * PageRequest
 *
 * <AUTHOR>
 * @date 2024/8/13 23:13
 * @description 分页请求
 */
@Data
@Accessors(chain = true)
@Schema(description = "分页请求")
public class PageRequest {

    /**
     * 当前页码
     */
    @Schema(description = "当前页码", example = "1")
    @NotNull
    private Integer current = 1;

    /**
     * 每页记录数
     */
    @Schema(description = "每页记录数", example = "10")
    @NotNull
    private Integer size = 10;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "secCode")
    private String orderBy;

    /**
     * 排序方式
     */
    @Schema(description = "排序方式", example = "asc")
    private String ascOrDesc;

    /**
     * 筛选证券代码
     */
    @Schema(description = "筛选证券代码", example = "000001")
    private String secCode;
}
