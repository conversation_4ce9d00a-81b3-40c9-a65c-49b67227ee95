package com.tjsj.modules.base.model.request;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * BaseRequest
 *
 * <AUTHOR>
 * @date 2024/7/19 15:17
 * @description 请求参数类基础类
 */
@Data
@Accessors(chain = true)
@Schema(description = "请求参数类基础类")
public class BaseRequest implements Serializable {

    @ExcelIgnore
    @TableField(exist = false)
    @Schema(description = "分页参数-当前页码")
    private Integer current = 1;

    @ExcelIgnore
    @TableField(exist = false)
    @Schema(description = "分页参数-每页数据量")
    private Integer size = 10;

    @Schema(description = "排序方式")
    @TableField(exist = false)
    @JSONField(serialize = false)
    @ExcelIgnore
    private String orderBy;

    @Schema(description = "升序或降序")
    @TableField(exist = false)
    @JSONField(serialize = false)
    @ExcelIgnore
    private String ascOrDesc;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "开始时间")
    private String startDate;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "结束时间")
    private String endDate;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "证券代码")
    private String secCode;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "是否系统用户")
    private Boolean isSystemUser;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "用户id")
    private String userId;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "用户名称")
    private String userName;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "是否仅查询股票代码，默认false")
    private Boolean isStockId = false;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "矩阵id")
    private Integer matrixId;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "矩阵日期")
    private String matrixDate;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "股票板块")
    private String stockType;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "系统评级")
    private String level;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "用户评级")
    private String userLevel;

    @ExcelIgnore
    @JSONField(serialize = false)
    @TableField(exist = false)
    @Schema(description = "查询类型")
    private String queryType;


    /**
     * 驼峰转下划线(即获取真正的数据库字段名)
     */
    public static String camelToUnderline(String name) {
        if (null == name) {
            return null;
        } else {
            return StrUtil.toUnderlineCase(name);
        }
    }

    /**
     * 校验分页参数是否合法
     *
     * @throws IllegalArgumentException 如果分页参数不合法
     */
    @Schema(description = "校验分页参数")
    @JSONField(serialize = false)
    public void validatePaginationParams() throws IllegalArgumentException {
        if (current < 0) {
            throw new IllegalArgumentException("当前页码参数错误");
        }
        if (size < 0) {
            throw new IllegalArgumentException("每页数据量参数错误");
        }
    }
}
