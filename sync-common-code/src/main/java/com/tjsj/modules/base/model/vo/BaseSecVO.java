package com.tjsj.modules.base.model.vo;

import com.tjsj.common.enums.base.SecTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BaseSecVO
 *
 * <AUTHOR>
 * @date 2024/8/8 15:32
 * @description 基础证券返回类
 */
@Data
@Accessors(chain = true)
@Schema(name = "BaseSecVO", description = "基础证券返回类")
public class BaseSecVO {

    @Schema(description = "证券代码")
    private String secCode;

    @Schema(description = "证券名称")
    private String secName;

    @Schema(description = "证券类型")
    private SecTypeEnum secType;


}
