package com.tjsj.modules.base.mapper;

import com.tjsj.modules.user.model.entity.UserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * MyBaseMapper
 *
 * <AUTHOR>
 * @date 2024/07/21
 * @description 我基础映射器
 */
@Mapper
public interface MyBaseMapper {

    List<String> getMenuIds(@Param("authorityScope") int authorityScope, @Param("uid") String uid);

    List<Integer> getAuthorityScope(@Param("uid") String uid);

    List<String> getRoleIds(@Param("authorityScope") int authorityScope, @Param("uid") String uid);

}
