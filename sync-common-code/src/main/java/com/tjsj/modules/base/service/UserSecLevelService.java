package com.tjsj.modules.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.base.model.entity.UserSecLevelDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * UserSecLevelService
 *
 * <AUTHOR> Ye
 * @date 2024/07/24
 * @description 用户自定义评级服务
 */
public interface UserSecLevelService extends IService<UserSecLevelDO> {
    /**
     * 查询用户评级列表,用户id不传即查询所有用户评级列表
     *
     * @param userId 用户id
     * @return {@link List }<{@link UserSecLevelDO }>
     * <AUTHOR> Ye
     * @date 2024/07/24
     */
    List<UserSecLevelDO> queryUserLevelList(String userId);

    /**
     * 查询用户证券策略信息
     *
     * @param userId   用户id
     * @param secCodes 证券代码
     * @return {@link List }<{@link UserSecLevelDO }>
     * <AUTHOR> Ye
     * @date 2024/08/22
     */
    List<UserSecLevelDO> queryUserSecSrtInfos(String userId, List<String> secCodes);

    /**
     * 保存批量用户评级
     *
     * @param userId 用户id
     * <AUTHOR> Ye
     * @date 2024/08/29
     */
    void saveBatchUserLevel(@Param("userId") String userId);
}
