package com.tjsj.modules.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;

/**
 * IBaseMapper
 *
 * <AUTHOR>
 * @date 2024/7/8 17:50
 * @description IBaseMapper，所有Mapper的基类
 * @version 1.0.0
 */
@Mapper
public interface IBaseMapper<T> extends BaseMapper<T> {

    /**
     * 批量插入
     *
     * @param entityList 实体集合
     * @return 批量插入成功的条数
     */
    Integer insertBatchSomeColumn(Collection<T> entityList);


}
