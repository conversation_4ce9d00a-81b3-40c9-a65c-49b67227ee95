package com.tjsj.modules.base.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

/**
 * StackedLineDataDTO
 *
 * <AUTHOR>
 * @date 2024/9/4 16:39
 * @description 堆叠折线图数据分析类
 */
@Data
@Accessors(chain = true)
@Alias(value = "StackedLineDataDTO")
@Schema(name = "StackedLineDataDTO",description = "堆叠折线图数据分析类")
public class StackedLineDataDTO {

    @Schema(description = "X轴数据")
    private Object xData;

    @Schema(description = "系列名称")
    private String seriesName;

    @Schema(description = "类型名称")
    private String typeName;

}
