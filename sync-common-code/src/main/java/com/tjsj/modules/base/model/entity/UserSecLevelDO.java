package com.tjsj.modules.base.model.entity;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * UserSecLevelDO
 * <p>
 *     数据插入时间：股票信息表插入数据启动触发器
 * </p>
 *
 * <AUTHOR>
 * @date 2024/07/11
 * @description 用户自定义评级
 */
@Data
@Accessors(chain = true)
@TableName("tj_middle_ground.t_user_sec_level")
@Schema(name = "UserSecLevelDO", description = "用户自定义评级")
@Alias(value = "UserSecLevelDO")
public class UserSecLevelDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Integer id;

    @TableField(value = "sec_code")
    @Schema(description = "证券代码")
    private String secCode;

    @TableField(value = "level")
    @Schema(description = "用户评级")
    private String level;

    @TableField(value = "user_id")
    @Schema(description = "用户ID")
    private String userId;

    @TableField(value = "strategy_id")
    @Schema(description = "策略ID")
    private Integer strategyId;

    @TableField(exist = false)
    @Schema(description = "策略名称")
    private String strategyName;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
