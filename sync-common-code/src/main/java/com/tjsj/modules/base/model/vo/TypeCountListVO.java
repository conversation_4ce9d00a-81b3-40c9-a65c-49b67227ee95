package com.tjsj.modules.base.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.util.List;

/**
 * TypeCountListVO
 *
 * <AUTHOR>
 * @date 2024/9/21 13:06
 * @description 类型统计列表
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@Alias(value = "TypeCountListVO")
@Schema(description = "类型统计列表返回类")
public class TypeCountListVO {

    /**
     * 类型统计列表
     */
    @Schema(description = "类型统计列表")
    private List<TypeCountVO> typeCountList;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private String type;

}
