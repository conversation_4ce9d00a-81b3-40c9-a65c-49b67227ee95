package com.tjsj.modules.base.model;

import com.tjsj.common.enums.base.CommonEnum;
import com.tjsj.common.utils.string.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * ResultData
 *
 * <AUTHOR> Ye
 * @date 2024/07/20
 * @description 统一返回结果类
 */
@Data
@Accessors(chain = true)
@Schema(name = "ResultData", description = "统一返回结果类")
public class ResData<T> implements Serializable {

    /**
     * 返回状态码
     */
    @NotNull(message = "返回状态码不能为null")
    private Integer code;

    /**
     * 返回信息
     */
    @NotNull(message = "返回信息不能为null")
    private String msg;

    /**
     * 返回数据
     */
    @NotNull(message = "返回数据不能为null")
    private T data;

    private ResData() {
        this.code = CommonEnum.SUCCESS.getStatus();
        this.msg = CommonEnum.SUCCESS.getMsg();
    }

    private ResData(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResData<T> setData(T data) {
        this.data = data;
        return this;
    }

    /**
     * 成功
     *
     * @param data 数据
     * @return ResultData<T>
     * <AUTHOR> Ye
     * @date 2024/09/24
     */
    public static <T> ResData<T> SUCCESS(T data) {
        return new ResData<T>().setData(data).setCode(CommonEnum.SUCCESS.getStatus());
    }

    /**
     * 成功
     *
     * @return ResultData<T>
     */
    public static <T> ResData<T> ok(T data) {
        return new ResData<T>().setData(data).setCode(CommonEnum.SUCCESS.getStatus());
    }

    /**
     * 成功
     *
     * @return ResultData<T>
     */
    public static <T> ResData<T> SUCCESS(String msg) {
        return new ResData<>(CommonEnum.SUCCESS.getStatus(), msg);
    }

    /**
     * 成功，使用默认信息
     *
     * @return ResultData<T>
     */
    public static <T> ResData<T> SUCCESS() {
        return new ResData<>(CommonEnum.SUCCESS.getStatus(), CommonEnum.SUCCESS.getMsg());
    }

    /**
     * 失败，参数中提供错误信息
     *
     * @return ResultData<T>
     */
    public static <T> ResData<T> FORBIDDEN(String msg) {
        if (StringUtils.isBlank(msg)) {
            return FORBIDDEN();
        } else {
            return new ResData<>(CommonEnum.FORBIDDEN.getStatus(), msg);
        }
    }

    /**
     * 失败
     *
     * @return ResultData<T>
     */
    public static <T> ResData<T> FORBIDDEN() {
        return new ResData<>(CommonEnum.FORBIDDEN.getStatus(), CommonEnum.FORBIDDEN.getMsg());
    }

}
