package com.tjsj.modules.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.common.utils.data.MyPageInfo;
import com.tjsj.modules.base.model.entity.SecInfoDO;
import com.tjsj.modules.base.model.vo.BaseSecVO;

/**
 * <AUTHOR>
 * @date 2024/8/8 18:50
 * @description 证券信息服务接口
 */

public interface SecInfoService extends IService<SecInfoDO> {


    /**
     * 模糊搜索证券
     *
     * @param secCode 证券代码
     * @param current 现在
     * @param size    大小
     * @return {@link MyPageInfo }<{@link BaseSecVO }>
     * <AUTHOR> Ye
     * @date 2024/08/08
     */
    MyPageInfo<BaseSecVO> fuzzySearchSec(String secCode, Integer current, Integer size);

}
