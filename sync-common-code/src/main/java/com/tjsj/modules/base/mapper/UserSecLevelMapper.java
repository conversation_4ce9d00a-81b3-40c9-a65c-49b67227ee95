package com.tjsj.modules.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.modules.base.model.entity.UserSecLevelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * UserSecLevelMapper
 *
 * <AUTHOR>
 * @date 2024/07/24
 * @description 用户自定义评级映射器
 */
@Mapper
public interface UserSecLevelMapper extends BaseMapper<UserSecLevelDO> {

    /**
     * 查询用户证券策略信息
     *
     * @param userId   用户id
     * @param secCodes 证券代码
     * @return {@link List }<{@link UserSecLevelDO }>
     * <AUTHOR>
     * @date 2024/08/22
     */
    List<UserSecLevelDO> queryUserSecSrtInfos(@Param("userId") String userId,
                                              @Param("secCodes") List<String> secCodes);

    /**
     * 保存批量用户评级
     *
     * @param userId 用户id
     * <AUTHOR>
     * @date 2024/08/29
     */
    void saveBatchUserLevel(@Param("userId") String userId);
}
