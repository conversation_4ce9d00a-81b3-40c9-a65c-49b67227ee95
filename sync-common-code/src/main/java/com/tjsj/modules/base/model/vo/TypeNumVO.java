package com.tjsj.modules.base.model.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.tjsj.common.config.converter.serialization.CustomBigDecimalSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.math.BigDecimal;

/**
 * TypeNumVO
 *
 * <AUTHOR>
 * @date 2024/8/25 12:44
 * @description 类型数量统计VO
 */
@Data
@Accessors(chain = true)
@Alias(value = "TypeNumVO")
@Schema(description = "类型数量统计VO")
public class TypeNumVO {

    @Schema(description = "类型名称")
    private String typeName;

    @Schema(description = "类型数量")
    @JSONField(serializeUsing = CustomBigDecimalSerializer.class)
    private BigDecimal count;

}
