package com.tjsj.modules.base.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tjsj.common.utils.data.MyPageInfo;
import com.tjsj.common.utils.string.StrUtils;
import com.tjsj.modules.base.mapper.SecInfoMapper;
import com.tjsj.modules.base.model.entity.SecInfoDO;
import com.tjsj.modules.base.model.vo.BaseSecVO;
import com.tjsj.modules.base.service.SecInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/8 18:50
 * @description 证券信息服务实现类
 */

@Service
public class SecInfoServiceImpl extends ServiceImpl<SecInfoMapper, SecInfoDO> implements SecInfoService {

    @Override
    public MyPageInfo<BaseSecVO> fuzzySearchSec(String secCode, Integer current, Integer size) {
        Page<BaseSecVO> page = PageHelper.startPage(current, size);

        if (StrUtils.nullOrEmpty(secCode)) {
            return MyPageInfo.buildPageInfo(page, new ArrayList<BaseSecVO>());
        } else {
            var voList = this.list(Wrappers.<SecInfoDO>lambdaQuery()
                            .select(SecInfoDO::getSecCode, SecInfoDO::getSecName, SecInfoDO::getSecType)
                            .like(SecInfoDO::getSecCode, secCode)
                            .or()
                            .like(SecInfoDO::getSecName, secCode)
                            .orderByAsc(SecInfoDO::getSecCode, SecInfoDO::getSecType))
                    .stream()
                    .map(sec -> BeanUtil.copyProperties(sec, BaseSecVO.class))
                    .collect(Collectors.toList());
            return MyPageInfo.buildPageInfo(page, voList);
        }

    }
}
