package com.tjsj.modules.base.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.Alias;

/**
 * TypeCountVO
 *
 * <AUTHOR>
 * @date 2024/7/21 14:21
 * @description 类型统计VO
 */
@Data
@Accessors(chain = true)
@Schema(description = "类型统计VO")
@Alias(value = "TypeCountVO")
@FieldNameConstants
public class TypeCountVO {

    @Schema(description = "类型名称")
    private String typeName;

    @Schema(description = "类型数量")
    private Long count;
}
