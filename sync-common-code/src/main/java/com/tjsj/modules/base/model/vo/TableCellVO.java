package com.tjsj.modules.base.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

/**
 * TableCellVO
 *
 * <AUTHOR>
 * @date 2024/7/20 13:21
 * @description
 */
@Data
@Accessors(chain = true)
@Schema(name = "TableCellVO", description = "矩阵单元格通用返回类")
@Alias(value = "TableCellVO")
public class TableCellVO {

    @Schema(description = "横坐标")
    private String across;

    @Schema(description = "纵坐标")
    private String vertical;

    @Schema(description = "状态")
    private Integer flag;

}
