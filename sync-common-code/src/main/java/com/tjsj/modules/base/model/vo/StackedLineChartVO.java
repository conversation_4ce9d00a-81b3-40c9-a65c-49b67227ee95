package com.tjsj.modules.base.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * StackedLineChartVO
 *
 * <AUTHOR>
 * @date 2024/08/21
 * @description StackedLineChartVO 是用于前端展示堆叠折线图的数据对象
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "StackedLineChartVO", description = "是用于前端展示堆叠折线图的数据对象")
public class StackedLineChartVO {

    /**
     * X 轴标签列表
     */
    private List<String> xAxisLabels;

    /**
     * 系列数据
     */
    private List<Series> seriesData;

    /**
     * 内部类，用于表示单个系列的数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Series {

        /**
         * 系列名称
         */
        private String name;

        /**
         * 系列数据
         */
        private List<Object> data;
    }
}

