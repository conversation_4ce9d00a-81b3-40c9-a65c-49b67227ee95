package com.tjsj.modules.base.service.impl;

import com.tjsj.modules.base.mapper.MyBaseMapper;
import com.tjsj.modules.base.service.BasicService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * BasicServiceImpl
 *
 * <AUTHOR>
 * @date 2024/08/08
 * @description 基本服务实现类
 */
@Service
public class BasicServiceImpl implements BasicService {

    @Resource
    private MyBaseMapper myBaseMapper;

    @Override
    public List<String> getMenuIds(Integer authorityScope,String uid) {
        return myBaseMapper.getMenuIds(authorityScope,uid);
    }

    @Override
    public List<Integer> getAuthorityScope(String uid) {
        return myBaseMapper.getAuthorityScope(uid);
    }

    @Override
    public List<String> getRoleIds(Integer authorityScope,String uid) {
        return myBaseMapper.getRoleIds(authorityScope,uid);
    }




}
