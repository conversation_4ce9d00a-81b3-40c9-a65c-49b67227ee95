package com.tjsj.modules.base.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.time.LocalDateTime;

/**
 * TradingDayDO
 *
 * <AUTHOR>
 * @date 2024/8/13 15:21
 * @description 交易日期
 */
@Data
@Accessors(chain = true)
@Alias(value = "TradingDayDO")
@Schema(name = "TradingDayDO", description = "交易日期")
@TableName(value = "pledgedata.t_tradingdays")
public class TradingDayDO {

    @TableId(value = "id")
    private Long id;

    @TableField(value = "date")
    private String date;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    @JSONField(serialize = false)
    private LocalDateTime createTime;

    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    @JSONField(serialize = false)
    private LocalDateTime updateTime;


}
