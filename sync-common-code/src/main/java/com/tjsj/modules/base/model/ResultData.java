package com.tjsj.modules.base.model;

import com.tjsj.common.enums.base.CommonEnum;
import com.tjsj.common.utils.string.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * ResultData
 *
 * <AUTHOR>
 * @date 2024/07/20
 * @description 统一返回结果类
 */
@Data
@Accessors(chain = true)
@Schema(name = "ResultData", description = "统一返回结果类")
public class ResultData implements Serializable {

    /**
     * 返回状态码
     */
    @NotNull(message = "返回状态码不能为null")
    private Integer code;

    /**
     * 返回信息
     */
    @NotNull(message = "返回信息不能为null")
    private String msg;

    /**
     * 返回数据
     */
    @NotNull(message = "返回数据不能为null")
    private Object data;

    private ResultData() {
        this.code = CommonEnum.SUCCESS.getStatus();
        this.msg = CommonEnum.SUCCESS.getMsg();
    }

    private ResultData(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResultData setData(Object data) {
        this.data = data;
        return this;
    }

    /**
     * 成功
     *
     * @return ResultData<T>
     */
    public static ResultData SUCCESS(String msg) {
        return new ResultData(CommonEnum.SUCCESS.getStatus(), msg);
    }

    /**
     * 成功
     *
     * @param data 数据
     * @return ResultData<T>
     * <AUTHOR> Ye
     * @date 2024/08/03
     */
    public static ResultData SUCCESS(Object data) {
        return new ResultData().setData(data).setCode(CommonEnum.SUCCESS.getStatus());
    }


    /**
     * 成功，使用默认信息
     *
     * @return ResultData<T>
     */
    public static ResultData SUCCESS() {
        return new ResultData(CommonEnum.SUCCESS.getStatus(), CommonEnum.SUCCESS.getMsg());
    }

    /**
     * 失败，参数中提供错误信息
     *
     * @return ResultData<T>
     */
    public static <T> ResultData FORBIDDEN(String msg) {
        if (StringUtils.isBlank(msg)) {
            return FORBIDDEN();
        } else {
            return new ResultData(CommonEnum.FORBIDDEN.getStatus(), msg);
        }
    }

    /**
     * 失败
     *
     * @return ResultData<T>
     */
    public static ResultData FORBIDDEN() {
        return new ResultData(CommonEnum.FORBIDDEN.getStatus(), CommonEnum.FORBIDDEN.getMsg());
    }

}
