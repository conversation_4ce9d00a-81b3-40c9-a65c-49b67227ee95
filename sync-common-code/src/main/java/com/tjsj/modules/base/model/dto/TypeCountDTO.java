package com.tjsj.modules.base.model.dto;

import com.tjsj.modules.base.model.vo.TypeCountVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

/**
 * TypeCountDTO
 *
 * <AUTHOR>
 * @date 2024/8/24 21:01
 * @description 类型统计DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Alias(value = "TypeCountDTO")
@Schema(description = "类型统计DTO")
public class TypeCountDTO extends TypeCountVO {

    @Schema(description = "项目名称")
    private String itemName;

}
