package com.tjsj.modules.base.model.entity;

import com.google.common.base.CaseFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BaseEntity
 *
 * <AUTHOR>
 * @date 2024/7/19 17:29
 * @description
 */
@Schema(description = "基础实体类")
@Data
@Accessors(chain = true)
public class BaseEntity {

//    @ExcelIgnore
//    @JSONField(serialize = false)
//    @TableField(exist = false)
//    private Integer current = 1;
//
//    @ExcelIgnore
//    @JSONField(serialize = false)
//    @TableField(exist = false)
//    private Integer size = 10;
//
//    @Schema(description = "排序方式")
//    @TableField(exist = false)
//    @JSONField(serialize = false)
//    @ExcelIgnore
//    private String orderBy;
//
//    @Schema(description = "升序或降序")
//    @TableField(exist = false)
//    @JSONField(serialize = false)
//    @ExcelIgnore
//    private String ascOrDesc;

    /**
     * 驼峰转下划线(即获取真正的数据库字段名)
     */
    public static String camelToUnderline(String name) {
        //统一都转小写
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, name);
    }

}
