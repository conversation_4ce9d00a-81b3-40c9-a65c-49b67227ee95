package com.tjsj.modules.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.base.model.entity.TradingDayDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/13 15:24
 * @description 交易日服务
 */
public interface TradingDayService extends IService<TradingDayDO> {

    /**
     * 查询指定交易日期
     *
     * @param beforeDays 指定多少天之前
     * @return {@link String }
     * <AUTHOR> Ye
     * @date 2024/08/13
     */
    String queryDesignateTradingDay(Integer beforeDays);


    /**
     * 查询日期范围交易日
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2024/08/13
     */
    List<String> queryDateRangeTradingDays(String startDate, String endDate);
}
