package com.tjsj.modules.base.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * StockTableInfoVO
 *
 * <AUTHOR>
 * @date 2024/7/14 0:23
 * @description 股票信息表返回类
 * @version 1.0.0
 */
@Data
@Accessors(chain = true)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@FieldNameConstants
@Alias(value = "StockTableInfoVO")
@Schema(name = "StockTableInfoVO", description = "股票信息表VO")
public class StockTableInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "股票代码")
    @ExcelProperty(value = "证券代码")
    private String stockId;

    @Schema(description = "股票名称")
    @ExcelProperty(value = "证券名称")
    private String stockName;

    @Schema(description = "行业名称")
    @ExcelProperty(value = "行业名称")
    private String industryName;

    @Schema(description = "行业名称二级")
    @ExcelProperty(value = "二级行业")
    @ColumnWidth(15)
    private String industryNameTwo;

    @Schema(description = "用户评级")
    @ExcelProperty(value = "当前用户评级")
    private String userLevel;

    @Schema(description = "策略评级")
    @ExcelProperty(value = "策略评级结果")
    private String afterLevel;

    @Schema(description = "塔金评级")
    @ExcelProperty(value = "塔金系统评级")
    private String level;

    @Schema(description = "同业分类区间")
    @ExcelProperty(value = "同业分类区间")
    private String levelRange;

    @Schema(description = "市值")
    @ExcelProperty(value = "市值")
    private BigDecimal totalAmount;

    @Schema(description = "股价")
    @ExcelProperty(value = "股价")
    private BigDecimal closes;

    @Schema(description = "财务评分")
    @ExcelProperty(value = "财务评分")
    private Integer financialScore;

    @Schema(description = "综合评分")
    @ExcelProperty(value = "综合评分")
    private Integer totalScore;

    @Schema(description = "市净率")
    @ExcelProperty(value = "市净率")
    private BigDecimal pb;

    @Schema(description = "市净率行业中位数")
    @ExcelProperty(value = "市净率行业中位数")
    private BigDecimal midPb;

    @Schema(description = "市盈率")
    @ExcelProperty(value = "市盈率")
    private BigDecimal pe;

    @Schema(description = "市盈率行业中位数")
    @ExcelProperty(value = "市盈率行业中位数")
    private BigDecimal midPe;

    @Schema(description = "每股净资产")
    @ExcelProperty(value = "每股净资产")
    private BigDecimal netAssetValuePerShare;

    @Schema(description = "扣非净利润")
    @ExcelProperty(value = "扣非净利润")
    private BigDecimal kfnetprofit;

    @Schema(description = "毛利率3年增速")
    @ExcelProperty(value = "毛利率3年增速")
    private BigDecimal grossMarginTrendThreeYear;

    @Schema(description = "毛利率5年增速")
    @ExcelProperty(value = "毛利率3年平均增速")
    private BigDecimal grossMarginTrendFiveYear;

    @Schema(description = "毛利率3年增速-毛利率5年增速")
    @ExcelProperty(value = "毛利率3年增速-毛利率5年增速")
    private BigDecimal grossMarginSub;

    @Schema(description = "扣非净利润3年增速")
    @ExcelProperty(value = "扣非净利润3年增速")
    private BigDecimal kfnetprofittrendthreeyear;

    @Schema(description = "扣非净利润5年增速")
    @ExcelProperty(value = "扣非净利润5年增速")
    private BigDecimal kfnetprofittrendfiveyear;

    @Schema(description = "扣非净利润3年增速-扣非净利润5年增速")
    @ExcelProperty(value = "扣非净利润3年增速-扣非净利润5年增速")
    private BigDecimal kfnetprofitsub;

    @Schema(description = "营业收入3年增速")
    @ExcelProperty(value = "营业收入3年增速")
    private BigDecimal incomeTrendThreeYear;

    @Schema(description = "营业收入5年增速")
    @ExcelProperty(value = "营业收入5年增速")
    private BigDecimal incomeTrendFiveYear;

    @Schema(description = "营业收入3年增速-营业收入5年增速")
    @ExcelProperty(value = "营业收入3年增速-营业收入5年增速")
    private BigDecimal incomeSub;

    @Schema(description = "3年营收负债增速差")
    @ExcelProperty(value = "3年营收负债增速差")
    private BigDecimal incomeDebtSubThreeYear;

    @Schema(description = "5年营收负债增速差")
    @ExcelProperty(value = "5年营收负债增速差")
    private BigDecimal incomeDebtSubFiveYear;

    @Schema(description = "5年亏损次数")
    @ExcelProperty(value = "5年亏损次数")
    private Integer losses;

    @Schema(description = "标签数量")
    @ExcelProperty(value = "标签数量")
    private Integer count;

    @Schema(description = "高风险标签数量")
    @ExcelProperty(value = "高风险标签数量")
    private Integer highcount;

    @Schema(description = "标签内容")
    @ExcelProperty(value = "标签内容")
    @ColumnWidth(200)
    private String labels;

    @Schema(description = "新评级理由")
    @ExcelIgnore
    private String newReason;

    @Schema(description = "旧评级理由")
    @ExcelIgnore
    private String oldReason;

    @Schema(description = "系统用户对应策略名称")
    @ExcelIgnore
    private String name;

    @Schema(description = "用户对应策略名称")
    @ExcelIgnore
    private String userPolicyName;

    @Schema(description = "二级行业id")
    @ExcelIgnore
    private String industryIdTwo;
}
