package com.tjsj.modules.sync.model.request;

import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.TaskExecStatus;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.modules.base.model.request.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SyncDataRequest
 *
 * <AUTHOR>
 * @date 2024/7/17 23:16
 * @description
 */
@Schema(description = "同步数据参数请求类")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SyncDataRequest extends BaseRequest {

    @Schema(description = "数据库名")
    private String schemaName;

    @Schema(description = "表名")
    private String tableName;

    @Schema(name = "dbType")
    private EnvironmentTypeEnum dbType;

    @Schema(description = "插入表名")
    private String insertTableName;

    @Schema(description = "任务状态")
    private TaskExecStatus taskStatus;

    @Schema(description = "启用状态")
    private CommonStatus enableStatus;

    @Schema(description = "是否全量更新")
    private CommonStatus ifFullUpdate;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    private SyncTaskTypeEnum taskType;

}
