package com.tjsj.modules.sync.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.enums.base.CommonStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * SyncDataTaskDO
 *
 * <AUTHOR>
 * @date 2024/11/26 17:42
 * @version 1.0.0
 * @description 同步数据任务
 */
@Schema(description = "同步数据任务")
@Data
@Accessors(chain = true)
@Alias(value = "SyncDataTask")
@TableName(value = "tarkin.t_sync_data_task")
public class SyncDataTaskDO implements Serializable {

    /**
     * 同步数据任务id
     */
    @TableId(value = "task_id", type = IdType.AUTO)
    @Schema(description = "同步数据任务id")
    private Integer taskId;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    @Schema(description = "项目id")
    @JSONField(ordinal = 150)
    private String projectId;


    /**
     * 同步库类型
     */
    @TableField(value = "db_type")
    @Schema(description = "同步库类型")
    private String dbType;

    /**
     * 环境配置
     */
    @TableField(value = "profile_type")
    @Schema(description = "环境配置")
    @JSONField(ordinal = 250)
    private String profileType;

    /**
     * 同步任务日期
     */
    @TableField(value = "`task_date`")
    @Schema(description = "同步任务日期")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate taskDate;

    /**
     * 同步表数量
     */
    @TableField(value = "sync_table_count")
    @Schema(description = "同步表数量")
    private Integer syncTableCount;

    /**
     * 同步任务执行状态：0执行成功，1执行失败
     */
    @TableField(value = "task_status")
    @Schema(description = "同步任务执行状态：0执行成功，1执行未完成")
    private CommonStatus taskStatus;

    /**
     * 删除状态
     */
    @TableField(value = "delete_status")
    @Schema(description = "删除状态")
    private CommonStatus deleteStatus;

    /**
     * 任务开始时间
     */
    @TableField(value = "task_start_time")
    @Schema(description = "任务开始时间")
    private LocalDateTime taskStartTime;

    /**
     * 任务结束时间
     */
    @TableField(value = "task_end_time")
    @Schema(description = "任务结束时间")
    private LocalDateTime taskEndTime;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}