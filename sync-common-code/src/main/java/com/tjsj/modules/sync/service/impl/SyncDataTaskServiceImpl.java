package com.tjsj.modules.sync.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.modules.sync.mapper.SyncDataTaskMapper;
import com.tjsj.modules.sync.model.entity.SyncDataTaskDO;
import com.tjsj.modules.sync.service.SyncDataTaskService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * SyncDataTaskServiceImpl
 *
 * <AUTHOR> Ye
 * @date 2024/11/26 17:42
 * @version 1.0.0
 * @description 同步数据任务服务实现类
 */
@Service
public class SyncDataTaskServiceImpl extends ServiceImpl<SyncDataTaskMapper, SyncDataTaskDO>
        implements SyncDataTaskService {

    @Resource
    private TarkinConfig tarkinConfig;


    @Override
    public int insertOrUpdate(SyncDataTaskDO record) {
        return baseMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(SyncDataTaskDO record) {
        return baseMapper.insertOrUpdateSelective(record);
    }

    @Override
    public SyncDataTaskDO generateSyncTask(int syncTableBatchSize) {

        SyncDataTaskDO syncDataTaskDO = new SyncDataTaskDO().setTaskStartTime(LocalDateTime.now())
                .setProjectId(tarkinConfig.getProjectId())
                .setDbType(tarkinConfig.getDbType())
                .setProfileType(tarkinConfig.getProfile())
                .setSyncTableCount(syncTableBatchSize)
                .setTaskDate(LocalDate.now());
        this.saveOrUpdate(syncDataTaskDO);
        return syncDataTaskDO;
    }
}
