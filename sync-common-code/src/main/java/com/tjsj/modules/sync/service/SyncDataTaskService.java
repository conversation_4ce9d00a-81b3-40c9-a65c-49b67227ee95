package com.tjsj.modules.sync.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.sync.model.entity.SyncDataTaskDO;

/**
 * SyncDataTaskService
 *
 * <AUTHOR> Ye
 * @date 2024/11/26 17:42
 * @version 1.0.0
 * @description 同步数据任务服务
 */
public interface SyncDataTaskService extends IService<SyncDataTaskDO> {


    int insertOrUpdate(SyncDataTaskDO record);

    int insertOrUpdateSelective(SyncDataTaskDO record);

    /**
     * 生成同步任务
     *
     * @param syncTableBatchSize 同步表批量大小
     * @return {@link Integer }
     * <AUTHOR> Ye
     * @date 2024/11/26
     */
    SyncDataTaskDO generateSyncTask(int syncTableBatchSize);
}
