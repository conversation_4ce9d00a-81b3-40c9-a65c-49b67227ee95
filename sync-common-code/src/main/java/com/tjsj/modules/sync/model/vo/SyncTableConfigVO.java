package com.tjsj.modules.sync.model.vo;


import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.time.LocalDateTime;

/**
 * SyncTableConfigVO
 *
 * <AUTHOR>
 * @date 2024/8/8 14:20
 * @description 同步表配置返回类
 */
@Data
@Alias(value = "SyncTableConfigVO")
@Accessors(chain = true)
@Schema(name = "SyncTableConfigVO", description = "同步数据库表配置返回类")
public class SyncTableConfigVO {

    /**
     *
     */
    private Integer id;

    /**
     * 同步库名称
     */
    @Schema(description = "同步库名称")
    private String schemaName;

    /**
     * 同步表名称
     */
    @Schema(description = "同步表名称")
    private String tableName;

    /**
     * 表注释
     */
    @Schema(description = "表注释")
    private String tableComment;

    /**
     * 启用状态
     */
    @Schema(description = "启用状态")
    private CommonStatus enableStatus;

    /**
     * 数据库类型
     */
    @Schema(description = "数据库类型")
    private EnvironmentTypeEnum dbType;

    /**
     * 是否全量更新
     */
    @Schema(description = "是否全量更新")
    private CommonStatus ifFullUpdate;

    /**
     * 表行数
     */
    @Schema(description = "表行数")
    private Integer tableRows;

    /**
     * 每批大小
     */
    @Schema(description = "每批大小")
    private Integer batchSize;

    /**
     * 包含字段
     */
    @Schema(description = "包含字段")
    private String includeColumn;

    /**
     * 排除字段
     */
    @Schema(description = "排除字段")
    private String excludeColumn;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    private SyncTaskTypeEnum taskType;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


}
