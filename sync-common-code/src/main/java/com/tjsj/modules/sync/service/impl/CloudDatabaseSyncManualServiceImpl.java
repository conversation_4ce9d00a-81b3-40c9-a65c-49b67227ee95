package com.tjsj.modules.sync.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.modules.sync.mapper.CloudDatabaseSyncManualMapper;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.modules.sync.service.CloudDatabaseSyncManualService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * CloudDatabaseSyncManualServiceImpl
 *
 * <AUTHOR> Ye
 * @date 2024/08/07
 * @description 针对表【t_database_sync_info(数据库同步指定表)】的数据库操作Service实现
 */
@Service
public class CloudDatabaseSyncManualServiceImpl extends ServiceImpl<CloudDatabaseSyncManualMapper,
        DataSyncManualDO> implements CloudDatabaseSyncManualService {

    @Override
    public DataSyncManualDO getOne(DataSyncManualDO localDatabaseSyncManual) {

        String executeSql = localDatabaseSyncManual.getExecuteSql();
        String dbType = localDatabaseSyncManual.getDbType();
        String deleteTableName = localDatabaseSyncManual.getDeleteTableName();
        String insertTableName = localDatabaseSyncManual.getInsertTableName();
        String deleteTableCondition = localDatabaseSyncManual.getDeleteTableCondition();
        String insertTableCondition = localDatabaseSyncManual.getInsertTableCondition();
        Integer deleteStartId = localDatabaseSyncManual.getDeleteStartId();
        Integer insertStartId = localDatabaseSyncManual.getInsertStartId();
        LocalDateTime deleteStartUpdateTime = localDatabaseSyncManual.getDeleteStartUpdateTime();
        LocalDateTime insertStartUpdateTime = localDatabaseSyncManual.getInsertStartUpdateTime();
        CommonStatus ifIdSync = localDatabaseSyncManual.getIfIdSync();
        CommonStatus ifTableDelete = localDatabaseSyncManual.getIfTableDelete();
        SyncTaskTypeEnum taskType = localDatabaseSyncManual.getTaskType();


        return this.getOne(Wrappers.<DataSyncManualDO>lambdaQuery()
                .eq(StrUtil.isNotEmpty(executeSql), DataSyncManualDO::getExecuteSql, executeSql)
                .eq(StrUtil.isNotEmpty(dbType), DataSyncManualDO::getDbType, dbType)
                .eq(StrUtil.isNotEmpty(deleteTableName), DataSyncManualDO::getDeleteTableName, deleteTableName)
                .eq(StrUtil.isNotEmpty(insertTableName), DataSyncManualDO::getInsertTableName, insertTableName)
                .eq(StrUtil.isNotEmpty(deleteTableCondition), DataSyncManualDO::getDeleteTableCondition,
                        deleteTableCondition)
                .eq(StrUtil.isNotEmpty(insertTableCondition), DataSyncManualDO::getInsertTableCondition,
                        insertTableCondition)
                .eq(null != deleteStartId, DataSyncManualDO::getDeleteStartId, deleteStartId)
                .eq(null != insertStartId, DataSyncManualDO::getInsertStartId, insertStartId)
                .eq(null != deleteStartUpdateTime, DataSyncManualDO::getDeleteStartUpdateTime,
                        deleteStartUpdateTime)
                .eq(null != insertStartUpdateTime, DataSyncManualDO::getInsertStartUpdateTime,
                        insertStartUpdateTime)
                .eq(ifIdSync != null, DataSyncManualDO::getIfIdSync, ifIdSync)
                .eq(ifTableDelete != null, DataSyncManualDO::getIfTableDelete, ifTableDelete)
                .eq(null != taskType, DataSyncManualDO::getTaskType, taskType));
    }
}




