package com.tjsj.modules.sync.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.sync.model.entity.SyncDataTaskDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * SyncDataTaskMapper
 *
 * <AUTHOR>
 * @date 2024/11/26 17:42
 * @version 1.0.0
 * @description 同步数据任务Mapper
 */
@Mapper
@DS(value = DataSourceNames.TARGET_DB)
public interface LocalSyncDataTaskMapper extends BaseMapper<SyncDataTaskDO> {
    int insertOrUpdate(SyncDataTaskDO record);

    int insertOrUpdateSelective(SyncDataTaskDO record);
}