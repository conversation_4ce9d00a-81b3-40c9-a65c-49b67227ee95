package com.tjsj.modules.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.modules.user.mapper.UserRoleMapper;
import com.tjsj.modules.user.model.entity.UserRole;
import com.tjsj.modules.user.service.UserRoleService;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * UserRoleServiceImpl
 *
 * <AUTHOR>
 * @date 2024/07/19
 * @description 用户角色服务实现类
 */
@Service
@Primary
@Schema(description = "用户-角色关联表服务实现类")
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole>
        implements UserRoleService {


    @Override
    public List<String> getUserRoleGreyMenuIds(String userId) {
        return this.baseMapper.listUserRolesGreyMenuId(userId);
    }

    @Override
    public List<String> queryRoleNameByUser(String userId) {
        return this.baseMapper.queryRoleNameByUser(userId);
    }
}
