package com.tjsj.modules.user.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.enums.base.CommonStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/3 20:51
 * @description 系统功能字典表
 */
@Schema(description = "系统功能字典表")
@Data
@Accessors(chain = true)
@TableName(value = "tj_middle_ground.t_function_dict")
@Alias(value = "FunctionDictDO")
public class FunctionDictDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 功能名称
     */
    @TableField(value = "function_name")
    @Schema(description = "功能名称")
    private String functionName;

    /**
     * 启用状态
     */
    @TableField(value = "enable_status")
    @Schema(description = "启用状态")
    private CommonStatus enableStatus;

    @JSONField(serialize = false)
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @JSONField(serialize = false)
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}