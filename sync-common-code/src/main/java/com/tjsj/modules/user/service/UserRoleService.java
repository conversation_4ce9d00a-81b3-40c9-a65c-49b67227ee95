package com.tjsj.modules.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.user.model.entity.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;


/**
 * UserRoleService
 *
 * <AUTHOR> Ye
 * @date 2024/07/19
 * @description iuser角色服务
 */
@Schema(description = "用户角色关联表服务类")
public interface UserRoleService extends IService<UserRole> {

    /**
     * 获取用户角色灰色菜单id列表
     * <p>
     *     通过获取用户所有角色，只要有一个角色的某菜单权限没有被置灰，即不返回置灰状态
     * </p>
     *
     * @param userId 用户id
     * @return {@link List }<{@link String }>
     * <AUTHOR> Ye
     * @date 2024/07/19
     */
    List<String> getUserRoleGreyMenuIds(String userId);

    /**
     * 查询角色名称通过用户
     *
     * @param userId 用户id
     * @return {@link List }<{@link String }>
     * <AUTHOR> Ye
     * @date 2024/07/19
     */
    List<String> queryRoleNameByUser(String userId);
}
