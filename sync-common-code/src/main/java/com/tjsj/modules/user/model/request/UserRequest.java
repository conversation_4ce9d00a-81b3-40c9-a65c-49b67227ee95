package com.tjsj.modules.user.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * UserRequest
 *
 * <AUTHOR>
 * @date 2024/9/4 10:20
 * @description 用户请求参数
 */
@Data
@Schema(name = "UserRequest", description = "用户请求参数")
public class UserRequest {

    @Schema(description = "角色ID列表")
    private List<String> roleIds;

    @Schema(description = "是否复制系统管理员策略评级")
    private Boolean ifSystemLevel;

}
