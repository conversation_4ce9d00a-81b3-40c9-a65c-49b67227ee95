package com.tjsj.modules.user.model.dto;

import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.modules.user.model.entity.UserDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

/**
 * UserDTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/24 23:44
 * @description 用户信息DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Alias(value = "UserDTO")
@Schema(description = "用户信息DTO")
public class UserDTO extends UserDO {


    /**
     * 特殊标记
     */
    @Schema(description = "特殊标记")
    private CommonStatus specialFlag;

}
