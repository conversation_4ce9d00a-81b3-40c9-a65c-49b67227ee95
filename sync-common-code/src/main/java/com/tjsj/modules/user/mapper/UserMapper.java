package com.tjsj.modules.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.modules.user.model.entity.FunctionDictDO;
import com.tjsj.modules.user.model.entity.UserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * UserMapper
 *
 * <AUTHOR>
 * @date 2024/08/11
 * @description 用户映射器
 */
@Mapper
public interface UserMapper extends BaseMapper<UserDO> {


    /**
     * 获取用户功能权限
     *
     * @param userId 用户id
     * @return {@link List }<{@link FunctionDictDO }>
     * <AUTHOR>
     * @date 2024/09/04
     */
    List<FunctionDictDO> getUserFunctions(@Param("userId") String userId);
}
