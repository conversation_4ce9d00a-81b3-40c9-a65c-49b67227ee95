package com.tjsj.modules.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.base.model.ResData;
import com.tjsj.modules.user.model.dto.UserDTO;
import com.tjsj.modules.user.model.entity.FunctionDictDO;
import com.tjsj.modules.user.model.entity.UserDO;
import com.tjsj.modules.user.model.request.UserRequest;

import java.util.List;

/**
 * UserService
 *
 * <AUTHOR>
 * @date 2024/08/16
 * @description 用户服务
 */
public interface UserService extends IService<UserDO> {

}
