package com.tjsj.modules.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.modules.user.mapper.FunctionRoleMapper;
import com.tjsj.modules.user.model.entity.FunctionRoleDO;
import com.tjsj.modules.user.service.FunctionRoleService;
import org.springframework.stereotype.Service;
/**
 * <AUTHOR>
 * @date 2024/9/3 21:00
 * @description
 */

@Service
public class FunctionRoleServiceImpl extends ServiceImpl<FunctionRoleMapper, FunctionRoleDO>
        implements FunctionRoleService {

}
