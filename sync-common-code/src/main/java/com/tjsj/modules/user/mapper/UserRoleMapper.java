package com.tjsj.modules.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.modules.user.model.entity.UserRole;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * UserRoleMapper
 *
 * <AUTHOR> Ye
 * @date 2024/07/19
 * @description 用户角色映射器
 */
@Mapper
@Schema(description = "用户-角色关联表映射器")
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 查询角色名称通过用户
     *
     * @param userId 用户id
     * @return {@link List }<{@link String }>
     * <AUTHOR> Ye
     * @date 2024/07/19
     */
    List<String> queryRoleNameByUser(@Param("userId") String userId);

    /**
     * 列表用户角色灰色菜单id
     *
     * @param userId 用户id
     * @return {@link List }<{@link String }>
     * <AUTHOR> Ye
     * @date 2024/07/24
     */
    List<String> listUserRolesGreyMenuId(@Param("userId") String userId);
}
