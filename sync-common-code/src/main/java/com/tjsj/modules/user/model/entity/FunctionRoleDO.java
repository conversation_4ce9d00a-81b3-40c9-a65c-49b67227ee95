package com.tjsj.modules.user.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * FunctionRoleDO
 *
 * <AUTHOR>
 * @date 2024/9/3 21:00
 * @description 角色-功能关系表
 */
@Schema(description = "角色-功能关系表")
@Data
@Accessors(chain = true)
@TableName(value = "tj_middle_ground.t_function_role")
@Alias(value = "FunctionRoleDO")
public class FunctionRoleDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 角色ID
     */
    @TableField(value = "role_id")
    @Schema(description = "角色ID")
    private String roleId;

    /**
     * 功能ID
     */
    @TableField(value = "function_id")
    @Schema(description = "功能ID")
    private Integer functionId;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}