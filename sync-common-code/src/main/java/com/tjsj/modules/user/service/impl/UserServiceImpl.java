package com.tjsj.modules.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.constants.UserConsts;
import com.tjsj.common.constants.ValidMessageConstant;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.base.SysFunctionType;
import com.tjsj.common.enums.base.ValidType;
import com.tjsj.common.utils.crypto.TokenUtil;
import com.tjsj.common.utils.johnye.UserUtils;
import com.tjsj.common.utils.string.StrUtils;
import com.tjsj.modules.base.model.ResData;
import com.tjsj.modules.base.service.UserSecLevelService;
import com.tjsj.modules.user.mapper.UserMapper;
import com.tjsj.modules.user.model.dto.UserDTO;
import com.tjsj.modules.user.model.entity.FunctionDictDO;
import com.tjsj.modules.user.model.entity.UserDO;
import com.tjsj.modules.user.model.entity.UserRole;
import com.tjsj.modules.user.model.request.UserRequest;
import com.tjsj.modules.user.service.UserRoleService;
import com.tjsj.modules.user.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * UserServiceImpl
 *
 * <AUTHOR> Ye
 * @date 2024/08/03
 * @description 用户服务实现类
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, UserDO> implements UserService {


}
