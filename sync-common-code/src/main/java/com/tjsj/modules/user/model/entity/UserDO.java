package com.tjsj.modules.user.model.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.enums.label.RiskWarningType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.Alias;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Data
@Accessors(chain = true)
@Alias(value = "UserDO")
@FieldNameConstants
@TableName("tj_middle_ground.t_user")
@Schema(name = "UserDO", description = "用户基础信息表")
@CheckCount(count = 1)
public class UserDO {

    @Schema(description = "主键ID，自动生成")
    @TableId(value = "user_id", type = IdType.ASSIGN_ID)
    private String userId;

    @Schema(description = "用户昵称，可重复")
    @TableField("user_name")
    private String userName;

    @Schema(description = "用户账户，登录使用，有唯一约束")
    @TableField("user_account")
    private String userAccount;

    @Schema(description = "用户密码")
    @TableField("password")
    private String password;

    @Schema(description = "公司名称")
    @TableField("company_name")
    private String companyName;

    @Schema(description = "部门名称")
    @TableField("dept_name")
    private String deptName;

    @Schema(description = "职位名称")
    @TableField("job_name")
    private String jobName;

    @Schema(description = "用户11位手机号，选填")
    @TableField("user_mobile")
    private String userMobile;

    @Schema(description = "用户邮箱，选填")
    @TableField("user_email")
    private String userEmail;

    @Schema(description = "该条数据是否可用，默认为Y:可用 N:不可用")
    @TableField("is_valid")
    private String valid;

    @Schema(description = "创建人uid")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;

    @Schema(description = "更新人uid")
    @TableField(value = "update_user", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;

    @Schema(description = "用户来源 0:默认，真实用户 1：虚拟用户，例如管理员admin之类的账户")
    @TableField("user_source")
    private Integer userSource;

    @Schema(description = "接口校验令牌，除了登录接口，其他接口均需要由client端携带给server端")
    @TableField("token")
    private String token;

    @Schema(description = "预警类型")
    @TableField("warningType")
    private RiskWarningType warningType;

    @Schema(description = "用户评级标准表关联ID")
    @TableField(value = "user_criteria_id")
    private Integer userCriteriaId;


    @Schema(description = "创建时间，默认是当前时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @JSONField(serialize = false)
    @Schema(description = "更新时间，默认是当前时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
