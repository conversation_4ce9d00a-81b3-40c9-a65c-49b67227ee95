package com.tjsj.modules.user.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.time.LocalDateTime;


/**
 * UserRole
 *
 * <AUTHOR>
 * @date 2024/07/19
 * @description 用户角色
 */
@Data
@Accessors(chain = true)
@TableName("tj_middle_ground.t_user_role")
@Schema(name = "UserRole对象", description = "用户-角色关联表")
@Alias(value = "UserRole")
public class UserRole {

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    @Schema(description = "用户表user_id")
    @TableField("user_id")
    private String userId;

    @Schema(description = "角色表的role_id")
    @TableField("role_id")
    private String roleId;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}
