package com.tjsj.modules.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.modules.user.mapper.FunctionDictMapper;
import com.tjsj.modules.user.model.entity.FunctionDictDO;
import com.tjsj.modules.user.service.FunctionDictService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/9/3 20:51
 * @description
 */

@Service
public class FunctionDictServiceImpl extends ServiceImpl<FunctionDictMapper, FunctionDictDO>
        implements FunctionDictService {

}
