package com.tjsj.modules.manage.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.modules.manage.mapper.LocalHeartBeatTestMapper;
import com.tjsj.modules.manage.model.entity.LocalHeartBeatTest;
import com.tjsj.modules.manage.service.LocalHeartBeatTestService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/7/17 17:38
 * @description
 */

@Service
public class LocalHeartBeatTestServiceImpl extends ServiceImpl<LocalHeartBeatTestMapper, LocalHeartBeatTest>
        implements LocalHeartBeatTestService {

    /**
     * 当前数据库类型
     */
    @Value("${tarkin.dbType}")
    private String dbType;

    /**
     * 当前环境配置
     */
    @Value("${tarkin.profile}")
    private String profileValue;


    @Override
    public void updateHeartBeatStatus() {
        this.update(Wrappers.<LocalHeartBeatTest>lambdaUpdate()
                .set(LocalHeartBeatTest::getStatus, CommonStatus.ENABLE)
                .eq(LocalHeartBeatTest::getDbType, dbType)
                .eq(LocalHeartBeatTest::getProfileType, profileValue));
    }

    @Override
    public boolean testHeartBeat() {

        LocalHeartBeatTest heartBeatTest = this.getOne(Wrappers.<LocalHeartBeatTest>lambdaQuery()
                .eq(LocalHeartBeatTest::getDbType, profileValue)
                .eq(LocalHeartBeatTest::getProfileType, profileValue));
        if (heartBeatTest == null) {
            return false;
        }
        return heartBeatTest.getStatus().equals(CommonStatus.ENABLE);
    }
}
