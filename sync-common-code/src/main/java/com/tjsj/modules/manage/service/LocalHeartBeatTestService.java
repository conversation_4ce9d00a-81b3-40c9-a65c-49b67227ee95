package com.tjsj.modules.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.manage.model.entity.LocalHeartBeatTest;

/**
 * <AUTHOR>
 * @date 2024/7/17 17:38
 * @description
 */

public interface LocalHeartBeatTestService extends IService<LocalHeartBeatTest> {



    /**
     * 更新心跳状态
     *
     * <AUTHOR> Ye
     * @date 2024/10/17
     */
    void updateHeartBeatStatus();

    /**
     * 测试本地数据库心跳
     *
     * @return boolean
     * <AUTHOR>
     * @date 2024/10/22
     */
    boolean testHeartBeat();

}
