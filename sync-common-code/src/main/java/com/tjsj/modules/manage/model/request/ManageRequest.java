package com.tjsj.modules.manage.model.request;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ManageRequest
 *
 * <AUTHOR>
 * @date 2024/7/17 19:01
 * @description 管理模块相关请求参数
 */
@Schema(name = "ManageRequest", title = "管理模块相关请求参数", description = "管理模块相关请求参数")
@Data
@Accessors(chain = true)
public class ManageRequest {

    @Schema(description = "表状态")
    private Integer tableStatus;

    @Schema(description = "表名")
    private String tableName;

    @Schema(description = "启用状态")
    private Integer enableStatus;

    @Schema(description = "类型")
    private String type;
}
