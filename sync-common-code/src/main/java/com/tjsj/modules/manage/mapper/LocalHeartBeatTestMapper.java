package com.tjsj.modules.manage.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.manage.model.entity.LocalHeartBeatTest;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2024/7/17 17:38
 * @description
 */

@Mapper
@DS(DataSourceNames.TARGET_DB)
public interface LocalHeartBeatTestMapper extends BaseMapper<LocalHeartBeatTest> {
}