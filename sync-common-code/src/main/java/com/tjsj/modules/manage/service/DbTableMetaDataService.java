package com.tjsj.modules.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.manage.model.entity.DbTableMetaDataDO;
import com.tjsj.modules.manage.model.request.ManageRequest;
import com.tjsj.modules.manage.model.vo.SchemaResVO;

import java.util.List;

/**
 * DbTableMetaDataService
 *
 * <AUTHOR> Ye
 * @date 2024/08/01
 * @description 针对表【database_table_history(截至2024-06-09数据库存在的所有表的信息)】的数据库操作Service
 */
public interface DbTableMetaDataService extends IService<DbTableMetaDataDO> {


    /**
     * 更新数据库表信息
     *
     * <AUTHOR> Ye
     * @date 2024/07/11
     */
    void updateTableMetaData();

    /**
     * 查询表元数据
     *
     * @param request 请求
     * @return {@link List }<{@link SchemaResVO }>
     * <AUTHOR> Ye
     * @date 2024/08/01
     */
    List<SchemaResVO> queryTableMetaData(ManageRequest request);

}
