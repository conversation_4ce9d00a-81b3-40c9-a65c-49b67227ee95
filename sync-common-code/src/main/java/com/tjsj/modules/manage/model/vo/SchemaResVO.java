package com.tjsj.modules.manage.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.util.List;

/**
 * SchemaResVO
 *
 * <AUTHOR>
 * @date 2024/8/1 19:58
 * @description 数据库返回类
 */
@Data
@Accessors(chain = true)
@Schema(description = "数据库返回类")
@Alias(value = "SchemaResVO")
public class SchemaResVO {

    @Schema(description = "数据库名称")
    private String schemaName;

    @Schema(description = "子列表")
    private List<DbTableResVo> tableList;
}
