package com.tjsj.modules.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.manage.model.entity.HeartBeatTestDO;

/**
 * <AUTHOR>
 * @date 2024/7/17 17:38
 * @description
 */

public interface HeartBeatTestService extends IService<HeartBeatTestDO> {


    /**
     * 测试云数据库心跳
     *
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/10/17
     */
    Boolean testHeartBeat();

    /**
     * 更新心跳状态
     *
     * <AUTHOR>
     * @date 2024/10/17
     */
    void updateHeartBeatStatus();

}
