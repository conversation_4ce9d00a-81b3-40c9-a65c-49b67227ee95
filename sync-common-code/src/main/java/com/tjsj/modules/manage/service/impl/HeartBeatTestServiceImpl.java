package com.tjsj.modules.manage.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.manager.ServiceSwitchManager;
import com.tjsj.modules.manage.mapper.HeartBeatTestMapper;
import com.tjsj.modules.manage.model.entity.HeartBeatTestDO;
import com.tjsj.modules.manage.service.HeartBeatTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * HeartBeatTestServiceImpl
 *
 * <AUTHOR>
 * @date 2024/7/17 17:38
 * @version 1.0.0
 * @description 心跳测试-服务实现类
 */
@Service
@Slf4j
public class HeartBeatTestServiceImpl extends ServiceImpl<HeartBeatTestMapper, HeartBeatTestDO>
        implements HeartBeatTestService {

    @Autowired
    private ServiceSwitchManager serviceSwitchManager;

    @Resource
    private TarkinConfig tarkinConfig;


    @Override
    public Boolean testHeartBeat() {
        HeartBeatTestDO heartBeatTest = this.getOne(Wrappers.<HeartBeatTestDO>lambdaQuery()
                .eq(HeartBeatTestDO::getProjectId, tarkinConfig.getProjectId())
                .eq(HeartBeatTestDO::getDbType, tarkinConfig.getDbType())
                .eq(HeartBeatTestDO::getProfileType, tarkinConfig.getProfile()));
        if (heartBeatTest == null) {
            return false;
        }

        // 读取并更新服务开关状态
        updateServiceSwitchStatus(heartBeatTest);
        // 读取并更新定时任务开关状态
        updateSchedulerSwitchStatus(heartBeatTest);

        return heartBeatTest.getStatus().equals(CommonStatus.ENABLE);
    }


    @Override
    public void updateHeartBeatStatus() {
        this.update(Wrappers.<HeartBeatTestDO>lambdaUpdate()
                .set(HeartBeatTestDO::getStatus, CommonStatus.ENABLE)
                .eq(HeartBeatTestDO::getProjectId, tarkinConfig.getProjectId())
                .eq(HeartBeatTestDO::getDbType, tarkinConfig.getDbType())
                .eq(HeartBeatTestDO::getProfileType, tarkinConfig.getProfile()));
    }

    /**
     * 更新服务开关状态
     *
     * @param heartBeatTest 心跳测试数据
     */
    private void updateServiceSwitchStatus(HeartBeatTestDO heartBeatTest) {
        try {
            CommonStatus serviceSwitch = heartBeatTest.getServiceSwitch();
            if (serviceSwitch != null) {
                // 更新服务开关管理器的状态
                serviceSwitchManager.checkAndUpdateStatus(serviceSwitch);

                log.debug("服务开关状态已更新: {} ({})",
                        serviceSwitch.getDescription(), serviceSwitch.getCode());
            } else {
                // 如果数据库中没有设置服务开关，默认为启用
                serviceSwitchManager.checkAndUpdateStatus(CommonStatus.ENABLE);
                log.debug("服务开关状态默认为启用");
            }
        } catch (Exception e) {
            log.error("更新服务开关状态失败: {}", e.getMessage(), e);
            // 异常情况下默认为启用，确保服务可用性
            serviceSwitchManager.checkAndUpdateStatus(CommonStatus.ENABLE);
        }
    }


    /**
     * 更新定时任务开关状态
     *
     * @param heartBeatTest 心跳测试数据
     *
     * <AUTHOR> Ye
     * @date 2025/07/01
     */
    private void updateSchedulerSwitchStatus(HeartBeatTestDO heartBeatTest) {

        try {
            CommonStatus schedulerSwitch = heartBeatTest.getSchedulerSwitch();
            if (schedulerSwitch != null) {
                // 更新定时任务开关状态
                serviceSwitchManager.checkAndUpdateSchedulerStatus(schedulerSwitch);
                log.debug("定时任务开关状态已更新: {} ({})",
                        schedulerSwitch.getDescription(), schedulerSwitch.getCode());
            } else {
                // 如果数据库中没有设置定时任务开关，默认为启用
                serviceSwitchManager.checkAndUpdateSchedulerStatus(CommonStatus.ENABLE);
                log.debug("定时任务开关状态默认为启用");
            }
        } catch (Exception e) {
            log.error("更新定时任务开关状态失败: {}", e.getMessage(), e);
            // 异常情况下默认为启用，确保定时任务可用性
            serviceSwitchManager.checkAndUpdateSchedulerStatus(CommonStatus.ENABLE);
        }

    }

}
