package com.tjsj.modules.manage.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.base.CommonStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * HeartBeatTest
 *
 * <AUTHOR>
 * @date 2024/07/17
 * @description 心跳测试表
 */
@Schema(description = "心跳测试表")
@Data
@Accessors(chain = true)
@TableName(value = "tarkin.t_heart_beat_test")
@CheckCount(count = 1)
public class LocalHeartBeatTest implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 心跳测试类型
     */
    @TableField(value = "`type`")
    @Schema(description = "心跳测试类型")
    private EnvironmentTypeEnum dbType;

    /**
     * 环境配置
     */
    @TableField(value = "profile_type")
    @Schema(description = "环境配置")
    private String profileType;

    /**
     * 0正常，1异常
     */
    @TableField(value = "`status`")
    @Schema(description = "0正常，1异常")
    private CommonStatus status;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}