package com.tjsj.modules.manage.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.modules.manage.model.entity.DbTableMetaDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * DbTableMetaDataMapper
 *
 * <AUTHOR>
 * @date 2024/07/11
 * @description 数据库表信息映射器
 */
@Mapper
public interface DbTableMetaDataMapper extends BaseMapper<DbTableMetaDataDO> {


    /**
     * 根据数据库名称获取数据库中所有表的信息
     *
     * @param schemaName 数据库名称
     * @param dbType     数据库类型
     * @return List<DbTableMetaDataDO> 数据库表信息列表
     * <AUTHOR>
     * @date 2024/08/01
     */
    List<DbTableMetaDataDO> getTableBySchemaName(@Param("schemaName") String schemaName,
                                                 @Param("dbType") String dbType);

    /**
     * 批量插入数据库表信息
     *
     * @param list 数据库表信息列表
     */
    void batchInsertSchemaTableHistory(@Param("list") List<DbTableMetaDataDO> list);

    /**
     * 更新删除表的信息
     *
     * @param schemaName 数据库名称
     * <AUTHOR> Ye
     * @date 2024/07/11
     */
    void updateDeletedTable(@Param("schemaName") String schemaName);
}




