package com.tjsj.modules.manage.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/1 20:02
 * @description 数据库表详情信息返回类
 */
@Data
@Accessors(chain = true)
@Alias(value = "DbTableResVo")
@Schema(description = "数据库表详情信息返回类")
public class DbTableResVo {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "数据库名称")
    private String schemaName;

    @Schema(description = "表名称")
    private String tableName;

    @Schema(description = "表注释")
    private String tableComment;

    @Schema(description = "表状态")
    private Integer tableStatus;

    @Schema(description = "表类型")
    private String tableType;

    @Schema(description = "数据库类型")
    private String dbType;

    @Schema(description = "表总行数")
    private Long tableRows;

    @Schema(description = "表创建时间")
    private LocalDateTime tableCreateTime;

    @Schema(description = "表更新时间")
    private LocalDateTime tableUpdateTime;

}
