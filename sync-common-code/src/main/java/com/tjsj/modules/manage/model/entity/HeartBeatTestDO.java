package com.tjsj.modules.manage.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.base.CommonStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * HeartBeatTest
 *
 * <AUTHOR>
 * @date 2024/07/17
 * @description 心跳测试表
 */
@Schema(description = "心跳测试表")
@Data
@Accessors(chain = true)
@TableName(value = "tarkin.t_heart_beat_test")
@CheckCount(count = 1)
public class HeartBeatTestDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    @Schema(description = "项目id")
    private String projectId;


    /**
     * 数据库类型
     */
    @TableField(value = "`type`")
    @Schema(description = "心跳测试类型")
    private EnvironmentTypeEnum dbType;

    /**
     * 环境配置
     */
    @TableField(value = "profile_type")
    @Schema(description = "环境配置")
    private String profileType;

    /**
     * 0正常，1异常
     */
    @TableField(value = "`status`")
    @Schema(description = "0正常，1异常")
    private CommonStatus status;

    /**
     * 服务运行总开关：0-正常启用，1-停止启用
     * 用于控制服务的Http请求、Scheduler定时任务、Quartz定时任务等是否正常运行
     */
    @TableField(value = "service_switch")
    @Schema(description = "服务运行总开关：0-正常启用，1-停止启用")
    private CommonStatus serviceSwitch;

    /**
     * 定时任务总开关：0-正常启用，1-停止启用
     * 用于控制服务的Scheduler定时任务、Quartz定时任务等是否正常运行
     */
    @TableField(value = "scheduler_switch")
    @Schema(description = "定时任务总开关：0-正常启用，1-停止启用")
    private CommonStatus schedulerSwitch;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}