package com.tjsj.modules.manage.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.enums.base.CommonStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * DbTableMetaDataDO
 *
 * <AUTHOR>
 * @date 2024/08/01
 * @description 数据库存在的所有表的信息
 */
@TableName(value = "tarkin.t_db_table_metadata")
@Data
@Accessors(chain = true)
@Alias(value = "DbTableMetaDataDO")
@Schema(name = "DatabaseTableInfo对象", description = "数据库表信息")
public class DbTableMetaDataDO implements Serializable {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     *
     */
    @Schema(description = "数据库名称")
    @TableField(value = "schema_name")
    private String schemaName;

    /**
     *
     */
    @Schema(description = "表名称")
    @TableField(value = "table_name")
    private String tableName;

    /**
     *
     */
    @Schema(description = "表注释")
    @TableField(value = "table_comment")
    private String tableComment;

    /**
     * 表存在状态
     */
    @Schema(description = "表存在状态")
    @TableField(value = "delete_status")
    private CommonStatus deleteStatus;


    /**
     * 启用状态
     */
    @Schema(description = "启用状态")
    @TableField(value = "enable_status")
    private CommonStatus enableStatus;

    /**
     * 表类型
     */
    @Schema(description = "表的类型")
    @TableField(value = "table_type")
    private String tableType;


    /**
     * 数据库类型
     */
    @Schema(description = "数据库类型")
    @TableField(value = "db_type")
    private String dbType;


    @Schema(description = "表的行数")
    @TableField(value = "table_rows")
    private Integer tableRows;

    /**
     *
     */
    @Schema(description = "表创建时间")
    @TableField(value = "table_create_time")
    private LocalDateTime tableCreateTime;

    /**
     *
     */
    @Schema(description = "表更新时间")
    @TableField(value = "table_update_time")
    private LocalDateTime tableUpdateTime;

    /**
     *
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     *
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}