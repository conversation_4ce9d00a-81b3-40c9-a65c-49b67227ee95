package com.tjsj.modules.manage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.constants.ManageConsts;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.utils.string.StrUtils;
import com.tjsj.modules.manage.mapper.DbTableMetaDataMapper;
import com.tjsj.modules.manage.model.entity.DbTableMetaDataDO;
import com.tjsj.modules.manage.model.request.ManageRequest;
import com.tjsj.modules.manage.model.vo.DbTableResVo;
import com.tjsj.modules.manage.model.vo.SchemaResVO;
import com.tjsj.modules.manage.service.DbTableMetaDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * DbTableMetaDataServiceImpl
 *
 * <AUTHOR>
 * @date 2024/08/01
 * @description 针对表【database_table_history(截至2024-06-09数据库存在的所有表的信息)】的数据库操作Service实现
 */
@Service
@Slf4j
public class DbTableMetaDataServiceImpl extends ServiceImpl<DbTableMetaDataMapper, DbTableMetaDataDO>
        implements DbTableMetaDataService {

    @Override
    public void updateTableMetaData() {
        // 先取出所有用到的数据库
        List<String> schemaList = this.list(Wrappers.<DbTableMetaDataDO>lambdaQuery()
                        .eq(DbTableMetaDataDO::getDeleteStatus, CommonStatus.ENABLE)
                        .eq(DbTableMetaDataDO::getEnableStatus, CommonStatus.ENABLE)
                        .groupBy(DbTableMetaDataDO::getSchemaName))
                .stream()
                .map(DbTableMetaDataDO::getSchemaName)
                .toList();

        // 循环所有数据库
        try {
            schemaList.forEach(schema -> {
                // 查询当前数据库下所有表
                List<DbTableMetaDataDO> tableList = this.baseMapper.
                        getTableBySchemaName(schema, ManageConsts.TARKIN_6R_DB);
                // 插入新创建的表信息，更新被修改过的表信息
                if (!tableList.isEmpty()) {
                    this.baseMapper.batchInsertSchemaTableHistory(tableList);
                }
            });
            // 将数据库中已经被删除的表的信息更新
            this.baseMapper.updateDeletedTable(null);
        } catch (Exception e) {
            log.error("更新数据库表信息失败", e);
        }
    }

    @Override
    public List<SchemaResVO> queryTableMetaData(ManageRequest request) {
        // 使用 Stream 和 lambda 表达式来简化数据的处理和转换过程
        return this.list(Wrappers.<DbTableMetaDataDO>lambdaQuery()
                        .eq(request.getTableStatus() != null,
                                DbTableMetaDataDO::getDeleteStatus, request.getTableStatus())
                        .eq(DbTableMetaDataDO::getEnableStatus, CommonStatus.ENABLE)
                        /*根据表名模糊查询*/
                        .like(!StrUtils.nullOrEmpty(request.getTableName()),
                                DbTableMetaDataDO::getTableName, request.getTableName())
                )
                .stream()
                .map(dbTableMetaDataDO -> BeanUtil.copyProperties(dbTableMetaDataDO, DbTableResVo.class))
                .collect(Collectors.groupingBy(DbTableResVo::getSchemaName))
                .entrySet().stream()
                .map(entry -> new SchemaResVO()
                        .setSchemaName(entry.getKey())
                        .setTableList(entry.getValue()))
                .collect(Collectors.toList());
    }

}




