package com.tjsj.common.injector;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;
import org.apache.ibatis.session.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/8 17:22
 * @description MybatisPlus自定义SQL注入器
 */
public class MySqlInjector extends DefaultSqlInjector {


    /**
     * 获取方法列表
     *
     * @param mapperClass 映射类
     * @return {@link List }<{@link AbstractMethod }>
     * <AUTHOR>
     * @date 2024/07/08
     */
    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass, tableInfo);
        methodList.add(new InsertBatchSomeColumn(tableFieldInfo ->
                tableFieldInfo.getFieldFill() != FieldFill.INSERT_UPDATE));
        return methodList;
    }
}
