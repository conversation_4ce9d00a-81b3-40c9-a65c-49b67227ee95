package com.tjsj.common.config.interceptor;

import com.tjsj.common.config.converter.enums.EnumConverterFactory;
import com.tjsj.common.multirequestbody.MultiRequestBodyArgumentResolver;
import com.tjsj.interceptor.GeneralAuthInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.List;


/**
 * HttpInterceptorConfig
 *
 * <AUTHOR> Ye
 * @date 2024/08/06
 * @description http拦截器配置
 */
@Configuration
public class HttpInterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/images/**").addResourceLocations("classpath:/static/");
    }

//    @Override
//    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
//        configurer.enable();
//    }

    /**
     * 添加拦截器
     *
     * @param registry 注册表
     * <AUTHOR> Ye
     * @date 2024/07/13
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        String[] excludes = new String[]{"/*.html", "/document/**", "/ssl/**"};
        registry.addInterceptor(authenticationInterceptor())
                .excludePathPatterns(excludes)
                .excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**",
                        "/images/**");
    }

    /**
     * 添加自定义格式化器
     * <p>
     * 该方法用于向 Spring 的 {@link FormatterRegistry} 添加自定义的格式化器或转换器，
     * 以支持在控制器方法中使用自定义类型的请求参数或路径变量。通过注册 {@link EnumConverterFactory}，
     * 可以使枚举类型与字符串之间进行自动转换。
     * </p>
     *
     * @param registry 格式化器注册表，用于注册自定义格式化器和转换器
     * <AUTHOR> Ye
     * @date 2024/08/15
     */
    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverterFactory(new EnumConverterFactory());
    }



    /**
     * 注册拦截器
     *
     * @return {@link GeneralAuthInterceptor }
     * <AUTHOR> Ye
     * @date 2024/08/06
     */
    @Bean
    public GeneralAuthInterceptor authenticationInterceptor() {
        return new GeneralAuthInterceptor();
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowCredentials(false).allowedHeaders("*").allowedOrigins("*").allowedMethods("*");
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(responseBodyConverter());
    }

    @Bean
    public StringHttpMessageConverter responseBodyConverter() {
        //解决返回值中文乱码
        return new StringHttpMessageConverter(StandardCharsets.UTF_8);
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        // 添加MultiRequestBody参数解析器
        argumentResolvers.add(new MultiRequestBodyArgumentResolver());
    }


}