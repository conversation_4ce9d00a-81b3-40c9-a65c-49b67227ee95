package com.tjsj.common.config.converter.serialization;

import cn.hutool.core.net.URLDecoder;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.reader.ObjectReader;
import io.swagger.v3.oas.annotations.media.Schema;
import org.slf4j.Logger;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * StringDeserializer
 *
 * <AUTHOR>
 * @date 2024/8/30 18:07
 * @description 字符串反序列化器
 */
@Schema(name = "StringDeserializer", description = "字符串反序列化器")
public class StringDeserializer implements ObjectReader<String> {

    public static final StringDeserializer INSTANCE = new StringDeserializer();


    private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(StringDeserializer.class);

    @Override
    public String readObject(JSONReader jsonReader, Type type, Object o, long l) {
        // 从 JSONReader 读取字符串
        String encodedString = jsonReader.readString();

        if (encodedString == null) {
            return null;
        }

        try {
            // 使用 UTF-8 解码 URL 编码的字符串
            return URLDecoder.decode(encodedString, StandardCharsets.UTF_8);
        } catch (Exception e) {
            // 如果解码失败，打印错误并返回原始字符串
            LOGGER.error("Failed to decode URL-encoded string: " + encodedString, e);
            return encodedString;
        }
    }

}
