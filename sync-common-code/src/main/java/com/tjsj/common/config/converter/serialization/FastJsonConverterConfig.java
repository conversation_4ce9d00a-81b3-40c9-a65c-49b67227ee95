package com.tjsj.common.config.converter.serialization;

import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring.http.converter.FastJsonHttpMessageConverter;
import com.alibaba.fastjson2.support.spring.webservlet.view.FastJsonJsonView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.ViewResolverRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * FastJsonConverterConfig
 *
 * <p>
 * 通过实现 {@link WebMvcConfigurer} 接口，定制 Spring MVC 的消息转换行为。
 * 该配置类将 FastJson2 设置为默认的 JSON 处理库，并定制了序列化配置。
 * </p>
 *
 * <AUTHOR> Ye
 * @date 2024/08/14
 * @description 配置 FastJson2 作为 Spring MVC 的消息转换器，支持定制 JSON 的序列化和反序列化行为。
 */
@Configuration
@EnableWebMvc
public class FastJsonConverterConfig implements WebMvcConfigurer {

    private static final Logger LOGGER = LoggerFactory.getLogger(FastJsonConverterConfig.class);

    /*@Resource
    private RequestMappingHandlerAdapter requestMappingHandlerAdapter;

    @PostConstruct
    public void init() {
        List<HttpMessageConverter<?>> messageConverters = requestMappingHandlerAdapter.getMessageConverters();
        //messageConverters.forEach(converter -> LOGGER.error("message converter: {}", converter.getClass().getName()));
    }*/

    /**
     * 配置消息转换器，使用 FastJson2 处理 JSON 数据。
     *
     * @param converters 消息转换器列表
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {

        // 创建 FastJson 的消息转换器
        FastJsonHttpMessageConverter converter = new FastJsonHttpMessageConverter();

        // 配置 FastJsonConfig，定制 JSON 的读写行为
        FastJsonConfig config = new FastJsonConfig();
        config.setReaderFeatures(JSONReader.Feature.FieldBased, JSONReader.Feature.SupportArrayToBean);
        config.setWriterFeatures(JSONWriter.Feature.WriteMapNullValue, JSONWriter.Feature.PrettyFormat,
                JSONWriter.Feature.WriteNullListAsEmpty);

        // 设置字符集和支持的媒体类型
        converter.setFastJsonConfig(config);
        converter.setDefaultCharset(StandardCharsets.UTF_8);
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_ATOM_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedMediaTypes.add(MediaType.APPLICATION_PDF);
        supportedMediaTypes.add(MediaType.APPLICATION_RSS_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XHTML_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XML);
        supportedMediaTypes.add(MediaType.IMAGE_GIF);
        supportedMediaTypes.add(MediaType.IMAGE_JPEG);
        supportedMediaTypes.add(MediaType.IMAGE_PNG);
        supportedMediaTypes.add(MediaType.TEXT_EVENT_STREAM);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        supportedMediaTypes.add(MediaType.TEXT_MARKDOWN);
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        supportedMediaTypes.add(MediaType.TEXT_XML);
        converter.setSupportedMediaTypes(supportedMediaTypes);

        // 添加到转换器列表中，并优先使用
        converters.add(0,converter);
    }

    /**
     * 配置视图解析器，支持将视图内容渲染为 JSON 格式。
     *
     * @param registry 视图解析器注册器
     */
    @Override
    public void configureViewResolvers(ViewResolverRegistry registry) {
        // 创建 FastJson 的视图解析器
        FastJsonJsonView fastJsonJsonView = new FastJsonJsonView();

        // 可以在此处添加自定义的 FastJsonConfig 配置
        // FastJsonConfig config = new FastJsonConfig();
        // fastJsonJsonView.setFastJsonConfig(config);

        // 启用内容协商视图解析
        registry.enableContentNegotiation(fastJsonJsonView);
    }

}

