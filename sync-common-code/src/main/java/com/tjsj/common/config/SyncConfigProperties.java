package com.tjsj.common.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 数据同步配置属性
 * 
 * <p>映射application.yml中sync相关的配置项，提供统一的配置管理。</p>
 * 
 * <h3>🎯 配置结构</h3>
 * <ul>
 *   <li><strong>auto</strong>：自动同步相关配置</li>
 *   <li><strong>financial-table-fix</strong>：财务表修复同步配置</li>
 * </ul>
 * 
 * <h3>📋 使用示例</h3>
 * <pre>{@code
 * @Autowired
 * private SyncConfigProperties syncConfig;
 * 
 * // 获取自动同步配置
 * int pullBatchSize = syncConfig.getAuto().getPullBatchSize();
 * int insertBatchSize = syncConfig.getAuto().getInsertBatchSize();
 * 
 * // 获取财务表修复配置
 * int fixPullBatchSize = syncConfig.getFinancialTableFix().getPullBatchSize();
 * }</pre>
 * 
 * <AUTHOR> Ye
 * @version 1.0.0
 * @since 2025-08-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "sync")
public class SyncConfigProperties {

    /**
     * 自动同步配置
     */
    private AutoSyncConfig auto = new AutoSyncConfig();

    /**
     * 财务表修复同步配置
     */
    private FinancialTableFixConfig financialTableFix = new FinancialTableFixConfig();

    /**
     * AutoSyncConfig
     *
     * @description 对应配置文件中sync.auto的配置项
     * <AUTHOR> Ye
     * @date 2025/08/01
     * @version 1.0.0
     * @description 自动同步配置类
     */
    @Data
    public static class AutoSyncConfig {

        /**
         * 每批拉取数据量
         * <p>默认值：10000条</p>
         * @description 对应配置文件中sync.auto.pull-batch-size的配置项
         */
        @Schema(description = "每批拉取数据量")
        private int pullBatchSize = 10000;

        /**
         * 每批插入数据量
         * <p>默认值：10000条</p>
         * @description 对应配置文件中sync.auto.insert-batch-size的配置项
         */
        @Schema(description = "每批插入数据量")
        private int insertBatchSize = 10000;

        /**
         * 重试间隔时间（毫秒）
         * <p>默认值：2000毫秒</p>
         * @description 对应配置文件中sync.auto.retry-delay-millis的配置项
         */
        @Schema(description = "重试间隔时间（毫秒）")
        private long retryDelayMillis = 2000L;

        /**
         * 最大重试次数
         * <p>默认值：2次</p>
         * @description 对应配置文件中sync.auto.max-retry-times的配置项
         */
        @Schema(description = "最大重试次数")
        private int maxRetryTimes = 2;
    }

    /**
     * 财务表修复同步配置类
     */
    @Data
    public static class FinancialTableFixConfig {

        /**
         * 每批拉取数据量
         * <p>默认值：10000条</p>
         * @description 对应配置文件中sync.financial-table-fix.pull-batch-size的配置项
         */
        @Schema(description = "每批拉取数据量")
        private int pullBatchSize = 10000;

        /**
         * 每批插入数据量
         * <p>默认值：10000条</p>
         * @description 对应配置文件中sync.financial-table-fix.insert-batch-size的配置项
         */
        @Schema(description = "每批插入数据量")
        private int insertBatchSize = 10000;
    }

    // ═══════════════════════════════════════════════════════════════════════════════════════
    // 🔧 便捷方法
    // ═══════════════════════════════════════════════════════════════════════════════════════

    /**
     * 获取自动同步的拉取批次大小
     * 
     * @return 拉取批次大小
     */
    public int getAutoPullBatchSize() {
        return auto.getPullBatchSize();
    }

    /**
     * 获取自动同步的插入批次大小
     * 
     * @return 插入批次大小
     */
    public int getAutoInsertBatchSize() {
        return auto.getInsertBatchSize();
    }

    /**
     * 获取自动同步的重试延迟时间
     * 
     * @return 重试延迟时间（毫秒）
     */
    public long getAutoRetryDelayMillis() {
        return auto.getRetryDelayMillis();
    }

    /**
     * 获取自动同步的最大重试次数
     * 
     * @return 最大重试次数
     */
    public int getAutoMaxRetryTimes() {
        return auto.getMaxRetryTimes();
    }

    /**
     * 获取财务表修复的拉取批次大小
     * 
     * @return 拉取批次大小
     */
    public int getFinancialFixPullBatchSize() {
        return financialTableFix.getPullBatchSize();
    }

    /**
     * 获取财务表修复的插入批次大小
     * 
     * @return 插入批次大小
     */
    public int getFinancialFixInsertBatchSize() {
        return financialTableFix.getInsertBatchSize();
    }

}
