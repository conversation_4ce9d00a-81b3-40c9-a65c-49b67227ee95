package com.tjsj.common.config.converter.enums;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.tjsj.common.annotation.ExcelDisplayType;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.base.EnumDisplayType;

/**
 * CommonStatusConverter
 *
 * <AUTHOR>
 * @date 2024/08/15
 * @description 常见状态enum转换器x
 */
public class CommonStatusConverter implements Converter<CommonStatus> {

    @Override
    public WriteCellData<?> convertToExcelData(CommonStatus value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        String displayValue = value == null ? "" : value.getCode().toString();
        // 获取字段上的注解
        ExcelDisplayType displayName = contentProperty.getField().getAnnotation(ExcelDisplayType.class);
        // 如果有注解，进行相应处理
        if (displayName != null && value != null) {
            if (displayName.value().equals(EnumDisplayType.MARGIN_TRADING)) {
                displayValue = value.equals(CommonStatus.ENABLE) ? "是" : "否";
            }
        }
        // 如果没有注解，使用枚举的描述字段
        return new WriteCellData<>(displayValue);
    }

}


