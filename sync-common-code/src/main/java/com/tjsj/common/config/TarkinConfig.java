package com.tjsj.common.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * Tarkin项目自定义配置类
 *
 * <p>该配置类用于管理Tarkin项目的全局配置信息，包括环境标识、自动更新设置和定时任务调度配置。</p>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0 (2025/07/28)
 */
@ConfigurationProperties(prefix = "tarkin")
@Data
@Component
@Slf4j
@Schema(description = "Tarkin项目自定义配置")
public class TarkinConfig {

    /**
     * 项目id
     */
    @Schema(description = "项目id", example = "tarkin_xyyw")
    private String projectId;

    /**
     * 项目运行环境标识
     *
     *
     * @see com.tjsj.common.enums.EnvironmentTypeEnum
     */
    @Schema(description = "项目运行环境标识", example = "tarkin")
    private String env;

    /**
     * 券商本地数据库类型
     *
     * @see com.tjsj.common.enums.EnvironmentTypeEnum
     */
    @Schema(description = "券商本地数据库类型", example = "xyzq")
    private String dbType;

    /**
     * 项目环境配置
     *
     * @see com.tjsj.common.enums.base.ProfileTypeEnum
     */
    @Schema(description = "项目环境配置", example = "dev")
    private String profile;


    /**
     * Quartz定时任务配置
     *
     * <p>映射yml配置中的 tarkin.quartz 节点</p>
     */
    @Schema(description = "Quartz定时任务配置")
    private Quartz quartz = new Quartz();

    /**
     * 定时任务调度配置
     *
     * <p>映射yml配置中的 tarkin.scheduler 节点</p>
     */
    @Schema(description = "定时任务调度配置")
    private Scheduler scheduler = new Scheduler();


    /**
     * 定时任务调度配置内部类
     */
    @Data
    @Schema(description = "Quartz定时任务调度配置")
    public static class Quartz {

        /**
         * 是否启用定时任务调度
         *
         * <p>控制系统是否启用定时任务调度功能，包括数据同步、缓存刷新等功能。</p>
         *
         * <ul>
         *   <li>true - 开启定时任务调度（默认）</li>
         *   <li>false - 关闭定时任务调度</li>
         * </ul>
         */
        @Schema(description = "是否启用Quartz定时任务调度", example = "true")
        private boolean enabled = true;

    }


    /**
     * 定时任务调度配置内部类
     */
    @Data
    @Schema(description = "定时任务调度配置")
    public static class Scheduler {

        /**
         * 是否启用定时任务调度
         *
         * <p>控制系统是否启用定时任务调度功能，包括数据同步、缓存刷新等功能。</p>
         *
         * <ul>
         *   <li>true - 开启定时任务调度（默认）</li>
         *   <li>false - 关闭定时任务调度</li>
         * </ul>
         */
        @Schema(description = "是否启用定时任务调度", example = "true")
        private boolean enabled = true;

    }


    /**
     * 配置初始化
     *
     * <p>在配置加载完成后执行初始化逻辑，输出配置摘要信息。</p>
     */
    @PostConstruct
    public void init() {
        log.info("🔧 ==================== Tarkin配置初始化开始 ====================");

        try {
            // 输出配置摘要
            logConfigurationSummary();


            log.info("✅ ==================== Tarkin配置初始化完成 ====================");

        } catch (Exception e) {
            log.error("❌ ==================== Tarkin配置初始化失败 ====================");
            log.error("💥 异常信息: {}", e.getMessage(), e);


            log.warn("🔄 已应用默认配置，系统将继续运行");
            log.error("❌ ========================================================");
        }
    }

    /**
     * 输出配置摘要信息
     */
    private void logConfigurationSummary() {
        log.info("📊 Tarkin配置摘要:");
        log.info("   🆔 项目ID: {}", projectId != null ? projectId : "未配置");
        log.info("   🌍 运行环境: {}", getEnvironmentDisplay());
        log.info("   🗄️ 数据库类型: {}", dbType != null ? dbType : "未配置");
        log.info("   🏷️ 环境配置: {}", profile != null ? profile : "未配置");
        log.info("   ⏰ Quartz定时任务: {}", quartz != null && quartz.enabled ? "开启" : "关闭");
        log.info("   📅 调度器: {}", scheduler != null && scheduler.enabled ? "开启" : "关闭");
    }


    /**
     * 获取环境显示名称
     *
     * @return 格式化的环境名称
     */
    private String getEnvironmentDisplay() {
        if (env != null && !env.trim().isEmpty()) {
            return env.trim().toUpperCase();
        }
        return "未配置";
    }

}
