package com.tjsj.config;


import org.aspectj.lang.annotation.Aspect;
import org.springframework.aop.Advisor;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.interceptor.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * TransactionAdviceConfig
 *
 * <AUTHOR>
 * @date 2024/08/06
 * @description 交易建议配置类，配置全局事务管理
 */
@Aspect
@Configuration
public class TransactionAdviceConfig {

    /**
     * AOP的切入点表达式，用于匹配需要进行事务管理的方法
     */
    //private static final String AOP_POINTCUT_EXPRESSION = "execution(* com.tjsj.**.modules.**.controller.*.*(..))";

    private static final String AOP_POINTCUT_EXPRESSION = "execution(* com.nonexistentpackage.nonexistentclass..*(..))";


    @Resource
    private PlatformTransactionManager platformTransactionManager;

    /**
     * 从配置文件中读取事务的超时时间，默认为5000毫秒
     */
    @Value(value = "${transaction.timeout:5000}")
    private int txMethodTimeout;

    /**
     * 配置事务拦截器
     *
     * @return TransactionInterceptor 事务拦截器
     */
    @Bean
    public TransactionInterceptor txAdvice() {
        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();
        Map<String, TransactionAttribute> txMap = getTransactionAttributeMap();

        // 将配置的事务属性映射到方法名上
        source.setNameMap(txMap);
        return new TransactionInterceptor(platformTransactionManager, source);
    }

    /**
     * 定义Advisor，应用AOP切面
     *
     * @return Advisor 事务切面Advisor
     */
    @Bean
    public Advisor txAdviceAdvisor() {
        AspectJExpressionPointcut pointcut = new AspectJExpressionPointcut();

        // 设置切入点表达式
        pointcut.setExpression(AOP_POINTCUT_EXPRESSION);
        // 将事务拦截器绑定到切入点
        return new DefaultPointcutAdvisor(pointcut, txAdvice());
    }

    /**
     * 定义事务属性的映射规则
     *
     * @return Map<String, TransactionAttribute> 事务属性的映射
     */
    private Map<String, TransactionAttribute> getTransactionAttributeMap() {
        // 定义只读事务属性，不涉及更新操作的事务
        RuleBasedTransactionAttribute readOnlyTx = new RuleBasedTransactionAttribute();
        readOnlyTx.setReadOnly(true);
        readOnlyTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_NOT_SUPPORTED);

        // 定义必须的事务属性，涉及数据修改的事务
        RuleBasedTransactionAttribute requiredTx = new RuleBasedTransactionAttribute();
        requiredTx.setRollbackRules(Collections.singletonList(new RollbackRuleAttribute(Exception.class)));
        requiredTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        // 设置事务超时时间
        requiredTx.setTimeout(txMethodTimeout);

        Map<String, TransactionAttribute> txMap = new HashMap<>();
        // 针对不同前缀的方法名定义事务属性
        String[] requiredMethods = {"save*", "remove*", "update*", "batch*", "clear*", "add*", "append*",
                "modify*", "edit*", "insert*", "delete*", "do*", "create*"};
        for (String method : requiredMethods) {
            // 这些方法需要事务管理
            txMap.put(method, requiredTx);
        }

        String[] readOnlyMethods = {"select*", "get*", "valid*", "list*", "count*", "find*", "load*", "search*",
                "tree*", "routes*"};
        for (String method : readOnlyMethods) {
            // 这些方法设置为只读事务
            txMap.put(method, readOnlyTx);
        }

        // 默认规则，所有方法都使用requiredTx事务属性
        txMap.put("*", requiredTx);

        return txMap;
    }

}
