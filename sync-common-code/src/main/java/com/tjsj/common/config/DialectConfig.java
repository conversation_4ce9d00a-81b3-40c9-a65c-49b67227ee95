package com.tjsj.common.config;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/18 14:22
 * @description
 */
//@Configuration
//public class DialectConfig {
//
//    // 手动指定 MySQL 方言
//    @Bean
//    public Dialect jdbcDialect() {
//        return MySqlDialect.INSTANCE;
//    }
//
//    // 手动创建 JdbcTemplate 并关联主数据源
//    @Bean
//    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
//        return new JdbcTemplate(dataSource);
//    }
//}
