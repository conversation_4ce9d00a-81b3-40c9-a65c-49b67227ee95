package com.tjsj.common.config.converter.enums;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.tjsj.common.enums.base.SecTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * SecTypeConverter
 *
 * <AUTHOR>
 * @date 2024/8/15 16:04
 * @description 证券类型枚举类转换器
 */
@Schema(name = "SecTypeConverter", description = "证券类型枚举类转换器")
public class SecTypeConverter implements Converter<SecTypeEnum> {

    @Override
    public WriteCellData<?> convertToExcelData(SecTypeEnum value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(value.getDescription());
    }

}


