package com.tjsj.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * DistributedLockConfig
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/06/23
 * @description 分布式锁配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "tarkin.distributed-lock")
public class DistributedLockConfig {

    /**
     * 是否启用分布式锁，默认true
     */
    private boolean enabled = true;

    /**
     * Redis 健康检查间隔（毫秒）
     */
    private long healthCheckInterval = 5000L;

    /**
     * 是否在 Redis 不可用时使用本地锁降级
     */
    private boolean enableLocalLockFallback = true;

    /**
     * 是否启用数据库锁降级
     */
    private boolean enableDatabaseLockFallback = true;

    /**
     * 数据库锁降级优先级（true: 优先使用数据库锁，false: 优先使用本地锁）
     */
    private boolean databaseLockPriority = true;


}
