package com.tjsj.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.google.common.base.CaseFormat;
import com.tjsj.common.handler.UpdateRelatedFieldsMetaHandler;
import com.tjsj.common.injector.MySqlInjector;
import com.tjsj.common.utils.string.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.wrapper.MapWrapper;
import org.apache.ibatis.reflection.wrapper.ObjectWrapper;
import org.apache.ibatis.reflection.wrapper.ObjectWrapperFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * MybatisPlusConfig
 *
 * <AUTHOR> Ye
 * @date 2024/07/08
 * @description mybatis-plus配置
 */
@Configuration
public class MybatisPlusConfig {
    private static final String UPPER_CASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    /**
     * 新的分页插件,一缓和二缓遵循mybatis的规则,需要设置 MybatisConfiguration#useDeprecatedExecutor = false 避免缓存出现问题
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    /**
     * 我sql注入器
     *
     * @return {@link MySqlInjector }
     * <AUTHOR> Ye
     * @date 2024/07/08
     */
    @Bean
    public MySqlInjector mySqlInjector() {
        return new MySqlInjector();
    }

    /**
     * 更新相关字段元处理程序
     */
    @Autowired
    private UpdateRelatedFieldsMetaHandler updateRelatedFieldsMetaHandler;

    /**
     * 全局配置
     *
     * @return {@link GlobalConfig }
     * <AUTHOR> Ye
     * @date 2024/07/08
     */
    @Bean
    public GlobalConfig globalConfig() {
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.setMetaObjectHandler(updateRelatedFieldsMetaHandler);
        return globalConfig;
    }

    @Bean
    public ConfigurationCustomizer mybatisConfigurationCustomizer() {
        return configuration -> configuration.setObjectWrapperFactory(new MapWrapperFactory());
    }

    /**
     * CustomWrapper
     *
     * <AUTHOR> Ye
     * @date 2024/07/08
     * @description 自定义包装器
     */
    private static class CustomWrapper extends MapWrapper {
        public CustomWrapper(MetaObject metaObject, Map<String, Object> map) {
            super(metaObject, map);
        }

        @Override
        public String findProperty(String name, boolean useCamelCaseMapping) {
            if (useCamelCaseMapping) {
                if (StringUtils.isNotBlank(name)) {
                    if (UPPER_CASE.contains(name.substring(0, 1))) {
                        if (name.contains("_")) {

                            return CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, name);
                        } else {
                            return name.toLowerCase();
                        }
                    } else {
                        if (name.contains("_")) {
                            return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, name);
                        }
                    }
                }
            }
            return name;
        }
    }

    /**
     * MapWrapperFactory
     *
     * <AUTHOR> Ye
     * @date 2024/07/08
     * @description map包装物工厂
     */
    private static class MapWrapperFactory implements ObjectWrapperFactory {
        @Override
        public boolean hasWrapperFor(Object object) {
            return object instanceof Map;
        }

        @Override
        public ObjectWrapper getWrapperFor(MetaObject metaObject, Object object) {
            return new CustomWrapper(metaObject, (Map) object);
        }
    }
}
