package com.tjsj.common.config;

import org.springframework.context.annotation.Configuration;

/**
 * SwaggerConfig
 *
 * <AUTHOR>
 * @date 2024/07/12
 * @description swagger配置
 */
@Configuration
public class SwaggerConfig {

//    @Bean
//    public OpenAPI customOpenAPI() {
//        // 定义全局请求参数
//        Parameter globalParameter = new Parameter()
//                .in(ParameterIn.HEADER.toString())
//                .schema(new Schema().type("string"))
//                .name("token")
//                .description("token")
//                .required(true);
//
//        return new OpenAPI()
//                .components(new Components().addParameters("token", globalParameter))
//                .info(new Info()
//                        .title("塔金数据平台")
//                        .description("接口文档 (注意：访问链接部分的 *-service 部分可替换为项目名称)")
//                        .version("1.0")
//                        .contact(new Contact()
//                                .name("<PERSON><PERSON><PERSON>")
//                                .url("http://example.com")
//                                .email("<EMAIL>")));
//    }

}
