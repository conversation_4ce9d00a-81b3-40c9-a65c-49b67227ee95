package com.tjsj.common.config.converter.enums;

import com.tjsj.common.enums.BaseEnum;
import com.tjsj.common.utils.string.StringUtils;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;
import org.springframework.stereotype.Component;

/**
 * 字符串转枚举工厂类
 *
 * <AUTHOR> Ye
 * @date 2024/8/6 21:05
 * @description 字符串转枚举工厂类
 */
@Component
public class EnumConverterFactory implements ConverterFactory<String, BaseEnum> {

    /**
     * 返回一个将 {@link String} 转换为继承了 {@link BaseEnum} 接口的枚举类的 {@link Converter} 实例。
     * 当需要将字符串转换为特定的枚举类型时，将调用此方法进行转换。
     *
     * @param targetType 枚举的目标类型，即要转换到的枚举类型的 {@link Class} 对象。
     * @param <T>        枚举类型，必须继承自 {@link BaseEnum}。
     * @return {@link Converter}<{@link String}, {@link T}> 一个 {@link Converter} 实例，用于将字符串转换为指定的枚举类型。
     * <AUTHOR> Ye
     * @date 2024/08/06
     */
    @Override
    public <T extends BaseEnum> Converter<String, T> getConverter(Class<T> targetType) {
        return new StringToEnumConverter<>(targetType);
    }


    /**
     * 内部类 {@code StringToEnumConverter} 实现了 {@link Converter} 接口，
     * 用于将 {@link String} 类型转换为继承了 {@link BaseEnum} 接口的枚举类型。
     *
     * @param <T> 枚举类型，必须继承自 {@link BaseEnum}。
     */
    private static class StringToEnumConverter<T extends BaseEnum> implements Converter<String, T> {

        private final Class<T> enumType;

        /**
         * 构造方法，接受枚举类型的 {@link Class} 对象。
         *
         * @param enumType 枚举的目标类型，即要转换到的枚举类型的 {@link Class} 对象。
         */
        public StringToEnumConverter(Class<T> enumType) {
            this.enumType = enumType;
        }

        /**
         * 将 {@link String} 类型转换为指定的枚举类型。
         * 如果找不到对应的枚举值，将抛出异常。
         *
         * @param source 要转换的字符串。
         * @return 转换后的枚举类型对象。
         */
        @Override
        public T convert(String source) {
            return BaseEnum.getEnum(enumType, StringUtils.trim(source), true);
        }
    }

}

