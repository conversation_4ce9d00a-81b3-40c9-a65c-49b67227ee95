package com.tjsj.common.config.converter.serialization.backup;

/**
 * <AUTHOR>
 * @date 2024/8/23 23:58
 * @description Jackson converter config
 */
/*
@Configuration
@EnableWebMvc
@Profile("!prod,!dev")
public class JacksonConverterConfig implements WebMvcConfigurer {

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        //objectMapper.registerModule(new JavaTimeModule());

        // 美化输出
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);

        // 序列化枚举使用 toString 方法
        objectMapper.configure(SerializationFeature.WRITE_ENUMS_USING_TO_STRING, true);

        // 忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 允许空对象转换为 null -> false
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        // 允许单值转换为数组
        objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);


        // 你可以通过这个配置来排除 null 值的字段，从而生成更紧凑的 JSON 输出。仅序列化非空的字段。
        //objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        return objectMapper;

    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // 创建 Jackson 的消息转换器
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter(objectMapper());

        // 设置字符集和支持的媒体类型
        converter.setDefaultCharset(StandardCharsets.UTF_8);
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_ATOM_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedMediaTypes.add(MediaType.APPLICATION_PDF);
        supportedMediaTypes.add(MediaType.APPLICATION_RSS_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XHTML_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XML);
        supportedMediaTypes.add(MediaType.IMAGE_GIF);
        supportedMediaTypes.add(MediaType.IMAGE_JPEG);
        supportedMediaTypes.add(MediaType.IMAGE_PNG);
        supportedMediaTypes.add(MediaType.TEXT_EVENT_STREAM);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        supportedMediaTypes.add(MediaType.TEXT_MARKDOWN);
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        supportedMediaTypes.add(MediaType.TEXT_XML);
        converter.setSupportedMediaTypes(supportedMediaTypes);

        // 添加到转换器列表中，并优先使用
        //converters.add(0, converter);
    }

    */
/**
     * 配置视图解析器，支持将视图内容渲染为 JSON 格式。
     *
     * @param registry 视图解析器注册器
     *//*

    @Override
    public void configureViewResolvers(ViewResolverRegistry registry) {
        // 创建 FastJson 的视图解析器
        FastJsonJsonView fastJsonJsonView = new FastJsonJsonView();

        // 可以在此处添加自定义的 FastJsonConfig 配置
        // FastJsonConfig config = new FastJsonConfig();
        // fastJsonJsonView.setFastJsonConfig(config);

        // 启用内容协商视图解析
        registry.enableContentNegotiation(fastJsonJsonView);
    }
}
*/
