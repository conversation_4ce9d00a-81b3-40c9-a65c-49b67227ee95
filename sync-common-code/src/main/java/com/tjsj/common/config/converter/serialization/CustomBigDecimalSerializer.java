package com.tjsj.common.config.converter.serialization;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.writer.ObjectWriter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.slf4j.Logger;

import java.lang.reflect.Type;
import java.math.BigDecimal;

/**
 * CustomBigDecimalSerializer
 *
 * <AUTHOR>
 * @date 2024/8/25 13:58
 * @description 自定义 BigDecimal 序列化器
 */
@Schema(name = "CustomBigDecimalSerializer", description = "自定义 BigDecimal 序列化器")
public class CustomBigDecimalSerializer implements ObjectWriter<BigDecimal> {

    private static final Logger LOGGER = org.slf4j.LoggerFactory.getLogger(CustomBigDecimalSerializer.class);

    /**
     * 序列化 BigDecimal 对象
     * 如果 BigDecimal 对象的小数点后全为零，则显示整数部分；否则正常显示
     *
     * @param jsonWriter JSONWriter 对象
     * @param o          BigDecimal 对象
     * @param o1         字段名称
     * @param type       字段类型
     * @param l          字段序号
     * <AUTHOR>
     * @date 2024/08/25
     */
    @Override
    public void write(JSONWriter jsonWriter, Object o, Object o1, Type type, long l) {
        if (o instanceof BigDecimal) {
            BigDecimal value = (BigDecimal) o;
            if (value.scale() > 0 && value.stripTrailingZeros().scale() <= 0) {
                // 如果小数点后全为零，显示整数部分
                jsonWriter.writeRaw(value.stripTrailingZeros().toPlainString());
            } else {
                // 否则正常显示
                jsonWriter.writeRaw(value.toPlainString());
            }
        } else {
            jsonWriter.writeAny(o);
        }
    }
}
