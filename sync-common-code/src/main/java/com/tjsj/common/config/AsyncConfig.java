package com.tjsj.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 异步任务线程池配置类
 *
 * <AUTHOR> Ye
 * @date 2024/12/19 12:34
 * @version 1.0.0
 * @description 专门为异步任务（@Async注解）提供线程池配置，主要服务于数据同步项目中的异步业务场景
 *
 * <h3>🎯 配置目的</h3>
 * <ul>
 *   <li><strong>资源隔离</strong>：为异步任务提供专用线程池，避免与其他业务线程池冲突</li>
 *   <li><strong>性能优化</strong>：针对数据同步等IO密集型任务优化线程池参数</li>
 *   <li><strong>监控管理</strong>：通过独立的线程池便于监控和调优异步任务性能</li>
 * </ul>
 *
 * <h3>🔄 与其他线程池的区别</h3>
 * <ul>
 *   <li><strong>asyncExecutor</strong>：专用于@Async注解的异步方法执行（如数据同步、定时任务异步化）</li>
 *   <li><strong>taskExecutor</strong>：通用任务执行器，用于一般的异步任务处理</li>
 * </ul>
 *
 * <h3>🚀 主要应用场景</h3>
 * <ul>
 *   <li>数据同步任务的异步执行（DatabaseSyncController.syncCloudToLocal）</li>
 *   <li>定时任务的异步化处理，避免阻塞定时任务调度器</li>
 *   <li>HTTP接口的异步响应，提升用户体验</li>
 *   <li>长时间运行的业务逻辑异步化</li>
 * </ul>
 *
 * <h3>⚙️ 配置说明</h3>
 * <p>可通过 <code>async.pool.size</code> 配置项调整核心线程数，默认为5个线程</p>
 *
 * @see com.tjsj.sync.modules.sync.controller.DatabaseSyncController#syncCloudToLocal()
 * @see org.springframework.scheduling.annotation.Async
 */
@Configuration
public class AsyncConfig {

    /**
     * 异步线程池核心线程数配置
     *
     * <p>从配置文件中读取 async.pool.size 参数，如果未配置则默认使用 5 个核心线程</p>
     *
     * <h4>🎯 默认值选择依据：</h4>
     * <ul>
     *   <li><strong>IO密集型任务</strong>：数据同步主要是数据库IO操作，5个线程可以充分利用IO等待时间</li>
     *   <li><strong>内存考虑</strong>：每个线程占用一定内存，5个线程在资源消耗和性能之间取得平衡</li>
     *   <li><strong>并发控制</strong>：避免过多并发导致数据库连接池耗尽或锁竞争</li>
     * </ul>
     *
     * <h4>📊 调优建议：</h4>
     * <ul>
     *   <li>CPU密集型业务：建议设置为 CPU核心数 + 1</li>
     *   <li>IO密集型业务：建议设置为 CPU核心数 * 2</li>
     *   <li>混合型业务：建议根据实际压测结果调整</li>
     * </ul>
     */
    @Value("${async.pool.size:5}")
    private int asyncPoolSize;

    /**
     * 创建异步任务专用线程池执行器
     *
     * <p>该线程池专门服务于标注了 @Async("asyncExecutor") 的异步方法，
     * 主要用于数据同步、定时任务异步化等场景</p>
     *
     * <h4>🔧 线程池参数说明：</h4>
     * <ul>
     *   <li><strong>核心线程数</strong>：{@code asyncPoolSize}（默认5）- 常驻线程数，处理日常异步任务</li>
     *   <li><strong>最大线程数</strong>：10 - 高峰期最多创建10个线程，避免资源过度消耗</li>
     *   <li><strong>队列容量</strong>：100 - 缓冲队列，当核心线程忙碌时暂存任务</li>
     *   <li><strong>线程名前缀</strong>：AsyncExecutor- - 便于日志追踪和问题排查</li>
     * </ul>
     *
     * <h4>🎯 设计理念：</h4>
     * <ul>
     *   <li><strong>稳定优先</strong>：核心线程数不会回收，保证基础处理能力</li>
     *   <li><strong>弹性扩展</strong>：高峰期可扩展到10个线程，应对突发流量</li>
     *   <li><strong>内存保护</strong>：队列容量限制避免内存溢出</li>
     *   <li><strong>可观测性</strong>：统一线程命名便于监控和调试</li>
     * </ul>
     *
     * <h4>📈 性能监控建议：</h4>
     * <ul>
     *   <li>监控线程池活跃线程数，评估是否需要调整核心线程数</li>
     *   <li>监控队列长度，如果经常满队列需要考虑增加线程数或优化任务执行效率</li>
     *   <li>监控任务执行时间，识别性能瓶颈</li>
     *   <li>通过JVM监控工具观察线程创建和销毁情况</li>
     * </ul>
     *
     * <h4>🚀 典型使用场景：</h4>
     * <pre>{@code
     * @Async("asyncExecutor")
     * @Scheduled(cron = "0 30 0 * * ?")
     * public void syncCloudToLocal() {
     *     // 数据同步逻辑异步执行，不阻塞定时任务调度器
     *     dataSyncService.syncCloudToLocal(true, null);
     * }
     * }</pre>
     *
     * @return 配置好的异步任务执行器
     * @see org.springframework.scheduling.annotation.Async
     * @see com.tjsj.sync.modules.sync.controller.DatabaseSyncController
     */
    @Bean(name = "asyncExecutor")
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 设置核心线程数：常驻线程，处理日常异步任务
        executor.setCorePoolSize(asyncPoolSize);

        // 设置最大线程数：高峰期最大线程数，平衡性能和资源消耗
        executor.setMaxPoolSize(10);

        // 设置队列容量：任务缓冲队列，避免任务丢失
        executor.setQueueCapacity(100);

        // 设置线程名称前缀：便于日志追踪和问题定位
        executor.setThreadNamePrefix("AsyncExecutor-");

        // 初始化线程池
        executor.initialize();

        return executor;
    }
}
