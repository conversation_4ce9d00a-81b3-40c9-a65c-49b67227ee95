package com.tjsj.common.utils.string;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.writer.ObjectWriter;

import java.lang.reflect.Type;

/**
 * StringToJsonSerializer
 *
 * <AUTHOR>
 * @date 2024/08/10
 * @description 字符串到json序列化器
 */
public class StringToJsonSerializer implements ObjectWriter<String> {

    @Override
    public void write(JSONWriter jsonWriter, Object object, Object fieldName, Type fieldType, long features) {
        // 将 object 转换为字符串并处理
        if (object instanceof String) {
            // 解析字符串为 JSON 对象并写入
            jsonWriter.writeAny(JSON.parse((String) object));
        } else {
            // 直接写入非字符串对象
            jsonWriter.writeAny(object);
        }
    }

}

