package com.tjsj.common.utils.crypto;


import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class AESUtils {

    /**
     * 加密
     * @param originalStr    待加密字符串
     * @param encodingFormat 编码方式
     * @param sKey           加密key
     * @return
     * @throws Exception
     */
    public static String encrypt(String originalStr, String encodingFormat, String sKey) {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            byte[] raw = sKey.substring(0, 16).getBytes("UTF-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            // 使用CBC模式，需要一个向量iv，可增加加密算法的强度
            String ivParam = MD5Utils.getMd5Str(sKey).substring(0, 16);
            IvParameterSpec iv = new IvParameterSpec(ivParam.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
            byte[] encrypted = cipher.doFinal(originalStr.getBytes(encodingFormat));

            Base64.Encoder base64 = Base64.getEncoder();
            String encodeStr = base64.encodeToString(encrypted);

            return encodeStr;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("AES加密失败...");
        }
        return null;
    }


    /**
     * 解密
     *
     * @param toDecryptStr   待解密字符串
     * @param encodingFormat 编码方式
     * @param sKey           解密key
     * @return
     * @throws Exception
     */
    public static String decrypt(String toDecryptStr, String encodingFormat, String sKey) {
        try {
            byte[] raw = sKey.substring(0, 16).getBytes("ASCII");
            SecretKeySpec sKeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            String ivParam = MD5Utils.getMd5Str(sKey).substring(0, 16);
            IvParameterSpec iv = new IvParameterSpec(ivParam.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, sKeySpec, iv);

            Base64.Decoder decode = Base64.getDecoder();
            // 先用base64解密
            byte[] encrypted = decode.decode(toDecryptStr);
            byte[] original = cipher.doFinal(encrypted);
            String originalString = new String(original, encodingFormat);
            return originalString;
        } catch (Exception ex) {
            log.error("AES解密失败...");
        }
        return null;
    }

    public static void main(String[] args) {
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("mobile", "18408234277");
            map.put("channelName", "ceshi");

            String result = JSONObject.toJSONString(map);
            System.out.println(result);

            String sKey = "aljteqm74ypenon46yer".substring(0, 16);

            System.out.println(sKey);
            // 加密
            String enString = encrypt(result, "utf-8", sKey);
            System.out.println("加密后的字串是：" + enString);

            // 解密
            String DeString = decrypt(enString, "utf-8", sKey);
            System.out.println("解密后的字串是：" + DeString);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
