package com.tjsj.common.utils.network;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取经纬度
 *
 * <AUTHOR> 返回格式：Map<String,Object> map map.put("status",
 * reader.nextString());//状态 map.put("result", list);//查询结果
 * list<map<String,String>> 密钥:f247cdb592eb43ebac6ccd27f796e2d2
 */
public class GetLatAndLngByBaidu {

    private static String loadJSON(String url) {
        StringBuilder json = new StringBuilder();
        try {
            URL oracle = new URL(url);
            URLConnection yc = oracle.openConnection();
            BufferedReader in = new BufferedReader(new InputStreamReader(yc.getInputStream()));
            String inputLine = null;
            while ((inputLine = in.readLine()) != null) {
                json.append(inputLine);
            }
            in.close();
        } catch (MalformedURLException e) {
        } catch (IOException e) {
        }
        return json.toString();
    }

    /**
     * 百度根据地址取经纬度
     *
     * @param @param  address
     * @param @return
     * @param @throws Exception
     * @return Map<String                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ,                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Double>
     * @throws <AUTHOR>
     * @date 2017年9月20日下午9:50:00
     */
    public static Map<String, Double> getLngAndLat(String address) throws Exception {
        Map<String, Double> map = new HashMap<String, Double>();
        String url = "http://api.map.baidu.com/geocoder/v2/?address=" + address
                + "&output=json&ak=你的AK";
        String json = loadJSON(url);
        System.out.println(json);
        JSONObject obj = JSONObject.parseObject(json);
        if (obj != null) {
            if (obj.get("status").toString().equals("0")) {
                double lng = obj.getJSONObject("result").getJSONObject("location").getDouble("lng");
                double lat = obj.getJSONObject("result").getJSONObject("location").getDouble("lat");
                map.put("lng", lng);
                map.put("lat", lat);
                System.out.println("经度：" + lng + "---纬度：" + lat);
            } else {
                System.out.println("未找到相匹配的经纬度！");
//                throw new Exception();
            }

        }
        return map;
    }

    /**
     * 高德地图地址获取经纬度
     *
     * @param @param  address
     * @param @return
     * @param @throws Exception
     * @return Map<String                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ,                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Double>
     * @throws <AUTHOR>
     * @date 2017年9月20日下午8:46:52
     */
    public static Map<String, Double> getLngAndLatByAmap(String address) throws Exception {
        System.out.println(address);
        address = address.trim();
        Map<String, Double> map = new HashMap<String, Double>();
        String url = "http://restapi.amap.com/v3/geocode/geo?address=" + URLEncoder.encode(address, "utf-8")
                + "&output=json&key=9ed2019fc12d249398cccac9fdbc9a90";
        GetMethod method = new GetMethod(url);
        method.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
        HttpClient client = new HttpClient();
        client.getHttpConnectionManager().getParams().setConnectionTimeout(10000); // 设置连接超时
        int status = client.executeMethod(method);
        if (status == 200) {
            String json = method.getResponseBodyAsString();
            System.out.println(json);
            JSONObject obj = JSONObject.parseObject(json);
            if (obj.get("status").toString().equals("1")) {
                JSONArray array = obj.getJSONArray("geocodes");
                String str = array.getString(0);
                JSONObject locationjson = JSONObject.parseObject(str);
                str = locationjson.getString("location");
                String[] location = str.split(",");
                double lng = Double.parseDouble(location[0]);
                double lat = Double.parseDouble(location[1]);
                map.put("lng", lng);
                map.put("lat", lat);
                System.out.println("经度：" + lng + "---纬度：" + lat);
            } else {
                System.out.println("未找到相匹配的经纬度！");
//                throw new Exception();
            }
        }
        return map;
    }

    public static void main(String[] args) throws IOException {

    }


    /**
     * 从腾讯根据地址获取经纬度
     *
     * @param @param  address
     * @param @return
     * @param @throws Exception
     * @return Map<String                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ,                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Double>
     * @throws
     * <AUTHOR>
     * @date 2017年9月21日 上午8:08:09
     * @version 1.0.0
     */
//    public static Map<String, Double> getLngAndLatByQQ(String address) throws Exception {
//        address = address.trim();
//        Map<String, Double> map = new HashMap<String, Double>();
//        String url = "http://apis.map.qq.com/ws/geocoder/v1/?address=" + URLEncoder.encode(address, "utf-8")
//                + "&key=你的key";
//        GetMethod method = new GetMethod(url);
//        method.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
//        HttpClient client = new HttpClient();
//        client.getHttpConnectionManager().getParams().setConnectionTimeout(10000); // 设置连接超时
//        int status = client.executeMethod(method);
//        if (status == 200) {
//            String json = method.getResponseBodyAsString();
//            System.out.println(json);
//            JSONObject obj = JSONObject.parseObject(json);
//            if (obj.get("status").toString().equals("0")) {
//                String msg = obj.getString("message");
//                System.out.println(msg);
//                JSONObject result = obj.getJSONObject("result");
//                JSONObject location = result.getJSONObject("location");
//                double lng = location.getDouble("lng");
//                double lat = location.getDouble("lat");
//                map.put("lng", lng);
//                map.put("lat", lat);
//                System.out.println("经度：" + lng + "---纬度：" + lat);
//            } else {
//                System.out.println("未找到相匹配的经纬度！");
//                throw new Exception();
//            }
//        }
//        return map;
//    }

}
