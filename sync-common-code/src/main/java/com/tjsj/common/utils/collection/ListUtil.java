package com.tjsj.common.utils.collection;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.List;

/**
 * ListUtil
 *
 * <AUTHOR>
 * @date 2024/8/13 21:13
 * @description 列表工具类
 */
@UtilityClass
@Schema(name = "ListUtil", description = "列表工具类")
public class ListUtil {

    /**
     * 向列表中添加元素并返回该列表，实现链式调用
     *
     * @param list 要操作的列表
     * @param item 要添加的元素
     * @param <T>  元素的类型
     * @return 返回传入的列表，以便支持链式调用
     */
    public <T> List<T> add(List<T> list, T item) {
        list.add(item);
        return list;
    }

    public <T> List<T> add(List<T> list, List<T> items) {
        list.addAll(items);
        return list;
    }
    /**
     * 向列表中添加多个元素并返回该列表，实现链式调用
     *
     * @param list  要操作的列表
     * @param items 要添加的多个元素
     * @param <T>   元素的类型
     * @return 返回传入的列表，以便支持链式调用
     */
    @SafeVarargs
    public <T> List<T> addAll(List<T> list, T... items) {
        list.addAll(Arrays.asList(items));
        return list;
    }


}
