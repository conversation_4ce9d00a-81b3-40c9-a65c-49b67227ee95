package com.tjsj.common.utils.string;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * RegexUtils
 *
 * <AUTHOR>
 * @date 2024/7/18 16:10
 * @description 正则表达式工具类
 */
@Schema(name = "RegexUtils", description = "正则表达式工具类")
public class RegexUtils {

    /**
     * 判断字符串是否包含数字
     *
     * @param input 要检查的字符串
     * @return 如果字符串包含数字则返回 true，否则返回 false
     */
    public static boolean containsDigit(String input) {
        String regex = ".*\\d.*";
        return input != null && input.matches(regex);
    }



}
