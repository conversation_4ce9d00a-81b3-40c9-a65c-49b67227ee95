package com.tjsj.common.utils.identifier;

import com.tjsj.common.utils.date.DateUtils;

import java.util.Date;
import java.util.UUID;

/**
 * 唯一id生成工具类
 */
public class IdUtil {

    public static String getUuidStr() {
        String date = DateUtils.format(new Date(), "yyyyMMdd");
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return date + uuid;
    }

    public static String getDateStr() {
        String date = DateUtils.format(new Date(), "yyyyMMddHHmmss");
        return date;
    }


    public static String getUuid() {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return uuid;
    }
}
