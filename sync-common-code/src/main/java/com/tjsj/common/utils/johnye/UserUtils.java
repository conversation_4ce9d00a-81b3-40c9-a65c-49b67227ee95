package com.tjsj.common.utils.johnye;

import com.tjsj.common.constants.UserConsts;
import com.tjsj.common.utils.crypto.TokenUtil;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * UserUtils
 *
 * <AUTHOR> Ye
 * @date 2024/7/14 12:09
 * @description 用户工具类@version 1.0.0
 */
@Schema(description = "用户工具类")
public class UserUtils {

    /**
     * 判断是否系统用户
     *
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/07/14
     */
    public static Boolean isSystemUser() {
        return UserConsts.SYSTEM_USER.equals(TokenUtil.getCurrentUID());
    }

    /**
     * 判断不是系统用户
     *
     * @return {@link Boolean }
     * <AUTHOR> Ye
     * @date 2024/07/14
     */
    public static Boolean isNotSystemUser() {
        return !UserConsts.SYSTEM_USER.equals(TokenUtil.getCurrentUID());
    }

    /**
     * 判断是否系统用户
     *
     * @param userId 用户id
     * @return {@link Boolean }
     * <AUTHOR> Ye
     * @date 2024/07/14
     */
    public static Boolean isSystemUser(String userId) {
        return UserConsts.SYSTEM_USER.equals(userId);
    }

}
