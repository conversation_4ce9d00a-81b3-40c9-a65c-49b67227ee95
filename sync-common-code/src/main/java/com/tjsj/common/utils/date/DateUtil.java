package com.tjsj.common.utils.date;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * DateUtil
 *
 * <AUTHOR>
 * @date 2024/08/10
 * @description 日期工具
 */
@Schema(name = "DateUtil", description = "日期工具类")
public class DateUtil {

    /**
     * 获取当前时间的字符串表示，使用默认格式 yyyy-MM-dd HH:mm:ss。
     *
     * @return 当前时间的字符串表示
     */
    public static String getCurrentTime() {
        return getCurrentTime("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获取当前时间的字符串表示，使用指定的格式。
     *
     * @param pattern 时间格式
     * @return 当前时间的字符串表示
     */
    public static String getCurrentTime(String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.now().format(formatter);
    }

    /**
     * 获取当前日期，不含时间部分。
     *
     * @return 当前日期的 LocalDate 表示
     */
    public static LocalDate getCurrentDate() {
        return LocalDate.now();
    }

    /**
     * 获取当前字符串日期
     *
     * @return {@link String }
     * <AUTHOR> Ye
     * @date 2024/08/27
     */
    public static String getCurStrDate() {
        return getCurrentTime(DateUtils.DATE_PATTERN);
    }


    /**
     * 获取当前日期和时间。
     *
     * @return 当前日期和时间的 LocalDateTime 表示
     */
    public static LocalDateTime getCurrentDateTime() {
        return LocalDateTime.now();
    }

    /**
     * 获得本周的开始日期和结束日期
     */
    public static Date[] getWeekStartDateAndEndDateRange() {
        long oneDayLong = 24 * 60 * 60 * 1000;
        Date now = new Date();
        long mondayTime = now.getTime() - (now.getDay() - 1) * oneDayLong;
        long sundayTime = now.getTime() + (7 - now.getDay()) * oneDayLong;
        Date monday = new Date(mondayTime);
        Date sunday = new Date(sundayTime);
        Date[] weekRange = {getStartOfDay(monday), getEndOfDay(sunday)};
        return weekRange;
    }

    // 获得某天最大时间 2020-02-19 23:59:59
    public static Date getEndOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()),
                ZoneId.systemDefault());
        ;
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    // 获得某天最小时间 2020-02-17 00:00:00
    public static Date getStartOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()),
                ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }


    /*
     * 将时间转换为时间戳
     */
    public static String dateToStamp(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String stamp = "";
        if (!"".equals(time)) {//时间不为空
            try {
                stamp = String.valueOf(sdf.parse(time).getTime() / 1000);
            } catch (Exception e) {
                System.out.println("参数为空！");
            }
        } else {    //时间为空
            long current_time = System.currentTimeMillis();  //获取当前时间
            stamp = String.valueOf(current_time / 1000);
        }
        return stamp;
    }


    /*
     * 将时间戳转换为时间
     */
    public static String stampToDate(int time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time_Date = sdf.format(new Date(time * 1000L));
        return time_Date;

    }

    //请求时间 格式:YYYYMMDD
    public static String getCurrent() {
        return new SimpleDateFormat("yyyyMMdd").format(new Date());
    }

    /**
     * 生成从开始日期到结束日期之间的所有日期字符串列表
     *
     * @param startDateStr 开始日期的字符串
     * @param endDateStr   结束日期的字符串
     * @param format       输入和输出的日期格式，例如 "yyyy-MM-dd"
     * @return 包含所有日期的字符串列表
     */
    public static List<String> generateDateList(String startDateStr, String endDateStr, String format) {

        format = format == null ? "yyyy-MM-dd" : format;

        // 将输入的日期字符串转换为 Date 对象
        DateTime start = cn.hutool.core.date.DateUtil.parse(startDateStr, format);
        DateTime end = cn.hutool.core.date.DateUtil.parse(endDateStr, format);

        List<String> dateList = new ArrayList<>();

        // 检查结束日期是否早于开始日期
        if (end.isBefore(start)) {
            throw new IllegalArgumentException("结束日期不能早于开始日期");
        }

        // 生成日期列表
        while (start.compareTo(end) <= 0) {
            dateList.add(cn.hutool.core.date.DateUtil.format(start, format));
            // 递增一天
            start = start.offset(DateField.DAY_OF_MONTH, 1);
        }

        return dateList;
    }

    public static List<LocalDate> generateLocalDateList(String startDateStr, String endDateStr, String format) {

        // 使用指定格式的DateTimeFormatter来解析日期字符串
        DateTimeFormatter formatter = format == null ? DateTimeFormatter.ofPattern("yyyy-MM-dd") :
                DateTimeFormatter.ofPattern(format);

        // 将输入的日期字符串转换为 LocalDate 对象
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        List<LocalDate> dateList = new ArrayList<>();

        // 检查结束日期是否早于开始日期
        if (endDate.isBefore(startDate)) {
            throw new IllegalArgumentException("结束日期不能早于开始日期");
        }

        // 生成日期列表
        while (!startDate.isAfter(endDate)) {
            dateList.add(startDate);
            // 递增一天
            startDate = startDate.plusDays(1);
        }

        return dateList;
    }

    /**
     * 将 LocalDate 转换为指定格式的字符串
     *
     * @param localDate 要转换的 LocalDate 对象
     * @param pattern   日期格式，如 "yyyy-MM-dd"
     * @return 格式化后的日期字符串
     */
    public static String formatLocalDate(LocalDate localDate, String pattern) {
        pattern = StrUtil.isEmpty(pattern) ? "yyyy-MM-dd" : pattern;
        if (localDate == null) {
            throw new IllegalArgumentException("localDate cannot be null");
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return localDate.format(formatter);
    }

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    /**
     * 将字符串转换为 LocalDate 对象
     *
     * @param dateStr 要转换的字符串
     * @return LocalDate 对象，如果转换失败则返回 null
     */
    public static LocalDate parse(String dateStr) {
        try {
            return LocalDate.parse(dateStr, FORMATTER);
        } catch (DateTimeParseException e) {
            // Handle the exception as needed
            return null;
        }
    }

    public static void main(String[] args) {
        // 示例开始日期和结束日期
        String startDateStr = "2024-07-01";
        String endDateStr = "2024-07-10";


        // 生成日期字符串列表
        List<String> dateList = generateDateList(startDateStr, endDateStr, null);

        // 打印日期字符串列表
        dateList.forEach(System.out::println);
    }

}
