package com.tjsj.common.utils.reflection;

import com.tjsj.modules.user.model.entity.UserDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.experimental.UtilityClass;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * LombokUtil
 *
 * <AUTHOR>
 * @date 2024/8/10 15:55
 * @description Lombok工具类
 */
@UtilityClass
@Schema(description = "Lombok工具类")
public class LombokUtil {

    private final Logger logger = LoggerFactory.getLogger(LombokUtil.class);

    /**
     * 获取使用 @FieldNameConstants 注解的类的所有常量值。
     *
     * @param clazz 实体类的 Class 对象
     * @return 包含所有字段常量值的列表
     */
    public List<String> getFieldNames(@NotNull Class<?> clazz) {
        List<String> fieldNames = new ArrayList<>();

        try {
            // 获取 Fields 内部类
            Class<?> fieldsClass = Class.forName(clazz.getName() + "$Fields");

            // 使用反射获取 Fields 内部类中的所有字段
            for (Field field : fieldsClass.getDeclaredFields()) {
                fieldNames.add((String) field.get(null));
            }
        } catch (ClassNotFoundException | IllegalAccessException e) {
            logger.error("获取 @FieldNameConstants 注解的类的所有常量值失败, 原因:", e);
        }

        return fieldNames;
    }

    public static void main(String[] args) {
        List<String> fieldNames = LombokUtil.getFieldNames(UserDO.class);
        System.out.println(fieldNames);
    }

}

