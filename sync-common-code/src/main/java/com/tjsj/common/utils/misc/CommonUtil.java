package com.tjsj.common.utils.misc;

import com.tjsj.common.utils.string.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通用工具封装
 */
public class CommonUtil {

    static String MOBILE_REGEX = "^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-8])|(18[0-9])|166|198|199|(147))\\d{8}$";

    static String IDCARD_REGEX = "/^(^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$)|(^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[Xx])$)$/";

    /**
     * 校验是否是手机号
     *
     * @param mobile
     * @return
     */
    public static boolean isMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        Pattern pattern = Pattern.compile(MOBILE_REGEX, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(mobile);
        return matcher.matches();
    }

    /**
     * 校验是否是正确的身份证号码
     *
     * @param num
     * @return
     */
    public static boolean isIdCardNum(String num) {
        if (StringUtils.isEmpty(num)) {
            return false;
        }
        Pattern pattern = Pattern.compile(IDCARD_REGEX, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(num);
        return matcher.matches();
    }

}
