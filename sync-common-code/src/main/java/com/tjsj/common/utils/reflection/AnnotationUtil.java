package com.tjsj.common.utils.reflection;

import cn.hutool.core.collection.CollUtil;
import com.tjsj.common.annotation.CalculateField;
import com.tjsj.common.utils.johnye.CustomUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Field;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/8/10 19:27
 * @description 注解工具类
 */
@Schema(description = "注解工具类")
public class AnnotationUtil {

    /**
     * 获取 CalculateField 注解的字段名列表
     *
     * @param clazz            实体类的 Class 对象
     * @param filterFieldNames 需要过滤的字段名列表
     * @return {@link List}<{@link String}>
     */
    public static List<String> getCalFieldAnnotationNames(Class<?> clazz, List<String> filterFieldNames) {
        // 获取类的所有字段，并筛选带有 @CalculateField 注解的字段
        Stream<String> fieldNamesStream = Stream.of(clazz.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(CalculateField.class))
                .map(field -> CustomUtils.camelToSnake(field.getName()));

        // 如果 filterFieldNames 不为 null 且不为空，则进行过滤，否则直接返回所有字段名
        return (CollUtil.isNotEmpty(filterFieldNames))
                ? fieldNamesStream
                .filter(filterFieldNames::contains).collect(Collectors.toList())
                : fieldNamesStream.collect(Collectors.toList());
    }

    /**
     * 获取CalculateField注解的值列表
     *
     * @param clazz            clazz
     * @param filterFieldNames 用于过滤的字段名列表
     * @return {@link List }<{@link String }>
     * <AUTHOR> Ye
     * @date 2024/07/17
     */
    public static List<String> getCalFieldAnnotationValues(Class<?> clazz, List<String> filterFieldNames) {
        // 获取类的所有字段并筛选带有 @CalculateField 注解的字段
        Stream<String> fieldNamesStream = Stream.of(clazz.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(CalculateField.class))
                .map(field -> field.getAnnotation(CalculateField.class).value());

        // 根据 filterFieldNames 是否为空进行过滤或直接返回
        return (filterFieldNames != null && !filterFieldNames.isEmpty())
                ? fieldNamesStream.filter(filterFieldNames::contains).collect(Collectors.toList())
                : fieldNamesStream.collect(Collectors.toList());
    }

    /**
     * 获取CalculateField注解的值的对应的属性名
     *
     * @param clazz           clazz
     * @param annotationValue 注释值
     * @return {@link String }
     * <AUTHOR> Ye
     * @date 2024/08/10
     */
    public static String getCalAnnoFieldName(@NotNull Class<?> clazz, String annotationValue) {
        // 获取类的所有字段并筛选带有 @CalculateField 注解的字段
        return Stream.of(clazz.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(CalculateField.class))
                .filter(field -> field.getAnnotation(CalculateField.class).value().equals(annotationValue))
                .map(Field::getName)
                .findFirst()
                .orElse(null);
    }

}
