package com.tjsj.common.utils.crypto;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.tjsj.common.constants.UserConsts;
import com.tjsj.modules.user.model.entity.UserDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * @Author: JOHN DUAN
 * @Date: 2020/8/21 下午11:00
 * token也称作令牌，由uid+time+sign[+固定参数]组成:
 * uid: 用户唯一身份标识
 * time: 当前时间的时间戳
 * sign: 签名, 使用 hash/encrypt 压缩成定长的十六进制字符串，以防止第三方恶意拼接
 * 固定参数(可选): 将一些常用的固定参数加入到 token 中是为了避免重复查数据库
 */
@Slf4j
public class TokenUtil {


    public static final String SPLIT = "@#%&";

    /**
     * 获取request
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return requestAttributes == null ? null : requestAttributes.getRequest();
    }

    /**
     * 获取当前请求的接口的用户的user_id
     *
     * @return {@link String }
     * <AUTHOR> Ye
     * @date 2024/07/14
     */
    public static String getCurrentUID() {
        HttpServletRequest request = getRequest();
        if (null == request) {
            return null;
        }
        // 从 http 请求头中取出 token
        String token = request.getHeader(UserDO.Fields.token);
        if (null == token) {
            return null;
        }
        if ("zhongyinadmin".equals(token)) {
            return UserConsts.SYSTEM_USER;
        }
        String spealToken = "ao4:J_AE+WFV=uk~g=L2UB.szU";
        if (spealToken.equals(token)) {
            return null;
        }
        try {
            String audience = JWT.decode(token).getAudience().get(0);
            if (audience.contains(SPLIT)) {
                return audience.split(SPLIT)[0];
            }
            return audience;
        } catch (JWTDecodeException e) {
            log.error("token解析失败", e);
            return null;
        }
    }

    /**
     * 获取当前请求的接口的用户的user_account
     *
     * @return
     */
    public static String getCurrentUserAccount() {
        // 从 http 请求头中取出 token
        String token = getRequest().getHeader(UserDO.Fields.token);
        String audience = JWT.decode(token).getAudience().get(0);
        if (audience.contains(SPLIT)) {
            return audience.split(SPLIT)[1];
        }
        return audience;
    }


    // 用于生成token
    public static String getToken(String userId, String userAccount, String password) {
        Date start = new Date();
        long currentTime = System.currentTimeMillis() + 24 * 60 * 60 * 1000;
        Date end = new Date(currentTime);
        return JWT.create().withAudience(userId + SPLIT + userAccount)
                .withIssuedAt(start).withExpiresAt(end).sign(Algorithm.HMAC256(password));
    }

}
