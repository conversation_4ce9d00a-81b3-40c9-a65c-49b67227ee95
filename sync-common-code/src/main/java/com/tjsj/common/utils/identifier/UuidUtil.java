package com.tjsj.common.utils.identifier;

import java.util.UUID;

/**
 * UUID生成工具类
 * creater lhh
 * data 2021-1-20
 */
public class UuidUtil {

    public UuidUtil() {
    }

    public static String getUUID() {
        UUID uuid = UUID.randomUUID();
        String str = uuid.toString();
        // 去掉"-"符号
        String temp = str.substring(0, 8) + str.substring(9, 13) + str.substring(14, 18) + str.substring(19, 23) + str.substring(24);
        return temp;
    }
    //获得指定数量的UUID
    public static String[] getUUID(int number) {
        if (number < 1) {
            return null;
        }
        String[] ss = new String[number];
        for (int i = 0; i < number; i++) {
            ss[i] = getUUID();
        }
        return ss;
    }

    public static void main(String[] args) {
        String ss = getUUID();
        System.out.println("ss====="+ss);
    }
}
