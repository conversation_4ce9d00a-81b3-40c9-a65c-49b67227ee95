package com.tjsj.common.utils.date;

import com.tjsj.common.utils.string.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * DateUtils
 *
 * <AUTHOR>
 * @date 2024/07/13
 * @description 日期工具类
 */
public class DateUtils {

    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(DateUtils.class);

    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN_WITHOUT_SPLIT = "yyyyMMdd";
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date 日期
     * @return 返回yyyy-MM-dd格式日期
     * <AUTHOR> Ye
     * @date 2024/07/24
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param localDate 日期
     * @return 返回yyyy-MM-dd格式日期
     * <AUTHOR> Ye
     * @date 2024/07/24
     */
    public static String format(LocalDate localDate) {
        if (null == localDate) {
            return null;
        }
        // 定义日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN);

        // 将 LocalDate 转换为字符串
        return localDate.format(formatter);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：MyDateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 字符串转换成日期
     *
     * @param strDate 日期字符串
     * @param pattern 日期的格式，如：MyDateUtils.DATE_TIME_PATTERN
     */
    public static Date stringToDate(String strDate, String pattern) {
        if (StringUtils.isBlank(strDate)) {
            return null;
        }

        org.joda.time.format.DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 对日期的【秒】进行加/减
     *
     * @param date    日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(Date date, int seconds) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusSeconds(seconds).toDate();
    }

    /**
     * 计算当前时间至凌晨12点之间的时间间隔（秒）
     *
     * @return
     */
    public static long getSecondsFormNowToEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH) + 1,
                0, 0, 0);
        long result = (calendar.getTimeInMillis() - new Date().getTime()) / 1000;
        return result;
    }

    /**
     * 获取过去的分钟
     *
     * @param date
     * @return
     */
    public static long pastMinutes(Date date) {
        long t = System.currentTimeMillis() - date.getTime();
        return t / (60 * 1000);
    }

    public static List<Date> findDates(String start, String end) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date dBegin = null;
        try {
            dBegin = sdf.parse(start);
            Date dEnd = sdf.parse(end);
            List lDate = new ArrayList();
            lDate.add(dBegin);
            Calendar calBegin = Calendar.getInstance();
            // 使用给定的 Date 设置此 Calendar 的时间
            calBegin.setTime(dBegin);
            Calendar calEnd = Calendar.getInstance();
            // 使用给定的 Date 设置此 Calendar 的时间
            calEnd.setTime(dEnd);
            // 测试此日期是否在指定日期之后
            while (dEnd.after(calBegin.getTime())) {
                // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
                calBegin.add(Calendar.DAY_OF_MONTH, 1);
                lDate.add(calBegin.getTime());
            }
            return lDate;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static List<String> getDateRange(String startDate, String endDate, boolean ascendingOrder) {
        return Stream.iterate(LocalDate.parse(startDate), date -> date.plusDays(1))
                .limit(LocalDate.parse(endDate).toEpochDay() - LocalDate.parse(startDate).toEpochDay() + 1)
                .sorted(ascendingOrder ? LocalDate::compareTo : (date1, date2) -> date2.compareTo(date1))
                .map(DateTimeFormatter.ofPattern("yyyy-MM-dd")::format)
                .collect(Collectors.toList());
    }

    /**
     * 比较字符串日期的大小
     *
     * @param date        日期
     * @param compareDate 比较日期
     * @return {@link Boolean }
     * <AUTHOR> Ye
     * @date 2024/07/13
     */
    public static Boolean compareStringDate(String date, String compareDate) {
        try {
            // 这里遇到了输入日期为null的问题，故把异常捕获，返回false
            // logger.info("date:{},compareDate:{}", date, compareDate);
            DateTimeFormatter formatter =
                    DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate date1 = LocalDate.parse(date, formatter);
            LocalDate date2 = LocalDate.parse(compareDate, formatter);

            // 比较日期
            return date1.isAfter(date2);
        } catch (Exception ignore) {
        }
        return false;
    }
}
