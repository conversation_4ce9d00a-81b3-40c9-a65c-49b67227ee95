package com.tjsj.common.utils.johnye;

import cn.hutool.core.util.StrUtil;
import com.tjsj.common.enums.sec.StockType;
import com.tjsj.modules.base.mapper.MyBaseMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Optional;

/**
 * CustomUtils
 *
 * <AUTHOR>
 * @date 2024/7/13 0:29
 * @description 自定义工具类
 */
@Schema(description = "自定义工具类")
@Component
@Slf4j
public class CustomUtils {


    @Resource
    private MyBaseMapper myBaseMapper;

    /**
     * 将驼峰命名转换为下划线命名
     * 例如：
     * camelToSnake("camelCaseToSnake") -> "camel_case_to_snake"
     * camelToSnake("exampleTestString") -> "example_test_string"
     *
     * @param camelCaseStr 驼峰命名的字符串
     * @return 下划线命名的字符串
     */
    public static String camelToSnake(String camelCaseStr) {
        if (camelCaseStr == null || camelCaseStr.isEmpty()) {
            return camelCaseStr;
        }
        String regex = "([a-z])([A-Z]+)";
        String replacement = "$1_$2";
        return camelCaseStr
                .replaceAll(regex, replacement)
                .toLowerCase();
    }

    /**
     * 将下划线命名转换为驼峰命名
     * 例如：
     * snakeToCamel("snake_case_to_camel") -> "snakeCaseToCamel"
     * snakeToCamel("example_test_string") -> "exampleTestString"
     *
     * @param snakeCaseStr 下划线命名的字符串
     * @return 驼峰命名的字符串
     */
    public static String snakeToCamel(String snakeCaseStr) {
        if (snakeCaseStr == null || snakeCaseStr.isEmpty()) {
            return snakeCaseStr;
        }
        StringBuilder result = new StringBuilder();
        boolean nextCharUpperCase = false;
        for (char c : snakeCaseStr.toCharArray()) {
            if (c == '_') {
                nextCharUpperCase = true;
            } else {
                if (nextCharUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextCharUpperCase = false;
                } else {
                    result.append(c);
                }
            }
        }
        return result.toString();
    }


    /**
     * 通过属性名称获取属性值, 已被BeanUtil.getProperty()方法取代
     *
     * @param obj       对象
     * @param fieldName 属性名称
     * @return {@link Object }
     * <AUTHOR> Ye
     * @date 2024/07/25
     */
    @Deprecated
    public static Object getValueByFieldName(Object obj, String fieldName) {
        try {
            // 获取属性
            Field field = obj.getClass().getDeclaredField(fieldName);
            // 设置可访问
            field.setAccessible(true);
            // 获取属性值
            return field.get(obj);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 通过属性名称设置属性值
     *
     * @param obj       对象
     * @param fieldName 字段名称
     * @param value     属性值
     * @return {@link Object } 对象
     * <AUTHOR> Ye
     * @date 2024/07/25
     */
    public static Object setFieldByName(Object obj, String fieldName, Object value) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
            return obj;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取股票类型
     *
     * @param secCode 证券代码
     * @return {@link Optional<StockType>}
     */
    public static Optional<StockType> getStockType(String secCode) {
        if (StrUtil.isEmpty(secCode)) {
            return Optional.empty();
        }

        if (secCode.charAt(0) == '0') {
            return Optional.of(StockType.SHENZHEN_EXCHANGE);
        } else if (secCode.startsWith("60")) {
            return Optional.of(StockType.SHANGHAI_EXCHANGE);
        } else if (secCode.startsWith("30")) {
            return Optional.of(StockType.ChiNext);
        } else if (secCode.startsWith("68")) {
            return Optional.of(StockType.StarMarket);
        } else if (Arrays.asList('8', '4', '9').contains(secCode.charAt(0))) {
            return Optional.of(StockType.BEIJING_EXCHANGE);
        }

        return Optional.empty();
    }


}
