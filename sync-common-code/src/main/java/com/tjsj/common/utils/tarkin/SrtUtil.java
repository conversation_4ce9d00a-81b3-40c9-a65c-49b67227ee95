package com.tjsj.common.utils.tarkin;

import com.tjsj.common.constants.LevelSrtConsts;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * SrtUtil
 *
 * <AUTHOR>
 * @date 2024/8/28 16:01
 * @description 策略相关工具类
 */
@Schema(name = "SrtUtil", description = "策略相关工具类")
public class SrtUtil {

    /**
     * 设置评级策略名称
     *
     * @param levelSrtName 评级策略名称
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/08/28
     */
    public static String levelSrtName(String levelSrtName) {
        return levelSrtName == null ? LevelSrtConsts.NO_USER_STRATEGY_SHOW : levelSrtName;
    }
}
