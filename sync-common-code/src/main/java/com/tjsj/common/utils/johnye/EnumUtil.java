package com.tjsj.common.utils.johnye;

import com.tjsj.common.enums.BaseEnum;

/**
 * <AUTHOR>
 * @date 2024/8/7 14:11
 * @description
 */
public class EnumUtil {

    /**
     * 根据 code 值获取对应的枚举对象
     *
     * @param enumType 枚举的类型
     * @param code     枚举的 code 值
     * @param <T>      枚举类型，必须实现 BaseEnum 接口
     * @return 对应的枚举对象，如果未匹配到则返回 null
     */
    public static <T extends BaseEnum> T fromCode(Class<T> enumType, Object code) {
        if (code == null || enumType == null) {
            return null;
        }
        Short codeValue = Short.valueOf(String.valueOf(code));
        for (T enumConstant : enumType.getEnumConstants()) {
            if (enumConstant.getCode().equals(codeValue)) {
                return enumConstant;
            }
        }
        return null;
    }
}
