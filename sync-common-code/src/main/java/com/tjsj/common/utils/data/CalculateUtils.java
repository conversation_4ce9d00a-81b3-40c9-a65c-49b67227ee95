package com.tjsj.common.utils.data;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * CalculateUtils
 *
 * <AUTHOR>
 * @date 2024/7/24 23:00
 * @description 计算相关工具类
 */
@Schema(name = "CalculateUtils", description = "计算相关工具类")
@Component
public class CalculateUtils {

    /**
     * 计算中位数
     * <p>
     * 1. 对列表进行null值的过滤，并且排序
     * 2. 计算列表的大小，如果列表为空，则返回null
     * 3. 如果列表的大小为偶数，则取中间两个数的平均值，否则取中间的数
     * 4. 返回计算结果
     * <p>
     *
     * @param processList 进程列表
     * @return {@link BigDecimal }
     * <AUTHOR> Ye
     * @date 2024/07/24
     */
    public static BigDecimal calculateMedianValue(List<BigDecimal> processList) {
        if (processList == null || processList.isEmpty()) {
            return null;
        }

        // 对列表进行null值的过滤，并且排序
        List<BigDecimal> sortedList = processList.stream()
                .filter(Objects::nonNull)
                .sorted()
                .collect(Collectors.toList());

        int size = sortedList.size();
        if (size == 0) {
            return null;
        }

        if (size % 2 == 0) {
            // 偶数个元素，取中间两个数的平均值
            return sortedList.get(size / 2 - 1)
                    .add(sortedList.get(size / 2))
                    .divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP);
        } else {
            // 奇数个元素，取中间的数
            return sortedList.get(size / 2);
        }
    }


}
