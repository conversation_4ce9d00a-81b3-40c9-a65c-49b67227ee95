package com.tjsj.common.utils.io;

import com.tjsj.common.exception.RRException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * UploadFileUtils
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/09/26
 * @description 上传文件工具
 */
@Slf4j
public class UploadFileUtils {

    @Value("${files.image_path}")
    private String imagePath;

    @Value("${server.port}")
    private String port;

    @Value("${files.ip}")
    private String ip;

    /**
     * 封装出来的方法 共多个上传功能调用
     *
     * @param file
     * @return
     */
    public static Map<String, Object> upload(MultipartFile file, String imagePath, String port, String ip) {
        Map<String, Object> resultMap = new HashMap<>();
        long size = file.getSize();
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));

        String suffix1 = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!("jpg".equals(suffix1) || "jpeg".equals(suffix1) || "png".equals(suffix1) || "pdf".equals(suffix1) || "bmp".equals(suffix1) ||
                "txt".equals(suffix1) || "xls".equals(suffix1) || "doc".equals(suffix1) || "docx".equals(suffix1) || "xlsx".equals(suffix1)
                || "ppt".equals(suffix1) || "pptx".equals(suffix1) || "vsd".equals(suffix1) || "csv".equals(suffix1))) {
            throw new RRException("文件类型不支持");
        }
        if (size > 20000000) {
            throw new RRException("文件过大 ，不支持上传");
        } else if (size == 0) {
            throw new RRException("文件内容不能为空");
        }

        String fileName = null;

        //判断是否带盘符的文件  IE会造成这种问题
        if (file.getOriginalFilename().contains("\\")) {
            fileName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("\\") + 1, file.getOriginalFilename().lastIndexOf("."));
        } else if (file.getOriginalFilename().contains("/")) {
            fileName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("/") + 1, file.getOriginalFilename().lastIndexOf("."));
        } else {
            // 重命名文件
            fileName = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf("."));
        }
        if (fileName.getBytes().length >= 256) {
            throw new RRException("文件名称过长 ，请修改名称");
        }

        // 目标路径
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String path = imagePath + date;
        int count = 0;
        File fileCheck = new File(path, fileName + suffix);
        // 判断路径是否存在，如果不存在就创建一个
        if (!fileCheck.getParentFile().exists()) {
            fileCheck.getParentFile().mkdirs();
        }
        while (fileCheck.exists()) {
            count++;
            fileCheck = new File(path, fileName + "(" + count + ")" + suffix);
        }

        if (count > 0) {
            fileName = fileName + "(" + count + ")";
        }
        // 后缀
        fileName += suffix;

        try {
            // 如果文件不为空，写入上传路径
            if (!file.isEmpty()) {
                // 将上传文件保存到一个目标文件当中
                path = path + File.separator + fileName;
                file.transferTo(new File(path));
                //ipAddress 主机地址  port 端口号
                String imageUrl = "http://" + ip + ":" + port + "/" + date + "/" + URLEncoder.encode(fileName).replaceAll("\\+", "%20");

                resultMap.put("name", fileName);
                resultMap.put("status", "done");
                resultMap.put("thumbUrl", imageUrl);
                resultMap.put("url", imageUrl);
                resultMap.put("date", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                return resultMap;
            } else {
                // 上传文件为空
                throw new RRException("上传文件为空");
            }
        } catch (Exception e) {
            throw new RRException("上传文件失败");
        }
    }

}
