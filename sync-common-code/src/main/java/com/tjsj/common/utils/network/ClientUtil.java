package com.tjsj.common.utils.network;

import javax.servlet.http.HttpServletRequest;

/**
 * 
     * 类名:ClientUtil
     * 描述:	客户机工具 
     * 创建者:段江华
     * 创建时间: 2017年8月5日 上午9:18:02 
	 * 更新者:段江华
	 * 更新时间: 2017年8月5日 上午9:18:02
 */
public class ClientUtil {
	
	//private static Logger logger = Logger.getLogger(ClientUtil.class);
	
    /**
     * 
         * 方法名:getIpAddress
         * 功能描述:获取IP地址	 
         * 创建者:段江华
         * 创建时间: Dec 8, 2017 9:57:30 AM 
    	 * 更新者:段江华  
    	 * 更新时间: Dec 8, 2017 9:57:30 AM
     */
	public static String getIpAddress(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
			ip = request.getHeader("Proxy-Client-IP");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
			ip = request.getHeader("WL-Proxy-Client-IP");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
			ip = request.getHeader("HTTP_CLIENT_IP");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
			ip = request.getHeader("HTTP_X_FORWARDED_FOR");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip))
			ip = request.getRemoteAddr();
		return ip;
	}
	
	/**
	 * 
	     * 方法名:getOs
	     * 功能描述:获取系统类型	 
	     * 创建者:段江华
	     * 创建时间: Dec 8, 2017 10:01:13 AM 
		 * 更新者:段江华
		 * 更新时间: Dec 8, 2017 10:01:13 AM
	 */
	public static String getOs(HttpServletRequest request){
		String userAgent = request.getHeader("User-Agent").toLowerCase();
		
		if (userAgent.indexOf("windows") >= 0) {
			return "Windows";
		}else if (userAgent.indexOf("iphone") >= 0) {
			return "IPhone";
		}else if (userAgent.indexOf("mac") >= 0) {
			return "Mac";
		} else if (userAgent.indexOf("x11") >= 0) {
			return "Unix";
		} else if (userAgent.indexOf("android") >= 0) {
			return "Android";
		} else if (userAgent.indexOf("iphone") >= 0) {
			return "IPhone";
		}
		return "UnKnown, More-Info: " + userAgent;	
	}
	
	/**
	 * 
	     * 方法名:getBrowser
	     * 功能描述:获取浏览器	 
	     * 创建者:段江华
	     * 创建时间: Dec 8, 2017 10:08:42 AM 
		 * 更新者:段江华
		 * 更新时间: Dec 8, 2017 10:08:42 AM
	 */
	public static String getBrowser(HttpServletRequest request){
		String userAgent = request.getHeader("User-Agent").toLowerCase();
		if (userAgent.contains("edge")) {
			return (userAgent.substring(userAgent.indexOf("edge")).split(" ")[0]).replace("/", "-");
		} else if (userAgent.contains("msie")) {
			String substring = userAgent.substring(userAgent.indexOf("msie")).split(";")[0];
			return substring.split(" ")[0].replace("msie", "ie") + "-"+ substring.split(" ")[1];
		} else if (userAgent.contains("safari") && userAgent.contains("version")) {
			return (userAgent.substring(userAgent.indexOf("safari")).split(" ")[0]).split("/")[0]+ "-"+ (userAgent.substring(userAgent.indexOf("version")).split(" ")[0]).split("/")[1];
		} else if (userAgent.contains("opr") || userAgent.contains("opera")) {
			if (userAgent.contains("opera")) {
				return (userAgent.substring(userAgent.indexOf("opera")).split(" ")[0]).split("/")[0]+ "-"+ (userAgent.substring(userAgent.indexOf("version")).split(" ")[0]).split("/")[1];
			}else if (userAgent.contains("opr")) {
				return ((userAgent.substring(userAgent.indexOf("opr")).split(" ")[0]).replace("/", "-")).replace("opr","opera");
			}
		} else if (userAgent.contains("chrome")) {
			return (userAgent.substring(userAgent.indexOf("chrome")).split(" ")[0]).replace("/", "-");
		} else if ((userAgent.indexOf("mozilla/7.0") > -1)|| (userAgent.indexOf("netscape6") != -1)|| (userAgent.indexOf("mozilla/4.7") != -1)|| (userAgent.indexOf("mozilla/4.78") != -1)|| (userAgent.indexOf("mozilla/4.08") != -1)|| (userAgent.indexOf("mozilla/3") != -1)) {
			return "netscape-?";
		} else if (userAgent.contains("firefox")) {
			return (userAgent.substring(userAgent.indexOf("firefox")).split(" ")[0]).replace("/", "-");
		} else if (userAgent.contains("rv")) {
			String IEVersion = (userAgent.substring(userAgent.indexOf("rv")).split(" ")[0]).replace("rv:", "-");
			return "ie" + IEVersion.substring(0, IEVersion.length() - 1);
		}
		return "UnKnown, More-Info: " + userAgent;
	}
	
	public static void main(String[] args) {

	}

}
