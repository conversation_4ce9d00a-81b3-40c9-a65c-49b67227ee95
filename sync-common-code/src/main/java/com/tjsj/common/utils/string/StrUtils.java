/*
 * Copyright Notice:
 *      Copyright  1998-2008, Huawei Technologies Co., Ltd.  ALL Rights Reserved.
 *
 *      Warning: This computer software sourcecode is protected by copyright law
 *      and international treaties. Unauthorized reproduction or distribution
 *      of this sourcecode, or any portion of it, may result in severe civil and
 *      criminal penalties, and will be prosecuted to the maximum extent
 *      possible under the law.
 */
package com.tjsj.common.utils.string;

/**
 * StrUtils
 *
 * <AUTHOR>
 * @date 2024/07/20
 * @description 字符串util
 */
public class StrUtils {

    /**
     * str为null或为空
     *
     * @param str str
     * @return boolean
     * <AUTHOR>
     * @date 2024/07/20
     */
    public static boolean nullOrEmpty(String str) {
        return (null == str || str.trim().isEmpty());
    }

    /**
     * 首字母大写
     *
     * @param str 字符串
     * @return String 首字母大写后的字符串；如果输入为null或空，返回原字符串
     */
    public static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }

    /**
     * 去除字符串中的末尾的零
     * <p>
     *     例如：1.000-43.015 -> 1-43.015
     * </p>
     *
     * @param str 字符串
     * @return String 去除末尾零后的字符串；如果输入为null或空，返回原字符串
     * <AUTHOR> Ye
     * @date 2024/08/25
     */
    public static String discardZeros(String str) {
        if (StrUtils.nullOrEmpty(str)) {
            return str;
        }
        // 使用正则表达式去除小数点后面多余的0
        return str.replaceAll("(\\d+\\.\\d*?[1-9])0+\\b", "$1")
                .replaceAll("(\\.0+\\b)", "");
    }

    public static void main(String[] args) {
        System.out.println(discardZeros("1.000-43.015"));
        System.out.println(discardZeros("123.4500"));
        System.out.println(discardZeros("100.00"));
        System.out.println(discardZeros("0.0000"));
        System.out.println(discardZeros("123.004500"));
    }

}