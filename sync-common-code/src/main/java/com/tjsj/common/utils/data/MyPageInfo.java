package com.tjsj.common.utils.data;

import com.github.pagehelper.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * MyPageInfo
 *
 * <AUTHOR>
 * @date 2024/07/14
 * @description 自定义通用分页信息
 */
@Data
@Accessors(chain = true)
@Schema(description = "自定义通用分页信息")
@Builder
@NoArgsConstructor
public class MyPageInfo<T> {

    @Schema(description = "记录列表")
    @NotNull(message = "记录列表不能为null")
    private List<T> records;

    @Schema(description = "总记录数")
    @NotNull(message = "总记录数不能为null")
    private long total;

    @Schema(description = "每页记录数")
    @NotNull(message = "每页记录数不能为null")
    private long size;

    @Schema(description = "当前页码")
    @NotNull(message = "当前页码不能为null")
    private long current;

    public MyPageInfo(List<T> records, long total, long size, long current) {
        this.records = records;
        this.total = total;
        this.size = size;
        this.current = current;
    }

    /**
     * 我页面信息
     *
     * @param page 页
     * <AUTHOR> Ye
     * @date 2024/07/14
     */
    public MyPageInfo(Page<T> page) {
        if (page != null) {
            this.total = page.getTotal();
            this.size = page.getPageSize();
            this.current = page.getPageNum();
        }
    }


    /**
     * 构建页面信息
     *
     * @param page 页
     * @param list 列表
     * @return {@link MyPageInfo }<{@link T }>
     * <AUTHOR> Ye
     * @date 2024/07/14
     */
    public static <T> MyPageInfo<T> buildPageInfo(Page<T> page, List<T> list) {
        MyPageInfo<T> pageInfo = new MyPageInfo<>();
        if (page != null) {
            pageInfo.total = page.getTotal();
            pageInfo.size = page.getPageSize();
            pageInfo.current = page.getPageNum();

        }
        pageInfo.setRecords(list);
        return pageInfo;
    }
}
