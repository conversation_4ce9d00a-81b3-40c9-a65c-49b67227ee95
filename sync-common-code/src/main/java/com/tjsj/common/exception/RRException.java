package com.tjsj.common.exception;

import lombok.Getter;
import org.apache.commons.httpclient.HttpStatus;

/**
 * 自定义异常
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年10月27日 下午10:11:27
 */
@Getter
public class RRException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private final String msg;
    private final int code;

    public RRException(String msg) {
        this(msg, HttpStatus.SC_INTERNAL_SERVER_ERROR);
    }

    public RRException(String msg, Throwable e) {
        this(msg, HttpStatus.SC_INTERNAL_SERVER_ERROR, e);
    }

    public RRException(String msg, int code) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }

    public RRException(String msg, int code, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }
}

