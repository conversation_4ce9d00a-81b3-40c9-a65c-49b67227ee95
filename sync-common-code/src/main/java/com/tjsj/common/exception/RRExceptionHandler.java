package com.tjsj.common.exception;

import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.tjsj.common.utils.data.R;
import com.tjsj.common.utils.string.StrUtils;
import com.tjsj.modules.log.mapper.LogExceptionMapper;
import com.tjsj.modules.log.model.entity.LogException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.io.EOFException;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

/**
 * 异常处理器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年10月27日 下午10:16:19
 */
@RestControllerAdvice
public class RRExceptionHandler {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private LogExceptionMapper tLogExceptionMapper;


    private String getApplicationName(Exception e) {
        return e.getMessage() == null ? "" : e.getMessage();
//		return new StringBuilder(env.getProperty("spring.application.name")).append("模块：").append(TokenUtil.getRequest
//		().getRequestURI()).append("请求 ").toString();
    }

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(RRException.class)
    public R handleRRException(RRException e) {
        R r = new R();
        r.put("code", e.getCode());
        r.put("msg", e.getMessage());
        return r;
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public R handlerNoFoundException(Exception e) {
        logger.error(e.getMessage(), e);
        String applicationName = getApplicationName(e);
        if (!StrUtils.nullOrEmpty(applicationName)) {
            LogException tLogException = new LogException().setException(applicationName);
            //tLogExceptionMapper.insert(tLogException);
        }
        return R.error("服务器繁忙，请稍后重试");
        //return R.error(HttpStatus.SC_NOT_FOUND, getApplicationName(e) + "路径不存在，请检查路径是否正确");
    }

    @ExceptionHandler(DuplicateKeyException.class)
    public R handleDuplicateKeyException(DuplicateKeyException e) {
        logger.error(e.getMessage(), e);
        String applicationName = getApplicationName(e);
        if (!StrUtils.nullOrEmpty(applicationName)) {
            LogException tLogException = new LogException().setException(applicationName);
            //tLogExceptionMapper.insert(tLogException);
        }
        return R.error("服务器繁忙，请稍后重试");
        //return R.error(getApplicationName(e) + "数据库中已存在该记录");
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        BindingResult result = e.getBindingResult();
        if (result.hasErrors()) {
            logger.error(e.getMessage(), e);
            return R.error(result.getAllErrors().get(0).getDefaultMessage());
        }
        return R.error();
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public R handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        InvalidFormatException formatException = (InvalidFormatException) e.getCause();
        List<JsonMappingException.Reference> ex = formatException.getPath();
        String fieldName = "";
        for (JsonMappingException.Reference reference : ex) {
            fieldName = reference.getFieldName();
        }
        return R.error(fieldName + "类型错误");
    }

    @ExceptionHandler(Exception.class)
    public R handleException(Exception e) {
        logger.error(e.getMessage(), e);
        String applicationName = getApplicationName(e);
        if (!StrUtils.nullOrEmpty(applicationName)) {
            LogException tLogException = new LogException().setException(applicationName);
            //tLogExceptionMapper.insert(tLogException);
        }
        return R.error("服务器繁忙，请稍后重试");
        //return R.error(getApplicationName(e) + "发生异常：服务器内部错误！");
    }

    @ExceptionHandler(NullPointerException.class)
    public R handleNullPointerException(Exception e) {
        logger.error(e.getMessage(), e);
        String applicationName = getApplicationName(e);
        if (!StrUtils.nullOrEmpty(applicationName)) {
            LogException tLogException = new LogException().setException(applicationName);
            //tLogExceptionMapper.insert(tLogException);
        }
        return R.error("服务器繁忙，请稍后重试");
        //return R.error(getApplicationName(e) + "空指针异常：服务器内部错误！");
    }

    @ExceptionHandler(ArrayIndexOutOfBoundsException.class)
    public R handleArrayIndexOutOfBoundsException(Exception e) {
        logger.error(e.getMessage(), e);
        String applicationName = getApplicationName(e);
        if (!StrUtils.nullOrEmpty(applicationName)) {
            LogException tLogException = new LogException().setException(applicationName);
            //tLogExceptionMapper.insert(tLogException);
        }
        return R.error("服务器繁忙，请稍后重试");
        //return R.error(getApplicationName(e) + "数组下标越界异常：");
    }

    @ExceptionHandler(EOFException.class)
    public R handleEOFException(Exception e) {
        logger.error(e.getMessage(), e);
        return R.error(getApplicationName(e) + "文件已结束异常：");
    }

    @ExceptionHandler(FileNotFoundException.class)
    public R handleFileNotFoundException(Exception e) {
        logger.error(e.getMessage(), e);
        return R.error(getApplicationName(e) + "文件未找到异常：");
    }

    @ExceptionHandler(NumberFormatException.class)
    public R handleNumberFormatException(Exception e) {
        logger.error(e.getMessage(), e);
        return R.error(getApplicationName(e) + "字符串转换为数字异常：");
    }

    @ExceptionHandler(SQLException.class)
    public R handleSQLException(Exception e) {
        logger.error(e.getMessage(), e);
        return R.error(getApplicationName(e) + "操作数据库异常：");
    }

    @ExceptionHandler(IOException.class)
    public R handleIOException(Exception e) {
        logger.error(e.getMessage(), e);
        return R.error(getApplicationName(e) + "输入输出异常：");
    }

    @ExceptionHandler(NoSuchMethodException.class)
    public R handleNoSuchMethodException(Exception e) {
        logger.error(e.getMessage(), e);
        return R.error(getApplicationName(e) + "方法未找到异常：");
    }
}
