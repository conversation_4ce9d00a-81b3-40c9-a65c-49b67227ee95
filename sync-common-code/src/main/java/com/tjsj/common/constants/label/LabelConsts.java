package com.tjsj.common.constants.label;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/5 11:54
 * @description 标签相关常量
 */
@Schema(name = "LabelConsts", description = "标签相关常量")
public class LabelConsts {

    public static final String QUERY_STOCK_ID_BY_LABEL_NAME = "stockList";

    public static final String QUERY_STOCK_INFO_BY_LABEL_NAME = "stocksInfo";


    public static final Integer LABEL_STRONG_RELATED = 1;

    public static final Integer LABEL_MIDDLE_RELATED = 2;

    public static final BigDecimal LABEL_MIDDLE_RELATED_VALUE = new BigDecimal("0.8");

    /**
     * 隐藏标签列表
     */
    public static final List<String> HIDDEN_LABEL_LIST = Arrays.asList("央企", "民企", "国企");

    /**
     * 自定义标签分析-市值区间
     */
    public static final String CUSTOM_LABEL_ANL_TTM = "ttm";

    /**
     * 标签历史记录
     */
    public static final String LABEL_HISTORY = "updateType-labelHistory";

    /**
     * 标签最新
     */
    public static final String LABEL_LAST_NEW = "updateType-labelLastNew";
}
