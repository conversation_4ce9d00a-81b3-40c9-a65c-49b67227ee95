package com.tjsj.common.constants;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

/**
 * LevelSrtConsts
 *
 * <AUTHOR>
 * @date 2024/7/13 19:32
 * @description 评级策略相关常量
 */
@Data
@Accessors(chain = true)
@Schema(name = "LevelSrtConsts", description = "评级策略相关常量")
public class LevelSrtConsts {

    /**
     * 评级策略类型-压力测试策略
     */
    public static final Integer LEVEL_SRT_TYPE_PRESSURE_TEST = 6;

    /**
     * 策略类型-非参与评级列表
     */
    public static final List<Integer> SRT_TYPE_NON_LEVEL_LIST = Collections.singletonList(LEVEL_SRT_TYPE_PRESSURE_TEST);

    /**
     * 查询类型-股票列表
     */
    public static final String QUERY_TYPE_STOCK_LIST = "stockList";

    /**
     * 查询类型-股票分析
     */
    public static final String QUERY_TYPE_STOCK_ANALYSIS = "stockAnalysis";
    /**
     * 当前股票用户无策略时-显示内容
     */
    public static final String NO_USER_STRATEGY_SHOW = "**无评级策略**";

    /**
     * 当前股票用户无评级时-显示内容
     */
    public static final String NO_USER_LEVEL_SHOW = "未评级";

    /**
     * 策略池或标签池分析-全部策略或标签
     */
    public static final String POOL_ANL_TYPE_ALL = "all";

    /**
     * 策略池或标签池分析-已使用策略或标签
     */
    public static final String POOL_ANL_TYPE_USED = "used";

    /**
     * 策略池或标签池分析-我的
     */
    public static final String POOL_ANA_TYPE_MINE = "mine";

    /**
     * 评级策略调整日志-调整前
     */
    public static final Integer LEVEL_SRT_LOG_TYPE_OLD = 0;

    /**
     * 评级策略调整日志-调整后
     */
    public static final Integer LEVEL_SRT_LOG_TYPE_NEW = 1;

    /**
     * API接口-保存评级策略
     */
    public static final String API_SAVE_LEVEL_SRT = "saveLevelSrt";

    /**
     * API接口-取评级策略证券
     */
    public static final String API_GET_LEVEL_SRT_SEC = "getLevelSrtSec";

    /**
     * 用户评级策略备份-最大值大小
     */
    public static final Integer USER_BACKUP_MAX_SIZE = 3;

}
