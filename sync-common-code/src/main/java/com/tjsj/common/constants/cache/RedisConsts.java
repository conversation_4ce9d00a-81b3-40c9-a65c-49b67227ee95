package com.tjsj.common.constants.cache;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * RedisConsts
 *
 * <AUTHOR>
 * @date 2024/7/3 14:34
 * @description redis常量
 */
@Schema(description = "redis常量")
public class RedisConsts {

    /**
     * RedisConsts防止实例化
     *
     * <AUTHOR>
     * @date 2024/07/20
     */
    private RedisConsts() {
    }


    /**
     * redis钥匙前缀
     */
    public static final String REDIS_KEY_PREFIX = "tjsj_";


    /**
     * 锁前缀
     */
    public static final String KEY_LOCK = RedisConsts.REDIS_KEY_PREFIX + "lock:";


}
