package com.tjsj.common.constants;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * CalConsts
 *
 * <AUTHOR>
 * @date 2024/7/18 9:55
 * @description 计算相关常量
 */
@Schema(description = "计算相关常量")
public class CalConsts {

    /**
     * 计算类型-股票板块
     */
    public static final String CALCULATE_STOCK_TYPE_BLOCK = "block";

    /**
     * 计算分数类型财务
     */
    public static final String CALCULATE_SCORE_TYPE_FINANCIAL = "financialScore";

    /**
     * 计算分数类型总计
     */
    public static final String CALCULATE_SCORE_TYPE_TOTAL = "totalScore";

    /**
     * 计算财务类型-意见
     */
    public static final String CALCULATE_FINANCIAL_TYPE_OPINION = "opinion";

    /**
     * 计算财务类型-意见cn
     */
    public static final String CALCULATE_FINANCIAL_TYPE_OPINION_CN = "意见";

    /**
     * 计算矩阵-单元格的分隔标识
     */
    public static final String CALCULATE_MATRIX_CELL_SPLIT = "~!@";

    /**
     * 计算矩阵-横坐标与纵坐标的分隔标识
     */
    public static final String CALCULATE_MATRIX_ACROSS_VERTICAL_SPLIT = "!@#";

    /**
     * 计算策略-自动
     */
    public static final String CAL_STRATEGY_AUTO = "auto";

    /**
     * 计算策略-手动
     */
    public static final String CAL_STRATEGY_MANUAL = "manual";

    public static final String CALCULATE_ALL = "all";

    public static final String CALCULATE_PART = "part";

    /**
     * 计算证券评分类型-基本面因子
     */
    public static final String CAL_SCORE_TYPE_BASIC = "jbmyz";

    /**
     * 计算证券评分类型-风险因子
     */
    public static final String CAL_SCORE_TYPE_RISK = "fxyz";
}
