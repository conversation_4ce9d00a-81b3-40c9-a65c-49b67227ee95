package com.tjsj.common.constants.label;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * LabelNameConsts
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/7 16:58
 * @description 标签名称常量
 */
@Schema(description = "标签名称常量")
public class LabelNameConsts {

    // =================== 财务风险相关标签 ===================

    /**
     * 高商誉
     */
    public static final String HIGH_GOOD_WILL = "高商誉";

    /**
     * 高负债
     */
    public static final String HIGH_DEBT = "高负债";

    /**
     * 业绩跳水
     */
    public static final String PERFORMANCE_SLIP = "业绩跳水";

    /**
     * 净现比异常
     */
    public static final String PROFIT_CASHFLOW_RATIO_ABNORMAL = "净现比异常";

    /**
     * 资产异常
     */
    public static final String ABNORMAL_ASSET = "资产异常";

    /**
     * 毛利率异常
     */
    public static final String ABNORMAL_GROSS_MARGIN = "毛利率异常";

    /**
     * 财务费用增长率异常
     */
    public static final String FINANCIAL_EXPENSES_GROWTH_RATE_ABNORMAL = "财务费用增长率异常";

    /**
     * 研发费用增长率异常
     */
    public static final String RD_EXPENSE_GROWTH_RATE_ABNORMAL = "研发费用增长率异常";

    /**
     * 研发资本化率较高
     */
    public static final String RD_CAPITALIZATION_RATE_HIGH = "研发资本化率较高";

    /**
     * 年报亏损
     */
    public static final String YEAR_END_LOSS = "年报亏损";


    // =================== 交易风险相关标签 ===================

    /**
     * <b>交易所风险警示</b>
     */
    public static final String EXCHANGE_RISK_ALERT = "交易所风险警示";

    /**
     * <b>被动减持</b>
     */
    public static final String PASSIVE_SHARE_REDUCTION = "被动减持";

    /**
     * <b>股东减持</b>
     */
    public static final String SHAREHOLDER_REDUCTION = "股东减持";

    /**
     * <b>年内停牌</b>
     */
    public static final String TRADING_HALT_IN_ONE_YEAR = "年内停牌";

    /**
     * <b>年内停牌风险</b>
     */
    public static final String TRADING_HALT_RISK_IN_ONE_YEAR = "年内停牌风险";

    /**
     * <b>停牌时长前十名</b>
     */
    public static final String TOP_TEN_HALT_DURATION = "停牌时长前十名";

    /**
     * <b>退市公告</b>
     */
    public static final String DELISTING_ANNOUNCEMENT = "退市公告";

    /**
     * <b>可能终止上市公告</b>
     */
    public static final String POSSIBLE_DELISTING_ANNOUNCEMENT = "可能终止上市公告";

    /**
     * <b>披星带帽</b>
     */
    public static final String STARRED_AND_HATTED = "披星带帽";

    /**
     * <b>摘星脱帽</b>
     */
    public static final String UN_STARRED_AND_UN_HATTED = "摘星脱帽";

    /**
     * <b>ST、*ST、退</b>
     */
    public static final String ST_OR_STAR_OR_DELISTING = "ST、*ST、退";

    // =================== 关联方风险相关标签 ===================

    /**
     * 开通关闭两融账户
     */
    public static final String OPEN_CLOSE_MRG_TRD_ACCOUNT = "开通关闭两融账户";

    // =================== 经营风险相关标签 ===================

    /**
     * 年报披露期延迟
     */
    public static final String YEAR_END_DISCLOSURE_DELAY = "年报披露期延迟";

    // =================== 另类风险相关标签 ===================

    /**
     * 同业调整
     */
    public static final String PEER_SECURITIES_ADJUST = "同业调整";

    // =================== 债务风险相关标签 ===================

    /**
     * 高额担保
     */
    public static final String HIGH_GUARANTEE = "高额担保";

    /**
     * 担保损失
     */
    public static final String GUARANTEE_LOSS = "担保损失";

    /**
     * 债券违约
     */
    public static final String BOND_DEFAULT = "债券违约";

}
