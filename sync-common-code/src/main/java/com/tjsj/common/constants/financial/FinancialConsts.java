package com.tjsj.common.constants.financial;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * FinancialConsts
 *
 * <AUTHOR>
 * @date 2024/7/8 21:05
 * @description 财务相关常量
 */
@Schema(name = "FinancialConsts", description = "财务相关常量")
public class FinancialConsts {

    /**
     * 第一季度
     */
    public static final String FIRST_QUARTER = "-03-31";

    /**
     * 第二季度
     */
    public static final String SECOND_QUARTER = "-06-30";

    /**
     * 第三季度
     */
    public static final String THIRD_QUARTER = "-09-30";


    /**
     * 第四季度
     */
    public static final String FOURTH_QUARTER = "-12-31";

    /**
     * 每季度日期
     */
    public static final List<String> PER_QUARTER_DATES = Arrays.asList(FIRST_QUARTER, SECOND_QUARTER, THIRD_QUARTER,
            FOURTH_QUARTER);

    /**
     * 半年度日期
     */
    public static final List<String> PER_HALF_YEAR_DATES = Arrays.asList(SECOND_QUARTER, FOURTH_QUARTER);

    /**
     * 年度日期
     */
    public static final List<String> PER_YEAR_DATES = Collections.singletonList(FOURTH_QUARTER);


    /**
     * 财务日期季度日期
     */
    public static final String T_FINANCIAL_DATE_QUARTER_DATE = "QuaterDate";

    /**
     * 财务日期年份日期
     */
    public static final String T_FINANCIAL_DATE_YEAR_DATE = "YearDate";

    /**
     * 财务日期半年日期
     */
    public static final String T_FINANCIAL_DATE_HALF_YEAR_DATE = "HalfYearDate";

    /**
     * 财务第三季度日期
     */
    public static final String T_FINANCIAL_THIRD_QUARTER_DATE = "ThirdQuaterDate";

    /**
     * 财务项中位数-一级行业
     */
    public static final Integer FIN_MID_VALUE_TIER_ONE = 1;

    /**
     * 财务项中位数-二级行业
     */
    public static final Integer FIN_MID_VALUE_TIER_TWO = 2;

    /**
     * 财务数据中位数自动更新--起始日期
     */
    public static final String MID_VALUE_AUTO_UPDATE_MIN_DATE = "2020-12-31";

    public static final String FIN_ITEM_PREFIX = "data";

    /**
     * 财务数据比值-符号判断位
     */
    public static final String FIN_ITEM_RATIO_SYMBOL = ",";

    /**
     * 资产负债表-表名称
     */
    public static final String BAL_SHEET_TABLE_NAME = "t_financial3";

    /**
     * 利润表-表名称
     */
    public static final String INCOME_STATEMENT_TABLE_NAME = "t_financial4";

    /**
     * 现金流量表-表名称
     */
    public static final String CASH_FLOW_STATEMENT_TABLE_NAME = "t_financial5";


    /**
     * 财务摘要(报告期)-表名称
     */
    public static final String FINANCIAL_DIGEST_REPORT_TABLE_NAME = "t_financial1";

    /**
     * 年报标准无保留意见
     */
    public static final String UNMODIFIED_OPINION = "标准无保留意见";


}
