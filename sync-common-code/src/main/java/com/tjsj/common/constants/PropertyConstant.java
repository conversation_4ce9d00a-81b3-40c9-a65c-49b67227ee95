package com.tjsj.common.constants;

/**
 * 和权限相关的实体中的关键字段(实体中的property名称（注意不是数据库字段名称）)的常量类
 */
public class PropertyConstant {
    //以下都是实体中的property名称（注意不是数据库字段名称）专门用于Example的参数构造
    public static final String MODULEID = "moduleId";
    public static final String UID = "uid";
    public static final String WORKNO = "workno";
    public static final String DEPID = "depid";
    public static final String MENUID = "menuId";
    public static final String TOKEN = "token";
    public static final String ROLEID = "roleId";
    public static final String DEPTROLEID = "deptRoleId";
//---------------------------------------------------------------------------------------------------------------
    //以下都是数据库字段名称（注意不是实体中的property名称）专门用于mybitis-plus的参数构造
    public static final String MODULE_ID = "module_id";
    public static final String MENU_ID = "menu_id";
    public static final String DEPT_ID = "dept_id";
    public static final String DEPT_ROLE_ID = "dept_role_id";
    public static final String ROLE_ID = "role_id";
    public static final String USER_ID = "user_id";
    public static final String ADMINID = "adminid";
}
