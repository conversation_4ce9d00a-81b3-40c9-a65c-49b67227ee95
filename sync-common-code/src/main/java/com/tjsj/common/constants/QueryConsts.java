package com.tjsj.common.constants;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * QueryConsts
 *
 * <AUTHOR>
 * @date 2024/7/14 13:02
 * @description 查询相关常量
 */
@Schema(description = "查询相关常量")
public class QueryConsts {


    public static final List<String> SORT_TYPE_PEER_LEVEL_LIST =
            Arrays.asList("peerLevel", "oldPeerLevel", "nowPeerLevel", "secCategory");

    public static final List<String> SORT_TYPE_SYSTEM_LEVEL_LIST =
            Arrays.asList("userLevel", "afterLevel", "level", "newLevel", "oldLevel");

    public static final List<String> SORT_TYPE_LEVEL_LIST =
            Collections.unmodifiableList(Stream.concat(
                            SORT_TYPE_PEER_LEVEL_LIST.stream(),
                            SORT_TYPE_SYSTEM_LEVEL_LIST.stream())
                    .collect(Collectors.toList()));

}
