package com.tjsj.common.constants;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Arrays;
import java.util.List;

/**
 * StockConsts
 *
 * <AUTHOR>
 * @date 2024/7/13 15:33
 * @description 证券详情相关常量
 */
@Schema(description = "证券详情相关常量")
public class StockConsts {

    /**
     * 证券行情平均值表-平均日期选择列表
     */
    public final static List<Integer> STOCK_MARKET_DATE_RANGE_LIST =
            Arrays.asList(1, 15, 30, 45, 60, 90);

    /**
     * 证券行情中位数表-一级行业
     */
    public final static Integer MARKET_MID_VAL_INDUSTRY_ONE = 1;

    /**
     * 证券行情中位数表-二级行业
     */
    public final static Integer MARKET_MID_VAL_INDUSTRY_TWO = 2;

}
