package com.tjsj.common.constants;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * ManageConsts
 *
 * <AUTHOR>
 * @date 2024/7/11 19:01
 * @description - 管理功能常量
 */
@Schema(description = "管理功能常量")
public class ManageConsts {

    /**
     * 基本服务
     */
    public static final String BASIC_SERVICE = "commons-code";

    /**
     * dev配置文件
     */
    public static final String DEV_PROFILE = "dev";

    /**
     * prod配置文件
     */
    public static final String PROD_PROFILE = "prod";

    /**
     * test配置文件
     */
    public static final String TEST_PROFILE = "test";

    /**
     * 本地配置文件
     */
    public static final String LOCAL_PROFILE = "local";

    /**
     * 默认配置文件
     */
    public static final String DEFAULT_PROFILE = "default";


    /**
     * 塔金6r数据库
     */
    public static final String TARKIN_6R_DB = "tarkin_6r";

    // ================================数据库schema名称=================================

    public static final String SCHEMA_CREDIT = "credit";

    public static final String SCHEMA_MARGIN = "margin";

    public static final String SCHEMA_LABEL = "label";

    public static final String SCHEMA_PLEDGEDATA = "pledgedata";

    public static final String SCHEMA_PUSHDATA = "pushdata";

    public static final String SCHEMA_TARKIN = "tarkin";

    public static final String SCHEMA_TEST = "test";

    public static final String SCHEMA_TJ_MIDDLE_GROUND = "tj_middle_ground";


    /**
     * 同步数据心跳测试-华龙证券
     */
    public static final String HEART_BEAT_TEST_HLZQ = "hlzq";

    /**
     * 表类型-评级策略
     */
    public static final String TABLE_LEVEL_STRATEGY = "level_strategy";

    /**
     * 表类型-矩阵
     */
    public static final String TABLE_MATRIX = "matrix";

    /**
     * 表类型-日志
     */
    public static final String TABLE_LOG = "log";

    /**
     * 表类型-同业数据
     */
    public static final String TABLE_PEER_DATA = "peer_data";

    /**
     * 表类型-股票分数
     */
    public static final String TABLE_STOCK_SCORE = "stock_score";

    /**
     * 表类型-融资融券
     */
    public static final String TABLE_MARGIN_TRADING = "margin_trading";

    /**
     * 表类型—股票评级
     */
    public static final String TABLE_STOCK_LEVEL = "stock_level";

    /**
     * 表类型-用户
     */
    public static final String TABLE_USER = "user";

    /**
     * 表类型-风险标签
     */
    public static final String TABLE_RISK_LABEL = "risk_label";
}
