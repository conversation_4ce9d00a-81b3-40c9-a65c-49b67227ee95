package com.tjsj.common.constants;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/3 22:47
 * @description 同业券商常量
 */
@Schema(description = "同业券商常量")
public class PeerConsts {

    public static final String TF_SECURITIES_CN = "天风证券";

    /**
     * 同业券商数据表名统一前缀
     */
    public static final String PEER_PREFIX = "t_rzrq_";

    /**
     * 同业券商数据表名统一前缀
     */
    public static final String HIS_PEER_PREFIX = "t_margin_";

    /**
     * 折算率表后缀
     */
    public static final String COLLATERAL_SUFFIX = "_zsl";


    public static final String COLLATERAL_HIS_SUFFIX = "_collateral";

    /**
     * 集中度表后缀
     */
    public static final String CONCENTRATION_SUFFIX = "_jzd";

    public static final String CATEGORY_HIS_SUFFIX = "_category";

    /**
     * 标的证券后缀
     */
    public static final String UNDERLYING_SUFFIX = "_bdzq";

    public static final String UNDERLYING_HIS_SUFFIX = "_underlying";


    /**
     * 后缀表名列表
     */
    public static final List<String> SUFFIX_LIST = Arrays.asList(COLLATERAL_SUFFIX,
            CONCENTRATION_SUFFIX, UNDERLYING_SUFFIX);


    public static final String PEER_COLLATERAL_HAIRCUT_DOWN = "同业折算率调低";

    public static final String PEER_COLLATERAL_HAIRCUT_UP = "同业折算率调高";

    public static final String PEER_PEER_LEVEL_ADJUST = "同业评级调整";

    public static final String PEER_PEER_LEVEL_OUT = "同业评级调出";

    public static final String PEER_PEER_LEVEL_DOWN = "同业评级调低";

    public static final String PEER_PEER_LEVEL_UP = "同业评级调高";

    public static final String PEER_COLLATERAL_OUT = "同业担保品调出";

    public static final String PEER_MARGIN_SECURITY_OUT = "同业调出融资标的";

    public static final String PEER_SHORT_SECURITY_OUT = "同业调出融券标的";

    public static final String PEER_MARGIN_REQUIREMENT_DOWN = "同业融资保证金比例调低";

    public static final String PEER_SHORT_REQUIREMENT_DOWN = "同业融券保证金比例调低";

    public static final String PEER_MARGIN_REQUIREMENT_UP = "同业融资保证金比例调高";

    public static final String PEER_SHORT_REQUIREMENT_UP = "同业融券保证金比例调高";

    /**
     * 调整标准一
     */
    public static final String ADJUST_CRITERIA_ONE = "调整";

    /**
     * 调整标准二
     */
    public static final String ADJUST_CRITERIA_TWO = "调整明细";

    public static final String ADJUST_TYPE_COLLATERAL = "担保品";

    public static final String ADJUST_TYPE_COLLATERAL_PARAM = "dbp";

    public static final String ADJUST_TYPE_UNDERLYING_SECURITY = "标的券";

    public static final String ADJUST_TYPE_UNDERLYING_SECURITY_PARAM = "bdzq";

    /**
     * 同业证券调整-调整开始日期
     */
    public static final String ADJUST_START_DATE = "startDate";


    /**
     * 同业证券调整-调整结束日期
     */
    public static final String ADJUST_END_DATE = "endDate";

    /**
     * 同业证券状态-正常
     */
    public static final Integer PEER_SECURITIES_STATUS_NORMAL = 1;

    /**
     * 同业证券状态-维护中
     */
    public static final Integer PEER_SECURITIES_STATUS_MAINTENANCE = 2;

    /**
     * 同业证券状态-开发中
     */
    public static final Integer PEER_SECURITIES_STATUS_DEVELOPING = 3;


    /**
     * 维持担保比例-无负债
     */
    public static final String MAINTAIN_RATIO_NO_DEBT = "无负债";

    /**
     * 网格类型类型-同业集中度策略
     */
    public static final String GRID_TYPE_TYPE_PEER_STRATEGY = "同业集中度策略";

}
