package com.tjsj.common.annotation.authorize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * SystemUserOnly
 *
 * <AUTHOR>
 * @date 2024/07/17
 * @description 系统用户权限
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface SystemUserOnly {
    boolean required() default true;
}