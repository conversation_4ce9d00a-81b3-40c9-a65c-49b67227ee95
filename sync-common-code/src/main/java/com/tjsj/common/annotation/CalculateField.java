package com.tjsj.common.annotation;

import com.tjsj.common.enums.base.DataUnit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * CalculateField
 *
 * <AUTHOR> Ye
 * @date 2024/7/17 11:13
 * @description 用于计算的属性注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface CalculateField {

    String value() default "";

    /**
     * 对应实际数据库中字段名称
     *
     * @return {@link String }
     * <AUTHOR> Ye
     * @date 2024/09/10
     */
    String fieldName() default "";

    /**
     * 单位
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/09/10
     */
    DataUnit dataUnit() default DataUnit.ONE;
}
