package com.tjsj.common.annotation.sync;

import com.tjsj.common.annotation.authorize.PassToken;
import org.springframework.core.annotation.Order;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/26 9:16
 * @description Cron定时同步任务注解
 * 用在属性上，标识该属性是Cron定时同步任务用到的属性
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface CronSyncConfig {
}
