package com.tjsj.common.annotation.authorize;

import com.tjsj.common.enums.base.SysFunctionType;
import io.swagger.v3.oas.annotations.media.Schema;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * FunctionOnly
 *
 * <AUTHOR>
 * @date 2024/9/4 11:25
 * @description 仅允许准许的功能用户
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Schema(name = "FunctionOnly", description = "仅允许准许的功能用户")
public @interface FunctionOnly {
    SysFunctionType[] allow() default {};
}
