package com.tjsj.common.annotation.authorize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ExpertOnly
 *
 * <AUTHOR> Ye
 * 只限专家评级
 * @date 2024/07/17
 * @description 仅限专家
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExpertOnly {
    boolean required() default true;
}
