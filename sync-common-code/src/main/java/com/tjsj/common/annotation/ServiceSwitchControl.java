package com.tjsj.common.annotation;

import java.lang.annotation.*;

/**
 * ServiceSwitchControl
 *
 * <AUTHOR>
 * @date 2025/06/30
 * @description 服务开关控制注解 - 当服务总开关为1（停用）时，被此注解标记的方法将不执行
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ServiceSwitchControl {
    
    /**
     * 描述信息，用于日志记录
     * 
     * @return 描述信息
     */
    String value() default "";
    
    /**
     * 是否启用服务开关控制
     * 
     * @return true-启用控制，false-不启用控制
     */
    boolean enabled() default true;
}
