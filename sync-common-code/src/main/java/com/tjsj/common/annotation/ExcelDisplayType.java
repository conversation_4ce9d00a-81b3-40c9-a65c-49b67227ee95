package com.tjsj.common.annotation;

import com.tjsj.common.enums.base.EnumDisplayType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ExcelDisplayType
 *
 * <AUTHOR>
 * @date 2024/08/15
 * @description excel显示类型
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelDisplayType {
    EnumDisplayType value();
}
