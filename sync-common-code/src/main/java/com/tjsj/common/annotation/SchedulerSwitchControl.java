package com.tjsj.common.annotation;

import java.lang.annotation.*;

/**
 * SchedulerSwitchControl
 *
 * <AUTHOR> Ye
 * @date 2025/07/01
 * @description 定时任务开关控制注解 - 当定时任务总开关为1（停用）时，被此注解标记的方法将不执行
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SchedulerSwitchControl {
    
    /**
     * 描述信息，用于日志记录
     * 
     * @return 描述信息
     */
    String value() default "";
    
    /**
     * 是否启用定时任务开关控制
     * 
     * @return true-启用控制，false-不启用控制
     */
    boolean enabled() default true;

    /**
     * 是否为Scheduler方法
     *
     * @return true-是定时任务方法，false-不是定时任务方法
     */
    boolean scheduled() default false;
}
