package com.tjsj.common.annotation.env;

import io.swagger.v3.oas.annotations.media.Schema;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * <AUTHOR>
 * @date 2024/8/3 20:24
 * @description 仅在当前服务中调用
 */
@Retention(RetentionPolicy.RUNTIME)
@Schema(name = "CurServiceOnly", description = "仅在当前服务中调用")
public @interface CurServiceOnly {
    @Schema(description = "服务名称")
    String value() default "";
}
