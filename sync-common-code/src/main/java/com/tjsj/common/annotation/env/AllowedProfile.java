package com.tjsj.common.annotation.env;

import com.tjsj.common.enums.base.ProfileTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * AllowedProfile
 *
 * <AUTHOR>
 * @date 2024/8/3 20:24
 * @description 准许的环境使用
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Schema(name = "AllowedProfile", description = "准许的使用环境")
public @interface AllowedProfile {
    ProfileTypeEnum[] allow() default {};
}
