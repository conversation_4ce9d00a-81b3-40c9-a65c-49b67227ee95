package com.tjsj.common.annotation;

/**
 * ReviewDate
 *
 * <AUTHOR> Ye
 * @date 2024/7/3 22:11
 * @description 标识项目中的类、方法的回顾检查日期，用于统计工作痕迹。
 * @version 1.1.0
 */
public @interface ReviewDate {

    /**
     * 回顾检查日期，支持单个或多个日期格式
     * 格式: yyyy-MM-dd HH:mm
     * @return 回顾日期数组
     */
    String[] reviewDates();

    /**
     * 描述字段，用于说明这些日期的意义
     * @return 描述信息
     */
    String[] description() default "";
}

