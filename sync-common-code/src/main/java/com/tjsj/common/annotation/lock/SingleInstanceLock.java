package com.tjsj.common.annotation.lock;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * SingleInstanceLock
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/4/11 15:40
 * @description 单实例锁注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SingleInstanceLock {

	/**
	 * 互斥锁对应的 key (相当于锁的名字)
	 */
	String key() default "";

	/**
	 * 等待获取锁的最大时间，默认100
	 * 时间单位由 waitTimeUnit 指定，默认为毫秒
	 * 若超过此时间还没拿到锁，抛异常或自行处理
	 */
	long waitTime() default 100L;

	/**
	 * 等待时间的时间单位，默认为毫秒
	 * 为了向后兼容，默认值保持为 MILLISECONDS
	 */
	TimeUnit waitTimeUnit() default TimeUnit.MILLISECONDS;

	/**
	 * 持有锁的租约时间，默认2分钟
	 * 超过此时间后锁自动释放(防止无限占用)
	 */
	long leaseTime() default 2L;

	/**
	 * 租约时间的时间单位，默认为分钟
	 */
	TimeUnit leaseTimeUnit() default TimeUnit.MINUTES;

}