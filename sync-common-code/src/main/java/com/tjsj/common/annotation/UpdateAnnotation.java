package com.tjsj.common.annotation;

import com.tjsj.common.annotation.authorize.PassToken;
import org.springframework.core.annotation.Order;

import java.lang.annotation.*;

/**
 * UpdateAnnotation
 * <p>
 *     使用此注解能够跳过token认证，不再需要@PassToken注解。
 * </p>
 *
 * <AUTHOR>
 * @date 2024/07/17
 * @description 自动更新注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Order(value = 0)
@PassToken
public @interface UpdateAnnotation {
}
