package com.tjsj.common.manager;

import com.tjsj.common.enums.base.CommonStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;

/**
 * ServiceSwitchManager
 *
 * <AUTHOR>
 * @date 2025/06/30
 * @description 服务开关管理器 - 统一管理服务运行状态
 */
@Component
@Slf4j
public class ServiceSwitchManager {

    /**
     * 当前服务状态（线程安全）
     *
     */
    private final AtomicReference<CommonStatus> currentServiceStatus =
            new AtomicReference<>(CommonStatus.ENABLE);

    /**
     * 当前定时任务状态（线程安全）
     */
    private final AtomicReference<CommonStatus> currentSchedulerStatus =
            new AtomicReference<>(CommonStatus.ENABLE);

    /**
     * 状态变更监听器列表（线程安全）
     */
    private final CopyOnWriteArrayList<ServiceSwitchListener> listeners = new CopyOnWriteArrayList<>();

    /**
     * 检查并更新服务状态
     *
     * @param newStatus 新的服务状态
     */
    public void checkAndUpdateStatus(CommonStatus newStatus) {
        CommonStatus oldStatus = currentServiceStatus.get();

        // 如果状态没有变化，直接返回
        if (oldStatus == newStatus) {
            return;
        }

        // 更新状态
        currentServiceStatus.set(newStatus);

        // 记录状态变更日志
        log.warn("╔═══════════════════════════════════════════════════════════════╗");
        log.warn("║                    服务状态发生变更                           ║");
        log.warn("╠═══════════════════════════════════════════════════════════════╣");
        log.warn("║ 原状态: {}                                    ║", oldStatus.getDescription());
        log.warn("║ 新状态: {}                                    ║", newStatus.getDescription());
        log.warn("║ 变更时间: {}                          ║", java.time.LocalDateTime.now());
        log.warn("╚═══════════════════════════════════════════════════════════════╝");

        // 通知所有监听器
        notifyListeners(oldStatus, newStatus);
    }

    /**
     * 检查并更新定时任务状态
     *
     * @param schedulerSwitch 定时任务开关
     */
    public void checkAndUpdateSchedulerStatus(CommonStatus schedulerSwitch) {
        CommonStatus oldStatus = currentSchedulerStatus.get();

        // 如果状态没有变化，直接返回
        if (oldStatus == schedulerSwitch) {
            return;
        }

        // 更新状态
        currentSchedulerStatus.set(schedulerSwitch);

        // 记录状态变更日志
        log.warn("╔═══════════════════════════════════════════════════════════════╗");
        log.warn("║                    定时任务状态发生变更                         ║");
        log.warn("╠═══════════════════════════════════════════════════════════════╣");
        log.warn("║ 原状态: {}                                    ║", oldStatus.getDescription());
        log.warn("║ 新状态: {}                                    ║", schedulerSwitch.getDescription());
        log.warn("║ 变更时间: {}                          ║", java.time.LocalDateTime.now());
        log.warn("╚═══════════════════════════════════════════════════════════════╝");

    }

    /**
     * 获取当前服务状态
     *
     * @return 当前服务状态
     */
    public CommonStatus getCurrentServiceStatus() {
        return currentServiceStatus.get();
    }

    /**
     * 获取当前定时任务状态
     *
     * @return 当前定时任务状态
     */
    public CommonStatus getCurrentSchedulerStatus() {
        return currentSchedulerStatus.get();
    }

    /**
     * 判断服务是否可用
     *
     * @return true-可用，false-不可用
     */
    public boolean isServiceAvailable() {
        return getCurrentServiceStatus() == CommonStatus.ENABLE;
    }

    /**
     * 判断服务是否停用
     *
     * @return true-停用，false-启用
     */
    public boolean isServiceDisabled() {
        return getCurrentServiceStatus() == CommonStatus.DISABLE;
    }

    /**
     * 判断定时任务是否可用
     *
     * @return true-可用，false-不可用
     */
    public boolean isSchedulerAvailable() {
        return getCurrentSchedulerStatus() == CommonStatus.ENABLE;
    }

    /**
     * 判断定时任务是否停用
     *
     * @return true-停用，false-启用
     */
    public boolean isSchedulerDisabled() {
        return getCurrentSchedulerStatus() == CommonStatus.DISABLE;
    }




    /**
     * 注册状态变更监听器
     *
     * @param listener 监听器
     */
    public void addListener(ServiceSwitchListener listener) {
        listeners.add(listener);
        log.info("注册服务状态监听器: {}", listener.getClass().getSimpleName());
    }

    /**
     * 移除状态变更监听器
     *
     * @param listener 监听器
     */
    public void removeListener(ServiceSwitchListener listener) {
        listeners.remove(listener);
        log.info("移除服务状态监听器: {}", listener.getClass().getSimpleName());
    }

    /**
     * 通知所有监听器状态变更
     *
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     */
    private void notifyListeners(CommonStatus oldStatus, CommonStatus newStatus) {
        for (ServiceSwitchListener listener : listeners) {
            try {
                listener.onStatusChanged(oldStatus, newStatus);
                log.info("通知监听器 {} 状态变更成功", listener.getClass().getSimpleName());
            } catch (Exception e) {
                log.error("通知监听器 {} 状态变更失败: {}",
                        listener.getClass().getSimpleName(), e.getMessage(), e);
            }
        }
    }



    /**
     * 服务状态变更监听器接口
     */
    public interface ServiceSwitchListener {
        /**
         * 状态变更回调
         *
         * @param oldStatus 旧状态
         * @param newStatus 新状态
         */
        void onStatusChanged(CommonStatus oldStatus, CommonStatus newStatus);
    }
}
