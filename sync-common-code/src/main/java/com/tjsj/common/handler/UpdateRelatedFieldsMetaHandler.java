package com.tjsj.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.constants.UserConsts;
import com.tjsj.common.utils.crypto.TokenUtil;
import com.tjsj.common.utils.date.DateUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * UpdateRelatedFieldsMetaHandler
 *
 * <AUTHOR>
 * @date 2024/07/23
 * @description 更新相关领域元处理器
 */
@Component
@CheckCount(count = 1)
public class UpdateRelatedFieldsMetaHandler implements MetaObjectHandler {

    @Value("${tarkin.env}")
    private String envValue;

    private static final String CREATE_USER = "createUser";
    private static final String CREATE_TIME = "createTime";
    private static final String UPDATE_USER = "updateUser";
    private static final String UPDATE_TIME = "updateTime";
    private static final String DATE = "date";
    private static final String ENV_FIELD = "env";

    /**
     * 新增操作
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, CREATE_USER, String.class, getUserId());
        this.strictInsertFill(metaObject, CREATE_TIME, Date.class, new Date());
        this.strictInsertFill(metaObject, UPDATE_USER, String.class, getUserId());
        this.strictInsertFill(metaObject, UPDATE_TIME, Date.class, new Date());
        this.strictInsertFill(metaObject, DATE, String.class, DateUtils.format(new Date()));
        this.strictInsertFill(metaObject, DATE, Date.class, new Date());
        /*LocalDate*/
        this.strictInsertFill(metaObject, DATE, LocalDate.class, LocalDate.now());
        /*LocalDateTime*/
        this.strictInsertFill(metaObject, CREATE_TIME, LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, UPDATE_TIME, LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, ENV_FIELD, String.class, envValue);
    }

    /**
     * 更新操作
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, UPDATE_USER, String.class, getUserId());
        this.strictUpdateFill(metaObject, UPDATE_TIME, Date.class, new Date());
        this.strictUpdateFill(metaObject, UPDATE_TIME, LocalDateTime.class, LocalDateTime.now());
    }

    /**
     * 获取用户id
     *
     * @return {@link String }
     * <AUTHOR> Ye
     * @date 2024/09/27
     */
    public static String getUserId() {
        String userId = TokenUtil.getCurrentUID();
        // 为了防止赋值为null的情况出现，给一个默认值
        if (userId == null) {
            userId = UserConsts.SYSTEM_USER;
        }
        return userId;
    }

}


