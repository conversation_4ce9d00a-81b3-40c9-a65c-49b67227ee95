package com.tjsj.common.monitor;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * RedisHealthMonitor
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @date 2025/06/23
 * @description Redis 健康状态监控组件，定时检查 Redis 连接状态并记录健康状态
 */
@Slf4j
@Component
@Data
public class RedisHealthMonitor implements HealthIndicator {

    @Autowired(required = false)
    private RedissonClient redissonClient;

    /**
     * Redis 是否健康
     */
    private final AtomicBoolean isHealthy = new AtomicBoolean(true);

    /**
     * 最后一次健康检查时间
     */
    private final AtomicLong lastCheckTime = new AtomicLong(System.currentTimeMillis());

    /**
     * 连续失败次数
     */
    private final AtomicLong consecutiveFailures = new AtomicLong(0);

    /**
     * 最大连续失败次数阈值
     */
    private static final long MAX_CONSECUTIVE_FAILURES = 3;

    /**
     * 健康检查间隔（毫秒），默认 5 秒
     */
    private static final long HEALTH_CHECK_INTERVAL = 5000L;

    /**
     * 定时健康检查
     */
    @Scheduled(fixedDelay = HEALTH_CHECK_INTERVAL)
    public void performHealthCheck() {

        long startTime = System.currentTimeMillis();

        if (redissonClient == null) {
            log.warn("╔═══════════════════════════════════════════════════════════════╗");
            log.warn("║                    Redis 健康检查失败                         ║");
            log.warn("╠═══════════════════════════════════════════════════════════════╣");
            log.warn("║ 原因: RedissonClient 未配置                                   ║");
            log.warn("║ 建议: 请检查 Redis 配置是否正确                               ║");
            log.warn("╚═══════════════════════════════════════════════════════════════╝");
            updateHealthStatus(false, "RedissonClient 未配置");
            return;
        }

        try {
            // 执行简单的 Redis 操作
            String testKey = "health_check_" + System.currentTimeMillis();
            long operationStart = System.currentTimeMillis();

            redissonClient.getBucket(testKey).set("test", 1000, java.util.concurrent.TimeUnit.MILLISECONDS);
            redissonClient.getBucket(testKey).delete();

            long operationTime = System.currentTimeMillis() - operationStart;
            long totalTime = System.currentTimeMillis() - startTime;

            // 健康检查成功 - 只在状态变化或每10次检查时输出详细日志
            boolean wasUnhealthy = !isHealthy.get();
            updateHealthStatus(true, "Redis 连接正常");
            consecutiveFailures.set(0);

            if (wasUnhealthy) {
                log.info("╔═══════════════════════════════════════════════════════════════╗");
                log.info("║                    Redis 健康检查成功                         ║");
                log.info("╠═══════════════════════════════════════════════════════════════╣");
                log.info("║ 状态: ✅ 连接正常                                            ║");
                log.info("║ 操作耗时: {} ms                                        ║",
                        String.format("%4d", operationTime));
                log.info("║ 总耗时: {} ms                                          ║", String.format("%4d", totalTime));
                log.info("║ 测试键: {}                          ║", String.format("%-30s", testKey));
                log.info("╚═══════════════════════════════════════════════════════════════╝");
            }

        } catch (Exception e) {
            long failures = consecutiveFailures.incrementAndGet();
            long totalTime = System.currentTimeMillis() - startTime;

            // 构建详细的错误信息
            String errorType = e.getClass().getSimpleName();
            String errorMessage = e.getMessage() != null ? e.getMessage() : "未知错误";

            // 输出可视化错误日志
            log.warn("╔═══════════════════════════════════════════════════════════════╗");
            log.warn("║                    Redis 健康检查异常                         ║");
            log.warn("╠═══════════════════════════════════════════════════════════════╣");
            log.warn("║ 状态: ❌ 连接异常                                            ║");
            log.warn("║ 连续失败: {}/{} 次                                     ║",
                    String.format("%2d", failures), String.format("%2d", MAX_CONSECUTIVE_FAILURES));
            log.warn("║ 异常类型: {}                                    ║", String.format("%-30s", errorType));
            log.warn("║ 异常信息: {}                                    ║", String.format("%-30s",
                    errorMessage.length() > 30 ? errorMessage.substring(0, 27) + "..." : errorMessage));
            log.warn("║ 检查耗时: {} ms                                        ║", String.format("%4d", totalTime));

            // 只有连续失败次数超过阈值才标记为不健康
            if (failures >= MAX_CONSECUTIVE_FAILURES) {
                log.warn("║ 警告: 🚨 连续失败次数已达阈值，标记为不健康                   ║");
                updateHealthStatus(false, String.format("Redis连接异常(连续%d次失败): %s", failures, errorType));
            } else {
                log.warn("║ 提示: ⚠️  暂未达到失败阈值，继续监控                         ║");
            }
            log.warn("╚═══════════════════════════════════════════════════════════════╝");
        }
    }

    /**
     * 更新健康状态
     *
     * @param healthy 是否健康
     * @param message 状态消息
     */
    private void updateHealthStatus(boolean healthy, String message) {
        boolean previousStatus = isHealthy.get();
        isHealthy.set(healthy);
        lastCheckTime.set(System.currentTimeMillis());

        // 状态发生变化时记录可视化日志
        if (previousStatus != healthy) {
            if (healthy) {
                log.info("╔═══════════════════════════════════════════════════════════════╗");
                log.info("║                    Redis 服务状态变更                         ║");
                log.info("╠═══════════════════════════════════════════════════════════════╣");
                log.info("║ 状态变更: ❌ 异常 → ✅ 正常                                  ║");
                log.info("║ 当前状态: 🟢 健康                                            ║");
                log.info("║ 详细信息: {}                                    ║", String.format("%-30s", message));
                log.info("║ 变更时间: {}                              ║",
                        String.format("%-30s", java.time.LocalDateTime.now().format(
                                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
                log.info("╚═══════════════════════════════════════════════════════════════╝");
            } else {
                log.error("╔═══════════════════════════════════════════════════════════════╗");
                log.error("║                    Redis 服务状态变更                         ║");
                log.error("╠═══════════════════════════════════════════════════════════════╣");
                log.error("║ 状态变更: ✅ 正常 → ❌ 异常                                  ║");
                log.error("║ 当前状态: 🔴 异常                                            ║");
                log.error("║ 详细信息: {}                                    ║", String.format("%-30s", message));
                log.error("║ 失败次数: {} 次                                         ║",
                        String.format("%4d", consecutiveFailures.get()));
                log.error("║ 变更时间: {}                              ║",
                        String.format("%-30s", java.time.LocalDateTime.now().format(
                                java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
                log.error("╚═══════════════════════════════════════════════════════════════╝");
            }
        }
    }

    /**
     * 获取 Redis 健康状态
     *
     * @return 是否健康
     */
    public boolean isRedisHealthy() {
        return isHealthy.get();
    }

    /**
     * Spring Boot Actuator 健康检查接口
     *
     * @return 健康状态
     */
    @Override
    public Health health() {
        if (isRedisHealthy()) {
            return Health.up()
                    .withDetail("status", "Redis 连接正常")
                    .withDetail("lastCheckTime", lastCheckTime.get())
                    .withDetail("consecutiveFailures", consecutiveFailures.get())
                    .build();
        } else {
            return Health.down()
                    .withDetail("status", "Redis 连接异常")
                    .withDetail("lastCheckTime", lastCheckTime.get())
                    .withDetail("consecutiveFailures", consecutiveFailures.get())
                    .build();
        }
    }
}
