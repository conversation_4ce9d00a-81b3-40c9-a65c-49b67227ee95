package com.tjsj.common.multirequestbody;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Enumeration;

/**
 * 多请求体过滤器
 *
 * <p>该过滤器主要用于处理HTTP请求体的多次读取问题，通过包装HttpServletRequest，
 * 使得请求体可以被多次读取，解决在某些场景下请求体只能读取一次的限制。</p>
 *
 * <h3>🎯 主要功能</h3>
 * <ul>
 *   <li><strong>请求体复用</strong>：允许请求体被多次读取</li>
 *   <li><strong>文件上传支持</strong>：对multipart/form-data请求进行特殊处理</li>
 *   <li><strong>透明代理</strong>：对应用层透明，不影响正常业务逻辑</li>
 *   <li><strong>性能监控</strong>：提供详细的请求处理日志和性能统计</li>
 * </ul>
 *
 * <h3>🔄 工作原理</h3>
 * <ol>
 *   <li>拦截所有HTTP请求</li>
 *   <li>检查请求类型（普通请求 vs 文件上传）</li>
 *   <li>对非文件上传请求进行包装，支持多次读取</li>
 *   <li>记录详细的处理日志和性能指标</li>
 * </ol>
 *
 * <AUTHOR> Ye
 * @version 2.0.0
 * @since 1.0.0 (2024/08/10)
 * @description 多请求体过滤器 - 支持请求体多次读取
 */
@Component
@WebFilter(urlPatterns = "/*")
@Slf4j
public class MultiRequestBodyFilter implements Filter {

    /**
     * 日志分隔线 - 用于美化日志输出
     */
    private static final String LOG_SEPARATOR = "═".repeat(80);
    private static final String LOG_SUB_SEPARATOR = "─".repeat(60);

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    /**
     * 过滤器统计信息
     */
    private volatile long totalRequests = 0;
    private volatile long wrappedRequests = 0;
    private volatile long multipartRequests = 0;

    @Override
    public void init(FilterConfig filterConfig) {
        logFilterInitialization(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        long startTime = System.currentTimeMillis();
        totalRequests++;

        try {
            // 记录请求开始日志
            logRequestStart(request);

            // 判断是否为文件上传请求
            boolean isMultipartRequest = isMultipartRequest(request);

            if (isMultipartRequest) {
                // 文件上传请求直接传递，不进行包装
                multipartRequests++;
                logMultipartRequest(request);
                filterChain.doFilter(request, response);
            } else {
                // 普通请求进行包装以支持多次读取
                wrappedRequests++;
                ServletRequest requestWrapper = new ReaderReuseHttpServletRequestWrapper(request);
//                logWrappedRequest(request);
                filterChain.doFilter(requestWrapper, response);
            }

            // 记录请求完成日志
            logRequestComplete(request, response, startTime);

        } catch (Exception e) {
            // 记录异常日志
            logRequestError(request, e, startTime);
            throw e;
        }
    }

    @Override
    public void destroy() {
        logFilterDestruction();
    }

    // ═══════════════════════════════════════════════════════════════════════════════════════
    // 🎨 私有方法 - 日志输出美化
    // ═══════════════════════════════════════════════════════════════════════════════════════

    /**
     * 记录过滤器初始化日志
     */
    private void logFilterInitialization(FilterConfig filterConfig) {
        if (log.isInfoEnabled()) {
            log.info("");
            log.info("╔{}", LOG_SEPARATOR);
            log.info("║  🚀 多请求体过滤器初始化");
            log.info("╠{}", LOG_SUB_SEPARATOR);
            log.info("║  📅 初始化时间: {}", LocalDateTime.now().format(TIME_FORMATTER));
            log.info("║  🔧 过滤器名称: {}",
                    filterConfig != null ? filterConfig.getFilterName() : "MultiRequestBodyFilter");
            log.info("║  🎯 拦截路径: /*");
            log.info("║  📋 主要功能:");
            log.info("║    • 支持请求体多次读取");
            log.info("║    • 处理文件上传请求");
            log.info("║    • 提供详细的请求日志");
            log.info("║    • 统计过滤器性能指标");
            log.info("╚{}", LOG_SEPARATOR);
            log.info("");
        }
    }

    /**
     * 记录请求开始日志
     */
    private void logRequestStart(HttpServletRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("🔄 [请求开始] {} {} | 来源: {} | Content-Type: {}",
                    request.getMethod(),
                    request.getRequestURI(),
                    getClientInfo(request),
                    StringUtils.hasText(request.getContentType()) ? request.getContentType() : "未指定"
            );
        }
    }

    /**
     * 记录文件上传请求日志
     */
    private void logMultipartRequest(HttpServletRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("📁 [文件上传] {} {} | 直接传递，无需包装",
                    request.getMethod(), request.getRequestURI());
        }
    }

    /**
     * 记录包装请求日志
     */
    private void logWrappedRequest(HttpServletRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("📦 [请求包装] {} {} | 已包装支持多次读取",
                    request.getMethod(), request.getRequestURI());
        }
    }

    /**
     * 记录请求完成日志
     */
    private void logRequestComplete(HttpServletRequest request, HttpServletResponse response, long startTime) {
        long duration = System.currentTimeMillis() - startTime;

        if (log.isDebugEnabled()) {
            log.debug("✅ [请求完成] {} {} | 状态: {} | 耗时: {}ms",
                    request.getMethod(),
                    request.getRequestURI(),
                    response.getStatus(),
                    duration
            );
        }

        // 如果处理时间过长，记录警告日志
        if (duration > 1000) { // 超过1秒
            log.warn("⚠️ [性能警告] {} {} | 处理时间过长: {}ms",
                    request.getMethod(), request.getRequestURI(), duration);
        }
    }

    /**
     * 记录请求异常日志
     */
    private void logRequestError(HttpServletRequest request, Exception e, long startTime) {
        long duration = System.currentTimeMillis() - startTime;

        log.error("❌ [请求异常] {} {} | 耗时: {}ms | 异常: {}",
                request.getMethod(),
                request.getRequestURI(),
                duration,
                e.getMessage(),
                e
        );
    }

    /**
     * 记录过滤器销毁日志
     */
    private void logFilterDestruction() {
        if (log.isInfoEnabled()) {
            log.info("");
            log.info("╔{}", LOG_SEPARATOR);
            log.info("║  🛑 多请求体过滤器销毁");
            log.info("╠{}", LOG_SUB_SEPARATOR);
            log.info("║  📅 销毁时间: {}", LocalDateTime.now().format(TIME_FORMATTER));
            log.info("║  📊 运行统计:");
            log.info("║    • 总请求数: {}", totalRequests);
            log.info("║    • 包装请求数: {}", wrappedRequests);
            log.info("║    • 文件上传请求数: {}", multipartRequests);
            log.info("║    • 包装率: {:.2f}%", totalRequests > 0 ? (wrappedRequests * 100.0 / totalRequests) : 0);
            log.info("║  💡 感谢使用多请求体过滤器服务！");
            log.info("╚{}", LOG_SEPARATOR);
            log.info("");
        }
    }

    // ═══════════════════════════════════════════════════════════════════════════════════════
    // 🔧 工具方法
    // ═══════════════════════════════════════════════════════════════════════════════════════

    /**
     * 判断是否为文件上传请求
     *
     * @param request HTTP请求
     * @return true-文件上传请求，false-普通请求
     */
    private boolean isMultipartRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        return contentType != null && contentType.toLowerCase().contains(MediaType.MULTIPART_FORM_DATA_VALUE);
    }

    /**
     * 获取客户端信息
     *
     * @param request HTTP请求
     * @return 客户端信息字符串
     */
    private String getClientInfo(HttpServletRequest request) {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");

        if (StringUtils.hasText(userAgent)) {
            // 简化User-Agent显示
            if (userAgent.length() > 50) {
                userAgent = userAgent.substring(0, 47) + "...";
            }
            return clientIp + " [" + userAgent + "]";
        }

        return clientIp;
    }

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        // 尝试从各种代理头中获取真实IP
        String[] headers = {
                "X-Forwarded-For",
                "X-Real-IP",
                "Proxy-Client-IP",
                "WL-Proxy-Client-IP",
                "HTTP_CLIENT_IP",
                "HTTP_X_FORWARDED_FOR"
        };

        for (String header : headers) {
            String ip = request.getHeader(header);
            if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }

        // 如果都没有，返回远程地址
        return request.getRemoteAddr();
    }
}
