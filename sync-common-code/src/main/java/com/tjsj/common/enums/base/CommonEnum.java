package com.tjsj.common.enums.base;

import lombok.Getter;
import org.apache.commons.httpclient.HttpStatus;

/**
 * CommonEnum
 *
 * <AUTHOR>
 * @date 2020/3/5
 * @description 通用枚举类
 */
@Getter
public enum CommonEnum {

    // 数据操作错误定义
    SUCCESS(HttpStatus.SC_OK, "接口请求成功!"),

    FORBIDDEN(HttpStatus.SC_FORBIDDEN, "接口访问被拒绝！");

    /**
     * 错误码
     */
    private final int status;

    /**
     * 错误描述
     */
    private final String msg;

    CommonEnum(int resultCode, String resultMsg) {
        this.status = resultCode;
        this.msg = resultMsg;
    }

}
