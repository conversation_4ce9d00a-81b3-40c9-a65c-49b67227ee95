package com.tjsj.common.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TaskStatusEnum
 *
 * <AUTHOR>
 * @date 2024/7/9 21:45
 * @version 1.0.0
 * @description 任务执行状态枚举类
 */
@Getter
@AllArgsConstructor
@Schema(name = "TaskExecuteStatusEnum", description = "任务执行状态枚举类")
public enum TaskExecuteStatusEnum implements BaseEnum {

    /**
     * 成功
     */
    SUCCESS((short) 0, "成功"),

    /**
     * 失败
     */
    FAILURE((short) 1, "失败");

    @EnumValue
    @JsonValue
    private final Short code;


    private final String description;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
