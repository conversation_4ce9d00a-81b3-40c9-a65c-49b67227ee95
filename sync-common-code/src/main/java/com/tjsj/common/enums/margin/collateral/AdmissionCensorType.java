package com.tjsj.common.enums.margin.collateral;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * AdmissionCensorType
 *
 * <AUTHOR>
 * @date 2024/9/3 15:31
 * @description 准入调整类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "AdmissionCensorType", description = "准入调整类型")
public enum AdmissionCensorType implements BaseEnum {

    /**
     * 1:待审核
     */
    TO_BE_CENSORED(1, "待审核"),

    /**
     * 2:审核通过
     */
    CENSOR_PASS(2, "审核通过"),

    /**
     * 3:审核不通过
     */
    CENSOR_FAIL(3, "审核不通过");

    @EnumValue
    @JsonValue
    private Integer code;

    private String description;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
