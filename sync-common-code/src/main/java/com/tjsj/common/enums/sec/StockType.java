package com.tjsj.common.enums.sec;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * StockType
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/24 15:31
 * @description 股票板块类型枚举类
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "股票板块类型枚举类")
public enum StockType implements BaseEnum {

    /**
     * 深市A股
     */
    SHENZHEN_EXCHANGE("深市A股"),

    /**
     * 沪市A股
     */
    SHANGHAI_EXCHANGE("沪市A股"),

    /**
     * 创业板
     */
    ChiNext("创业板"),

    /**
     * 科创板
     */
    StarMarket("科创板"),

    /**
     * 北证A股
     */
    BEIJING_EXCHANGE("北证A股");


    @JsonValue
    @EnumValue
    private String code;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
