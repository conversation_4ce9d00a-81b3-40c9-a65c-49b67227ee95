package com.tjsj.common.enums.financial;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/8/10 19:00
 * @description 财务报表日期类型枚举类
 */
@Getter
@AllArgsConstructor
@Schema(description = "财务报表日期类型枚举类")
public enum FinReportDateType implements BaseEnum {

    /**
     * -03-31:一季报
     */
    FIRST_QUARTER("-03-31","一季报"),

    /**
     * -06-30:中报
     */
    SECOND_QUARTER("-06-30","中报"),

    /**
     * -09-30:三季报
     */
    THIRD_QUARTER("-09-30","三季报"),

    /**
     * -12-31:年报
     */
    FOURTH_QUARTER("-12-31","年报");

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
