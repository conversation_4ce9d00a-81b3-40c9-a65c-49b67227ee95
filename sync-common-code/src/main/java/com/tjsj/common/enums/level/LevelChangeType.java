package com.tjsj.common.enums.level;

import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * LevelChangeType
 *
 * <AUTHOR>
 * @date 2024/8/16 14:12
 * @description 评级变动类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "LevelChangeType", description = "评级变动类型")
public enum LevelChangeType implements BaseEnum {

    /**
     * 0: 跨级变动
     */
    CROSS_LEVEL(0,"跨级变动"),

    /**
     * 1: 逐级变动
     */
    STEP_BY_STEP(1,"逐级变动");

    private Integer code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
