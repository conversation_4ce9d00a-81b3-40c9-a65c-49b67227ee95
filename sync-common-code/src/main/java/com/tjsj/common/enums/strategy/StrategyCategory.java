package com.tjsj.common.enums.strategy;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * StrategyCategory
 *
 * <AUTHOR>
 * @date 2024/8/16 9:38
 * @description 策略分类枚举类
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "StrategyCategory", description = "策略分类枚举类")
public enum StrategyCategory implements BaseEnum {

    /**
     * 评级策略
     */
    LEVEL_SRT(1, "评级策略"),

    /**
     * 矩阵策略
     */
    MATRIX_SRT(2, "矩阵策略"),

    /**
     * 压力策略
     */
    STRESS_SRT(3, "压力策略");

    @EnumValue
    @JsonValue
    private Integer code;

    private String name;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
