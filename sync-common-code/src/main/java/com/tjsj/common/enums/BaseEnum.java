package com.tjsj.common.enums;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Objects;

/**
 * <AUTHOR> Ye
 * @date 2024/8/6 20:59
 * @description 基础枚举
 */
@Schema(name = "BaseEnum", description = "用于扫描、序列化、反序列化实际枚举类")
public interface BaseEnum {

    /**
     * 获取code值~
     *
     * @return {@link Object }
     * <AUTHOR> Ye
     * @date 2024/08/07
     */
    Object getCode();


    /**
     * 转换值
     *
     * @return 值
     * <AUTHOR> Ye
     * @date 2024/08/06
     */
    Object getTransferValue();

    /**
     * 获取枚举
     *
     * @param enumTypeClazz 枚举类型类
     * @param value         值
     * @param nullThrExFlag 为空是否抛异常
     * @return <T extends BaseEnum> 枚举类型
     * <AUTHOR> Ye
     * @date 2024/08/06
     */
    static <T extends BaseEnum> T getEnum(Class<T> enumTypeClazz, Object value, boolean nullThrExFlag) {

        //System.out.println("enumTypeClazz: " + enumTypeClazz + ", value: " + value);
        // 如果传入的枚举类型类或值为null
        if (enumTypeClazz == null || value == null) {
            // 如果设置了为空抛出异常的标志，则抛出运行时异常
            if (nullThrExFlag) {
                if (enumTypeClazz != null) {
                    // 枚举类型不为空时，抛出带有枚举类名的异常信息
                    throw new RuntimeException("[" + enumTypeClazz.getSimpleName() + "]参数错误: 值不能为空");
                }
            }
            // 如果不需要抛出异常，则返回null
            return null;
        }

        // 获取枚举类中所有枚举常量
        T[] enumConstants = enumTypeClazz.getEnumConstants();

        // 遍历每个枚举常量
        for (T enumConstant : enumConstants) {
            // 获取当前枚举常量的代码值（如对应的数值或字符串）
            Object enumValue = enumConstant.getTransferValue();
            // 判断传入的值是否与枚举常量的代码值匹配
            if (Objects.equals(enumValue, value) || Objects.equals(enumValue.toString(), value.toString())) {
                // 如果匹配，则返回该枚举常量
                return enumConstant;
            }
        }

        // 如果未找到匹配的枚举值，并且设置了为空抛出异常的标志，则抛出运行时异常
        if (nullThrExFlag) {
            throw new RuntimeException("[" + enumTypeClazz.getName() + "]参数错误: 未找到匹配的枚举值[" + value + "]");
        }

        // 如果未找到匹配的枚举值且无需抛出异常，则返回null
        return null;
    }


}
