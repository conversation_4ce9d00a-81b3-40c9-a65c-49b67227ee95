package com.tjsj.common.enums.margin.peer;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.constants.PeerConsts;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * PeerDailyAdjustType
 *
 * <AUTHOR>
 * @date 2024/8/12 11:30
 * @description 同业每日简报调整类型枚举类
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "PeerDailyAdjustType", description = "同业每日简报调整类型枚举类")
public enum PeerDailyAdjustType implements BaseEnum {

    /**
     * dbp:担保品
     */
    COLLATERAL(Collections.singletonList(PeerConsts.PEER_COLLATERAL_OUT), "dbp"),

    /**
     * zsl:折算率
     */
    HAIRCUT(Collections.singletonList(PeerConsts.PEER_COLLATERAL_HAIRCUT_DOWN), "zsl"),

    /**
     * bdzq:标的证券
     */
    UNDERLYING(Arrays.asList(PeerConsts.PEER_MARGIN_SECURITY_OUT, PeerConsts.PEER_SHORT_SECURITY_OUT), "bdzq"),

    /**
     * peerLevel:证券分类
     */
    CATEGORY(Arrays.asList(PeerConsts.PEER_PEER_LEVEL_DOWN, PeerConsts.PEER_PEER_LEVEL_UP,
            PeerConsts.PEER_PEER_LEVEL_OUT), "peerLevel");

    @EnumValue
    private List<String> code;

    @JsonValue
    private String description;

    @Override
    public Object getTransferValue() {
        return description;
    }
}
