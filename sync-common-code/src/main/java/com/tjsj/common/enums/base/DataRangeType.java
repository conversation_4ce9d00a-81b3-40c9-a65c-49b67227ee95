package com.tjsj.common.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * DataRangeType
 *
 * <AUTHOR>
 * @date 2024/8/25 0:15
 * @description 数据范围类型枚举类
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "DataRangeType", description = "数据范围类型枚举类")
public enum DataRangeType implements BaseEnum {

    /**
     * 股价: 股价区间
     */
    STOCK_PRICE("股价", "股价区间"),

    /**
     * 市值: 市值区间
     */
    TOTAL_AMOUNT("市值", "市值区间"),

    /**
     * 财务评分: 财务评分区间
     */
    FINANCIAL_SCORE("财务评分", "财务评分区间");

    @EnumValue
    @JsonValue
    private String code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
