package com.tjsj.common.enums.level;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * LevelQueryType
 *
 * <AUTHOR>
 * @date 2024/8/29 19:35
 * @description 评级查询类型
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "LevelQueryType", description = "评级查询类型")
public enum LevelQueryType implements BaseEnum {

    /**
     * all :全部
     */
    ALL("all", "全部"),

    /**
     * regi :注册制
     */
    REGISTER("regi", "注册制"),

    /**
     * nonRegi :非注册制
     */
    NON_REGISTER("nonRegi", "非注册制");

    @EnumValue
    @JsonValue
    private String code;

    private String description;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
