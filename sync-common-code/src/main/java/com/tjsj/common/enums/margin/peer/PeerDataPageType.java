package com.tjsj.common.enums.margin.peer;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * PeerDataPageType
 *
 * <AUTHOR>
 * @date 2024/8/14 10:15
 * @description 同业数据页面查询类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "PeerDataPageType", description = "同业数据页面查询类型")
public enum PeerDataPageType implements BaseEnum {

    /**
     * 0:总览
     */
    OVERVIEW(0, "总览"),

    /**
     * 1:担保品
     */
    COLLATERAL(1, "担保品"),

    /**
     * 2:标的证券
     */
    UNDERLYING(2, "标的证券");

    @EnumValue
    @JsonValue
    private Integer code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
