package com.tjsj.common.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * DataUnit
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/10 14:03
 * @description 数据单位枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据单位枚举类")
public enum DataUnit implements BaseEnum {
    /**
     * 一
     */
    ONE("一"),

    /**
     * 元
     */
    YUAN("元"),

    /**
     * 百分比
     */
    PERCENTAGE("%"),

    /**
     * 万
     */
    TEN_THOUSAND("万"),

    /**
     * 百万
     */
    MILLION("百万"),

    /**
     * 千万
     */
    TEN_MILLION("千万"),

    /**
     * 亿
     */
    HUNDRED_MILLION("亿");

    @EnumValue
    @JsonValue
    private String code;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
