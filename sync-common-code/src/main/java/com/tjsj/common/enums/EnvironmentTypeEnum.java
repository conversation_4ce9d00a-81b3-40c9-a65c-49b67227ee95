package com.tjsj.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * EnvironmentTypeEnum
 *
 * <AUTHOR>
 * @date 2024/8/6 19:41
 * @version 1.0.0
 * @description 项目运行环境类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "EnvironmentTypeEnum", description = "项目运行环境类型枚举")
public enum EnvironmentTypeEnum implements BaseEnum {

    /**
     * 塔金
     */
    TARKIN("tarkin", "塔金"),

    /**
     * 华龙证券
     */
    HLZQ("hlzq", "华龙证券"),

    /**
     * 兴业证券
     */
    XYZQ("xyzq", "兴业证券"),

    /**
     * 东北证券
     */
    DBZQ("dbzq", "东北证券");

    @EnumValue
    private String code;

    @JsonValue
    private String description;


    @Override
    public Object getTransferValue() {
        return description;
    }
}
