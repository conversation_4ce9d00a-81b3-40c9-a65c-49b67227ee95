package com.tjsj.common.enums.margin.collateral;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/7 14:44
 * @description 担保品折算率设置类型
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "担保品折算率设置类型")
public enum CollHaircutSettingType implements BaseEnum {

    /**
     * 1:按证券类型
     */
    SEC_TYPE(1, "按证券类型"),

    /**
     * 2:按证券板块
     */
    SEC_BLOCK(2, "按证券板块");

    @EnumValue
    @JsonValue
    private Integer code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
