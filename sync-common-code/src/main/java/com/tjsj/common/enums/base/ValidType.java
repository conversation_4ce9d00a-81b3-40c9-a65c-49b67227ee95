package com.tjsj.common.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * ValidType
 *
 * <AUTHOR>
 * @date 2024/8/29 13:43
 * @description - 有效类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "有效类型")
public enum ValidType implements BaseEnum {

    /**
     * Y:启用
     */
    VALID("Y", "启用"),

    /**
     * N:禁用
     */
    INVALID("N", "禁用");

    @EnumValue
    @JsonValue
    private String code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
