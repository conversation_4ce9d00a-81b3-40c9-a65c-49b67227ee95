package com.tjsj.common.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/8 16:48
 * @description 证券类型枚举
 */
@Schema(name = "SecTypeEnum", description = "证券类型枚举")
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SecTypeEnum implements BaseEnum {

    /**
     * 0:股票
     */
    STOCK((short) 0, "股票"),

    /**
     * 1:基金
     */
    FUND((short) 1, "基金"),

    /**
     * 2:债券
     */
    BOND((short) 2, "债券");


    @EnumValue
    @JsonValue
    private Short code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
