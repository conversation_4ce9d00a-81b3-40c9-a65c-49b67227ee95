package com.tjsj.common.enums.strategy;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/19 13:05
 * @description 评级策略类型枚举类
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "LevelSrtType", description = "评级策略类型枚举类")
public enum LevelSrtType implements BaseEnum {

    /**
     * 1:全局策略
     */
    GLOBAL_SRT(1, "全局策略"),

    /**
     * 2:主策略
     */
    MAIN_SRT(2, "主策略"),

    /**
     * 3:拦截策略
     */
    INTERCEPT_SRT(3, "拦截策略"),

    /**
     * 4:调级策略
     */
    ADJUST_SRT(4, "调级策略"),

    /**
     * 5:后置策略
     */
    GLOBAL_POST_SRT(5, "后置策略");

    @EnumValue
    @JsonValue
    private Integer code;

    private String name;

    /**
     * 用于缓存 code 到枚举实例的映射
     */
    private static final Map<Integer, LevelSrtType> CODE_MAP = new HashMap<>();

    // 在类加载时初始化 MAP
    static {
        for (LevelSrtType type : LevelSrtType.values()) {
            CODE_MAP.put(type.getCode(), type);
        }
    }

    @Override
    public Object getTransferValue() {
        return code;
    }

    /**
     * 根据 code 值获取对应的枚举实例
     *
     * @param code 枚举的 code 值
     * @return 对应的 LevelSrtType 枚举实例
     */
    public static LevelSrtType fromCode(int code) {
        return CODE_MAP.get(code);
    }
}

