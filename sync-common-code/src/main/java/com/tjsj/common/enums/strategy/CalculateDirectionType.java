package com.tjsj.common.enums.strategy;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * CalculateDirectionType
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/10 21:03
 * @description 计算方向类型
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "计算方向类型")
public enum CalculateDirectionType implements BaseEnum {

    /**
     * >
     */
    GT(">", "大于"),

    /**
     * >=
     */
    GE(">=", "大于等于"),

    /**
     * =
     */
    EQ("=", "等于"),

    /**
     * <
     */
    LT("<", "小于"),

    /**
     * <=
     */
    LE("<=", "小于等于"),


    /**
     * 区间
     */
    RANGE("区间", "区间"),

    /**
     * -
     */
    SUBTRACT("-", "-"),


    /**
     * +
     */
    ADD("+", "+");


    private String description;

    @EnumValue
    @JsonValue
    private String code;


    @Override
    public Object getTransferValue() {
        return code;
    }

    /**
     * 从代码
     *
     * @param code 代码
     * @return {@link CalculateDirectionType }
     * <AUTHOR> Ye
     * @date 2024/09/14
     */
    public static CalculateDirectionType fromCode(String code) {
        for (CalculateDirectionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }
}
