package com.tjsj.common.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * ProfileType
 *
 * <AUTHOR>
 * @date 2024/8/27 19:21
 * @description 项目环境类型
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ProfileTypeEnum", description = "项目环境类型")
public enum ProfileTypeEnum implements BaseEnum {

    /**
     * dev:开发环境
     */
    DEV("dev", "开发环境"),

    /**
     * prod:生产环境
     */
    PROD("prod", "生产环境"),

    /**
     * test:测试环境
     */
    TEST("test", "测试环境"),

    /**
     * local:本地环境
     */
    LOCAL("local", "本地环境");


    @EnumValue
    @JsonValue
    private String code;

    private String description;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
