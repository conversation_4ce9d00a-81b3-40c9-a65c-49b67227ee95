package com.tjsj.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * MatrixOptionType
 *
 * <AUTHOR>
 * @date 2024/7/10 22:43
 * @description 矩阵类型枚举类
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "矩阵类型枚举类")
public enum MatrixOptionType {

    /**
     * 常用项
     */
    COMMON((short) 0, "常用项"),

    /**
     * 资产负债表
     */
    ASSET_LIABILITY((short) 3, "资产负债表"),


    /**
     * 利润表
     */
    PROFIT((short) 4, "利润表"),

    /**
     * 利润表
     */
    CASH_FLOW((short) 5, "现金流量表");

    @EnumValue
    @JsonValue
    private Short code;

    private String description;

    @Override
    public String toString() {
        return description;
    }
}
