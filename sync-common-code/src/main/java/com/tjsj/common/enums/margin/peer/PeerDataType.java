package com.tjsj.common.enums.margin.peer;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/11 20:04
 * @description 同业数据类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "PeerDataType", description = "同业数据类型枚举")
public enum PeerDataType implements BaseEnum {

    /**
     * 担保品
     */
    COLLATERAL("collateral", "担保品"),

    /**
     * 标的证券
     */
    UNDERLYING("underlying", "标的证券"),

    /**
     * 证券分类
     */
    CATEGORY("category","证券分类");

    @EnumValue
    @JsonValue
    private String code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }

}
