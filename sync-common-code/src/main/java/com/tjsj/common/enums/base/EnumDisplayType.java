package com.tjsj.common.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * EnumDisplayType
 *
 * <AUTHOR>
 * @date 2024/8/15 14:08
 * @description 枚举展示类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "EnumDisplayType", description = "枚举展示类型")
public enum EnumDisplayType implements BaseEnum {

    /**
     * marginTrading:融资融券
     */
    MARGIN_TRADING("marginTrading", "融资融券");


    @EnumValue
    @JsonValue
    private String code;

    private String description;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
