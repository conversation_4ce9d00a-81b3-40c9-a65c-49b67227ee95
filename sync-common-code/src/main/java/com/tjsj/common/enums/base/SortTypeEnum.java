package com.tjsj.common.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * SortTypeEnum
 *
 * <AUTHOR>
 * @date 2024/8/12 23:02
 * @description 排序类型枚举
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "排序类型枚举")
public enum SortTypeEnum implements BaseEnum {


    /**
     * 普通排序
     */
    GENERAL("general", "general", "普通排序"),

    /**
     * 系统评级排序
     */
    SYSLEVEL("sysLevel", "level", "系统评级排序"),

    /**
     * 同业券商分类排序
     */
    PEERCATEGORY("peerCategory", "level", "同业券商分类排序");

    public static final String LEVEL_TYPE = "level";

    @EnumValue
    @JsonValue
    private String code;

    private String type;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }

}
