package com.tjsj.common.enums.sync;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * SyncTaskType
 *
 * <AUTHOR>
 * @date 2024/8/7 11:09
 * @description 同步任务类型枚举
 * @version 1.0.0
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "SyncTaskTypeEnum", description = "同步任务类型枚举")
public enum SyncTaskTypeEnum implements BaseEnum {
    /**
     * 云到本地
     */
    CLOUD_TO_LOCAL(1, "云到本地"),

    /**
     * 本地到云
     */
    LOCAL_TO_CLOUD(2, "本地到云");

    @EnumValue
    @JsonValue
    private Integer code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
