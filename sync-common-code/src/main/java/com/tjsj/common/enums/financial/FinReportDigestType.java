package com.tjsj.common.enums.financial;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.constants.financial.FinancialConsts;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * FinReportDigestType
 *
 * <AUTHOR>
 * @date 2024/8/10 16:36
 * @description 财务报表摘要类型枚举
 */
@Getter
@AllArgsConstructor
@Schema(description = "财务报表摘要类型枚举")
public enum FinReportDigestType implements BaseEnum {

    /**
     * 资产负债表
     */
    BALANCE_SHEET("balanceSheet", "资产负债表", FinancialConsts.BAL_SHEET_TABLE_NAME),

    /**
     * 利润表
     */
    INCOME_STATEMENT("incomeStatement", "利润表", FinancialConsts.INCOME_STATEMENT_TABLE_NAME),

    /**
     * 现金流量表
     */
    CASH_FLOW_STATEMENT("cashFlowStatement", "现金流量表", FinancialConsts.CASH_FLOW_STATEMENT_TABLE_NAME),

    /**
     * 关键比率
     */
    KEY_RATIO("keyRatio", "关键比率", FinancialConsts.FINANCIAL_DIGEST_REPORT_TABLE_NAME);

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    private final String tableName;


    @Override
    public Object getTransferValue() {
        return code;
    }

}
