package com.tjsj.common.enums.sec;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * MarketDayType
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/10 11:57
 * @description 市场交易日类型
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "市场交易日类型")
public enum MarketDayType implements BaseEnum {
    /**
     * 1:自然日
     */
    NATURAL_DAY((short) 1, "自然日"),

    /**
     * 2:交易日
     */
    TRADING_DAY((short) 2, "交易日");

    @EnumValue
    @JsonValue
    private Short code;


    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
