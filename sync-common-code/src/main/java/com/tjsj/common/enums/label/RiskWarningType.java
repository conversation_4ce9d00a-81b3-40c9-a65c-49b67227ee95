package com.tjsj.common.enums.label;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * RiskWarningType
 *
 * <AUTHOR>
 * @date 2024/8/16 16:55
 * @description 风险预警类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "RiskWarningType", description = "风险预警类型")
public enum RiskWarningType implements BaseEnum {

    /**
     * 1:自定义评级预警
     */
    CUSTOM_LEVEL_WARNING(1, "自定义评级预警"),

    /**
     * 2:自定义标签预警
     */
    CUSTOM_LABEL_WARNING(2, "自定义标签预警");

    @EnumValue
    @JsonValue
    private Integer code;

    private String description;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
