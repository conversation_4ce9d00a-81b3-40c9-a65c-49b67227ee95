package com.tjsj.common.enums.sync;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * SyncTypeEnum
 *
 * <AUTHOR>
 * @date 2024/8/6 17:27
 * @description 同步方法类型枚举
 */
@Schema(name = "SyncTypeEnum", description = "同步方法类型枚举")
@Getter
@AllArgsConstructor
public enum SyncTypeEnum implements BaseEnum {

	/**
	 * 自动同步
	 */
	AUTO(0, "自动同步"),

	/**
	 * 手动同步
	 */
	MANUAL(1, "手动同步"),

	/**
	 * Quartz定时任务
	 */
	QUARTZ(2, "Quartz定时任务"),

	/**
	 * 表数据修复
	 */
	TABLE_REPAIR(3, "表数据修复");

	@EnumValue
	@JsonValue
	private final Integer code;


	private final String description;

	@Override
	public Object getTransferValue() {
		return code;
	}

}
