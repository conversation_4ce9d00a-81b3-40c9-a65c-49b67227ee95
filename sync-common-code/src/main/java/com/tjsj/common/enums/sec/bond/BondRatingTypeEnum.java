package com.tjsj.common.enums.sec.bond;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/9 17:36
 * @description 债券评级类型枚举
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Schema(description = "债券评级类型枚举")
public enum BondRatingTypeEnum implements BaseEnum {

    /**
     * 主体评级
     */
    SUBJECT("主体评级"),

    /**
     * 债券评级
     */
    BOND("债券评级");


    @EnumValue
    @JsonValue
    private String code;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
