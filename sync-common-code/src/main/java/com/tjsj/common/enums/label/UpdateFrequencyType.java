package com.tjsj.common.enums.label;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.constants.financial.FinancialConsts;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * UpdateFrequencyType
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/9/7 19:45
 * @description 标签更新频率类型
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "标签更新频率类型")
public enum UpdateFrequencyType implements BaseEnum {

    /**
     * 1:年度更新
     */
    YEARLY_UPDATE(1, FinancialConsts.PER_YEAR_DATES, "年度更新"),

    /**
     * 2:半年度更新
     */
    HALF_YEARLY_UPDATE(2, FinancialConsts.PER_HALF_YEAR_DATES, "半年度更新"),

    /**
     * 3:季度更新
     */
    QUARTERLY_UPDATE(3, FinancialConsts.PER_QUARTER_DATES, "季度更新");

    @EnumValue
    @JsonValue
    private Integer code;

    private List<String> template;

    private String description;


    @Override
    public Object getTransferValue() {
        return code;
    }

}
