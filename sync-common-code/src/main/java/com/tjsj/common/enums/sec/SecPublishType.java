package com.tjsj.common.enums.sec;

import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * SecPublishType
 *
 * <AUTHOR>
 * @date 2024/8/20 19:06
 * @description 证券发行类型
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SecPublishType", description = "证券发行类型")
public enum SecPublishType implements BaseEnum {

    /**
     * 审批制
     */
    APPROVAL("审批制"),

    /**
     * 核准制
     */
    AUTHORIZATION("核准制"),

    /**
     * 注册制
     */
    REGISTRATION("注册制");

    private String code;

    /**
     * 核准制起始日期
     */
    public static final String AUTHORIZATION_START_DATE = "2000-01-01";

    @Override
    public Object getTransferValue() {
        return code;
    }
}

