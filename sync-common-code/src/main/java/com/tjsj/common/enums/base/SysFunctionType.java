package com.tjsj.common.enums.base;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/4 10:17
 * @description 系统功能类型枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "SysFunctionType", description = "系统功能类型枚举")
public enum SysFunctionType implements BaseEnum {
    /**
     * 1:策略评级
     */
    STRATEGY_LEVEL(1, "策略评级"),

    /**
     * 2:专家意见评级
     */
    EXPERT_OPINION_LEVEL(2, "专家意见评级"),

    /**
     * 3:自定义标签
     */
    CUSTOM_LABEL(3, "自定义标签"),

    /**
     * 4:系统折算率模型
     */
    COLLATERAL_HAIRCUT_MODEL(4, "系统折算率模型"),

    /**
     * 5:自定义折算率模型
     */
    CUSTOM_COLLATERAL_HAIRCUT_MODEL(5, "自定义折算率模型");

    @EnumValue
    @JsonValue
    private Integer code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
