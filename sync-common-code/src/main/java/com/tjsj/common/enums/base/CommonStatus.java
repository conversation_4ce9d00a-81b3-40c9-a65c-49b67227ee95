package com.tjsj.common.enums.base;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/5 19:20
 * @description 通用状态枚举
 */
@Schema(description = "通用状态枚举")
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum CommonStatus implements BaseEnum {

    /**
     * 启用
     */
    ENABLE(0, "启用"),

    /**
     * 禁用
     */
    DISABLE(1, "禁用");

    @EnumValue
    @JSONField
    private Integer code;

    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
