package com.tjsj.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * TaskExecutionStatusEnum
 *
 * <AUTHOR>
 * @date 2024/08/07
 * @description 任务执行状态枚举类
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "TaskExecStatus", description = "任务执行状态枚举类")
public enum TaskExecStatus implements BaseEnum {

    /**
     * 0:待执行
     */
    NOT_EXECUTED(0, "待执行"),

    /**
     * 1:已执行
     */
    EXECUTED(1, "已执行");

//    /**
//     * 执行中
//     */
//    IN_PROGRESS((short) 2,"执行中"),


    @EnumValue
    @JsonValue
    private Integer code;


    private String description;

    @Override
    public Object getTransferValue() {
        return code;
    }
}
