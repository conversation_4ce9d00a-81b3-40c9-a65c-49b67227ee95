package com.tjsj.common.enums.industry;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * IndustryTier
 *
 * <AUTHOR>
 * @date 2024/8/24 15:02
 * @description 行业层级枚举类
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Schema(name = "IndustryTier", description = "行业层级枚举类")
public enum IndustryTier implements BaseEnum {

    /**
     * 1: 一级行业
     */
    TIER_ONE((short) 1, (short) 8, "一级行业"),

    /**
     * 2: 二级行业
     */
    TIER_TWO((short) 2, (short) 10, "二级行业"),

    /**
     * 3: 三级行业
     */
    TIER_THREE((short) 3, (short) 12, "三级行业");

    @EnumValue
    @JsonValue
    private Short code;

    @Schema(description = "编码长度")
    private Short codeLength;

    private String description;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
