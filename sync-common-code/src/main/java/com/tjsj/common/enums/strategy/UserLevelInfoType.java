package com.tjsj.common.enums.strategy;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.tjsj.common.enums.BaseEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * UserLevelInfoType
 *
 * <AUTHOR>
 * @date 2024/8/22 17:11
 * @description 用户评级详情类型
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "UserLevelInfoType", description = "用户评级详情类型")
public enum UserLevelInfoType implements BaseEnum {

    /**
     * 0:用户已评级证券
     */
    USER_RATED_SEC(0, "用户已评级证券"),

    /**
     * 1:用户未评级证券
     */
    USER_UNRATED_SEC(1, "用户未评级证券"),

    /**
     * 3:与系统评级差异的证券
     */
    DIFFERENCE_RATED_SEC(3, "与系统评级差异的证券"),

    /**
     * 4:调级策略
     */
    DIFFERENCE_USER_RATED_SEC(4, "与用户评级差异的证券");

    @EnumValue
    @JsonValue
    private Integer code;

    private String description;


    @Override
    public Object getTransferValue() {
        return code;
    }
}
