package com.tjsj.common.aspect;

import com.tjsj.common.annotation.SchedulerSwitchControl;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.common.manager.ServiceSwitchManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

/**
 * SchedulerSwitchControlAspect
 *
 * <AUTHOR> Ye
 * @date 2025/07/01
 * @description 定时任务开关控制切面 - 拦截使用@SchedulerSwitchControl注解的方法
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class SchedulerSwitchControlAspect {

    private final ServiceSwitchManager serviceSwitchManager;

    @Resource
    private TarkinConfig tarkinConfig;


    /**
     * 环绕通知：拦截使用@SchedulerSwitchControl注解的方法
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    @Around("@annotation(com.tjsj.common.annotation.SchedulerSwitchControl)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        // 判断如果当前调用不是定时任务调用（比如是http请求），则直接执行方法
        boolean scheduledTaskCall = isScheduledTaskCall();
        if (!scheduledTaskCall) {
            return joinPoint.proceed();
        }

        // 获取方法签名和注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        SchedulerSwitchControl annotation = method.getAnnotation(SchedulerSwitchControl.class);

        if (annotation.scheduled() && !tarkinConfig.getScheduler().isEnabled()) {
            return getDefaultReturnValue(method.getReturnType());
        }

        // 如果注解未启用，直接执行方法
        if (!annotation.enabled()) {
            return joinPoint.proceed();
        }

        // 检查是否为定时任务调用
        if (!isScheduledTaskCall()) {
            // 不是定时任务调用（如HTTP请求），直接执行方法
            return joinPoint.proceed();
        }

        // 检查定时任务开关状态
        if (serviceSwitchManager.isSchedulerDisabled()) {
            // 定时任务已停用，不执行方法
            String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();
            String description = annotation.value().isEmpty() ? methodName : annotation.value();

            // 记录拦截日志
//            log.warn("╔═══════════════════════════════════════════════════════════════╗");
//            log.warn("║                    定时任务执行被开关拦截                     ║");
//            log.warn("╠═══════════════════════════════════════════════════════════════╣");
//            log.warn("║ 方法名称: {}                     ║", methodName);
//            log.warn("║ 描述信息: {}                                        ║", description);
//            log.warn("║ 拦截原因: 定时任务总开关已停用                                ║");
//            log.warn("║ 拦截时间: {}                           ║", LocalDateTime.now());
//            log.warn("╚═══════════════════════════════════════════════════════════════╝");

            // 根据方法返回类型返回默认值
            return getDefaultReturnValue(method.getReturnType());
        }

        // 定时任务开关启用，正常执行方法
        return joinPoint.proceed();
    }

    /**
     * 检查当前调用是否为定时任务调用
     * 通过检查调用栈中是否包含定时任务相关的类来判断
     *
     * @return true-是定时任务调用，false-不是定时任务调用
     */
    private boolean isScheduledTaskCall() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();

        for (StackTraceElement element : stackTrace) {
            String className = element.getClassName();
            String methodName = element.getMethodName();

            // 检查是否为Spring定时任务调用
            if (className.contains("org.springframework.scheduling") ||
                    className.contains("org.quartz") ||
                    className.contains("java.util.concurrent.ScheduledThreadPoolExecutor") ||
                    className.contains("org.springframework.aop.interceptor.AsyncExecutionInterceptor") ||
                    "run".equals(methodName) && className.contains("ScheduledMethodRunnable")) {
                return true;
            }
        }

        return false;
    }

    /**
     * 根据方法返回类型获取默认返回值
     *
     * @param returnType 返回类型
     * @return 默认返回值
     */
    private Object getDefaultReturnValue(Class<?> returnType) {
        if (returnType == void.class || returnType == Void.class) {
            return null;
        } else if (returnType == boolean.class || returnType == Boolean.class) {
            return false;
        } else if (returnType == int.class || returnType == Integer.class) {
            return 0;
        } else if (returnType == long.class || returnType == Long.class) {
            return 0L;
        } else if (returnType == double.class || returnType == Double.class) {
            return 0.0;
        } else if (returnType == float.class || returnType == Float.class) {
            return 0.0f;
        } else if (returnType == String.class) {
            return null;
        } else {
            return null;
        }
    }
}
