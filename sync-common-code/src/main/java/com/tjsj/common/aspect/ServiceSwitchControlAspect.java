package com.tjsj.common.aspect;

import com.tjsj.common.annotation.ServiceSwitchControl;
import com.tjsj.common.manager.ServiceSwitchManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * ServiceSwitchControlAspect
 *
 * <AUTHOR>
 * @date 2025/06/30
 * @description 服务开关控制切面 - 拦截使用@ServiceSwitchControl注解的方法
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class ServiceSwitchControlAspect {

    private final ServiceSwitchManager serviceSwitchManager;

    /**
     * 环绕通知：拦截使用@ServiceSwitchControl注解的方法
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    @Around("@annotation(com.tjsj.common.annotation.ServiceSwitchControl)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        
        // 获取方法签名和注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ServiceSwitchControl annotation = method.getAnnotation(ServiceSwitchControl.class);
        
        // 如果注解未启用，直接执行方法
        if (!annotation.enabled()) {
            return joinPoint.proceed();
        }
        
        // 检查服务开关状态
        if (serviceSwitchManager.isServiceDisabled()) {
            // 服务已停用，不执行方法
            String methodName = method.getDeclaringClass().getSimpleName() + "." + method.getName();
            String description = annotation.value().isEmpty() ? methodName : annotation.value();
            
//            log.warn("╔═══════════════════════════════════════════════════════════════╗");
//            log.warn("║                    方法执行被服务开关拦截                     ║");
//            log.warn("╠═══════════════════════════════════════════════════════════════╣");
//            log.warn("║ 方法名称: {}                                  ║", methodName);
//            log.warn("║ 描述信息: {}                                  ║", description);
//            log.warn("║ 拦截原因: 服务总开关已停用                                    ║");
//            log.warn("║ 拦截时间: {}                          ║", java.time.LocalDateTime.now());
//            log.warn("╚═══════════════════════════════════════════════════════════════╝");
            
            // 根据方法返回类型返回默认值
            Class<?> returnType = method.getReturnType();
            if (returnType == void.class || returnType == Void.class) {
                return null;
            } else if (returnType == boolean.class || returnType == Boolean.class) {
                return false;
            } else if (returnType.isPrimitive()) {
                // 其他基本类型返回0
                if (returnType == int.class || returnType == Integer.class) return 0;
                if (returnType == long.class || returnType == Long.class) return 0L;
                if (returnType == double.class || returnType == Double.class) return 0.0;
                if (returnType == float.class || returnType == Float.class) return 0.0f;
                if (returnType == short.class || returnType == Short.class) return (short) 0;
                if (returnType == byte.class || returnType == Byte.class) return (byte) 0;
                if (returnType == char.class || returnType == Character.class) return '\0';
                return null;
            } else {
                // 对象类型返回null
                return null;
            }
        }
        
        // 服务正常，执行方法
        return joinPoint.proceed();
    }
}
