package com.tjsj.common.aspect;

import com.tjsj.common.constants.LogConsts;
import com.tjsj.modules.log.mapper.LogExceptionMapper;
import com.tjsj.modules.log.service.LogService;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;


/**
 * SysLogAspect
 *
 * <AUTHOR>
 * @date 2024/07/09
 * @description 系统日志方面
 */
@Aspect
@Component
@RequiredArgsConstructor
public class SysLogAspect {

    private final LogService logService;

    /**
     * 操作日志点切割 定义切入点，匹配带有 @Operation 注解的方法
     *
     * <AUTHOR>
     * @date 2024/07/13
     */
    @Pointcut("@annotation(io.swagger.v3.oas.annotations.Operation)")
    public void operationLogPointCut() {
    }

    /**
     * api操作日志点切割 定义切入点，匹配带有 @ApiOperation 注解的方法
     *
     * <AUTHOR>
     * @date 2024/07/13
     */
    @Pointcut("@annotation(io.swagger.annotations.ApiOperation)")
    public void apiOperationLogPointCut() {
    }

    /**
     * 对数点切割 组合切入点，匹配带有 @Operation 或 @ApiOperation 注解的方法
     *
     * <AUTHOR> Ye
     * @date 2024/07/13
     */
    @Pointcut("operationLogPointCut() || apiOperationLogPointCut()")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {

        long beginTime = System.currentTimeMillis();
        //执行方法
        Object result = null;
        String exception = null;
        String logLevel = LogConsts.T_LOG_INFO;
        try {
            // 获取方法执行结果
            result = point.proceed();
        } catch (Throwable e) {
            exception = e.toString();
            logLevel = LogConsts.T_LOG_ERROR;
            throw e;
        } finally {
            //执行时长(毫秒)
            long time = System.currentTimeMillis() - beginTime;
            //保存日志
            logService.saveSysLog(point, time, result, exception, logLevel);
        }
        return result;
    }


}
