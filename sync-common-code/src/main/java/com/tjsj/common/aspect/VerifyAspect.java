package com.tjsj.common.aspect;

import com.tjsj.common.annotation.authorize.SystemUserOnly;
import com.tjsj.common.annotation.env.AllowedProfile;
import com.tjsj.common.annotation.env.CurServiceOnly;
import com.tjsj.common.enums.base.ProfileTypeEnum;
import com.tjsj.common.utils.johnye.CustomUtils;
import com.tjsj.common.utils.johnye.UserUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;

/**
 * VerifyAspect
 *
 * <AUTHOR> Ye
 * @date 2024/7/31 10:32
 * @description 验证切面
 */
@Aspect
@Component
@Schema(description = "验证切面")
@Order(1)
public class VerifyAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(VerifyAspect.class);

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Resource
    private CustomUtils customUtils;

    @Resource
    private Environment environment;


    @Pointcut("@annotation(com.tjsj.common.annotation.env.TarkinEnvOnly)")
    public void tarkinEnvOnlyPointcut() {
    }

    @Pointcut("@annotation(com.tjsj.common.annotation.authorize.SystemUserOnly)")
    public void systemUserOnlyPointcut() {
    }

    @Pointcut("@annotation(com.tjsj.common.annotation.env.CurServiceOnly)")
    public void curServiceOnlyPointcut() {
    }

    @Pointcut("@annotation(com.tjsj.common.annotation.env.AllowedProfile)")
    public void allowedProfilePointcut() {
    }

    /**
     * 获取方法
     *
     * @param joinPoint 连接点
     * @return {@link Method }
     * <AUTHOR> Ye
     * @date 2024/08/19
     */
    private static Method getMethod(ProceedingJoinPoint joinPoint) {
        // 获取目标类
        Class<?> targetClass = joinPoint.getTarget().getClass();
        // 获取方法签名
        String methodName = joinPoint.getSignature().getName();
        // 获取方法参数类型
        Class<?>[] parameterTypes = ((MethodSignature) joinPoint.getSignature()).getMethod().getParameterTypes();
        // 获取目标方法
        try {
            return targetClass.getMethod(methodName, parameterTypes);
        } catch (NoSuchMethodException e) {
            return null;
        }
    }

    @Around(value = "allowedProfilePointcut()")
    public Object aroundAllowedProfile(ProceedingJoinPoint joinPoint) throws Throwable {

        Method method = getMethod(joinPoint);
        if (method != null) {
            // 检查方法是否有 @AllowedProfile 注解
            if (method.isAnnotationPresent(AllowedProfile.class)) {
                // 判断当前环境是否在允许的环境列表中
                ProfileTypeEnum[] profileTypes = method.getAnnotation(AllowedProfile.class).allow();
                long count = Arrays.stream(profileTypes)
                        .filter(serviceName -> serviceName.getCode().equals(activeProfile))
                        .count();
                if (count == 0) {
                    return null;
                }

            }
        }
        return joinPoint.proceed();
    }

    @Around(value = "curServiceOnlyPointcut()")
    public Object aroundCurServiceOnly(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取当前服务的名称
        String currentServiceName = environment.getProperty("spring.application.name");

        Method method = getMethod(joinPoint);
        if (method != null) {
            // 检查方法是否有 @CurServiceOnly 注解
            if (method.isAnnotationPresent(CurServiceOnly.class)) {
                String value = method.getAnnotation(CurServiceOnly.class).value();
                // 如果当前服务的名称和注解中指定的服务名称一致，则执行目标方法
                if (Objects.equals(value, currentServiceName)) {
                    return joinPoint.proceed();
                }
            }
        }
        return null;
    }

    @Around(value = "systemUserOnlyPointcut()")
    public Object aroundSystemUserOnly(ProceedingJoinPoint joinPoint) throws Throwable {

        Method method = getMethod(joinPoint);
        if (method != null) {
            // 检查方法是否有 @SystemUserOnly 注解，如果有，则检查当前用户是否是系统用户
            if (method.isAnnotationPresent(SystemUserOnly.class)) {
                if (UserUtils.isSystemUser()) {
                    return joinPoint.proceed();
                }
            }
        }
        return null;
    }


}
