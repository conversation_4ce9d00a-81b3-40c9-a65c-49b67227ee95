package com.tjsj.common.aspect;

import com.tjsj.common.annotation.lock.SingleInstanceLock;
import com.tjsj.common.constants.cache.RedisConsts;
import com.tjsj.common.lock.DistributedLockHelper;
import com.tjsj.common.lock.LockUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * SingleInstanceLockAspect
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/4/11 15:44
 * @description
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class SingleInstanceLockAspect {
    @Resource
    private LockUtil lockUtil;

    private final DistributedLockHelper distributedLockHelper;


    @Pointcut("@annotation(com.tjsj.common.annotation.lock.SingleInstanceLock)")
    public void singleInstanceLockPointcut() {
        // 定义一个空方法作切点
    }

    @Around("singleInstanceLockPointcut() && @annotation(lockAnno)")
    public Object around(ProceedingJoinPoint pjp, SingleInstanceLock lockAnno) throws Throwable {

        String methodSignature = pjp.getSignature().getDeclaringTypeName() + "." + pjp.getSignature().getName();

        // 1. 解析注解参数
        String key = lockAnno.key();
        // 如果没设置key，使用方法签名作为默认锁key
        if (key.isEmpty()) {
            key = methodSignature;
        }
        key = lockUtil.generateLockKey(RedisConsts.KEY_LOCK + key);

        // 等待时间和时间单位
        long waitTime = lockAnno.waitTime();
        TimeUnit waitTimeUnit = lockAnno.waitTimeUnit();
        // 租约时间和时间单位
        long leaseTime = lockAnno.leaseTime();
        TimeUnit leaseTimeUnit = lockAnno.leaseTimeUnit();


        // 检查方法返回类型，判断是否为void方法
        boolean isVoidMethod = false;
        try {
            if (pjp.getSignature() instanceof org.aspectj.lang.reflect.MethodSignature methodSig) {
                isVoidMethod = methodSig.getReturnType() == void.class;
            }
        } catch (Exception e) {
            log.debug("无法获取方法返回类型，默认按非void方法处理: {}", e.getMessage());
        }

        // 2. 使用统一的分布式锁工具类执行业务逻辑
        try {
            String finalKey = key;
            Object result = distributedLockHelper.executeWithLock(
                    key,
                    waitTime,                   // 等待时间
                    waitTimeUnit,               // 等待时间单位
                    leaseTime,                  // 租约时间
                    leaseTimeUnit,              // 租约时间单位
                    () -> {
                        try {
                            log.debug("成功获取分布式锁，开始执行业务逻辑，key={}, 方法: {}", finalKey, methodSignature);
                            return pjp.proceed();
                        } catch (Throwable throwable) {
                            // 将Throwable包装为RuntimeException，因为LockCallback不允许抛出Throwable
                            if (throwable instanceof RuntimeException) {
                                throw (RuntimeException) throwable;
                            } else if (throwable instanceof Error) {
                                throw (Error) throwable;
                            } else {
                                throw new RuntimeException("方法执行异常: " + methodSignature, throwable);
                            }
                        }
                    }
            );

            // 只有在非void方法且result为null时才认为是获取锁失败
            if (result == null && !isVoidMethod) {
                log.warn("竞争获取分布式锁失败，等待超时，key={}, 方法: {}", key, methodSignature);
            }

            return result;

        } catch (RuntimeException e) {
            // 检查是否是包装的原始异常
            if (e.getMessage() != null && e.getMessage().startsWith("方法执行异常: ") && e.getCause() != null) {
                Throwable originalException = e.getCause();
                if (originalException instanceof Exception) {
                    throw (Exception) originalException;
                } else if (originalException instanceof Error) {
                    throw (Error) originalException;
                } else {
                    throw originalException;
                }
            }
            throw e;
        }

    }

}
