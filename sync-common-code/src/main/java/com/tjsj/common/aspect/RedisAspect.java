package com.tjsj.common.aspect;

import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Configuration;

/**
 * RedisAspect
 *
 * <AUTHOR>
 * @version 1.0.2
 * @date 2024/09/08
 * @description Redis切面处理类
 */
@Aspect
@Configuration
public class RedisAspect {
//    private final Logger logger = LoggerFactory.getLogger(getClass());
//
//    //是否开启redis缓存  true开启   false关闭
//    @Value("${spring.redis.open: false}")
//    private boolean open;
//
//    @Around("execution(* com.tjsj.common.utils.data.RedisUtil.*(..))")
//    public Object around(ProceedingJoinPoint point) throws Throwable {
//        Object result = null;
//        if(open){
//            try{
//                result = point.proceed();
//            }catch (Exception e){
//                logger.error("redis error", e);
//                throw new RRException("Redis服务异常");
//            }
//        }
//        return result;
//    }
}
