package com.tjsj.common.aspect;

import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.modules.log.service.LogAutoService;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;


/**
 * UpdateAspect
 *
 * <AUTHOR>
 * @date 2024/07/31
 * @description 更新数据切面类
 */
@Aspect
@Component
@Schema(description = "更新数据切面类")
@Slf4j
public class UpdateAspect {

    @Resource
    private LogAutoService logAutoService;


    @Pointcut("@annotation(com.tjsj.common.annotation.UpdateAnnotation)")
    public void updatePointCut() {
    }

    @Around(value = "updatePointCut()")
    public Object updateAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        Object proceed;
        try {
            proceed = joinPoint.proceed();
            logAutoService.doLog(joinPoint, start, System.currentTimeMillis(), TaskExecuteStatusEnum.SUCCESS, null);
        } catch (Exception e) {
            log.error("执行方法失败", e);
            logAutoService.doLog(joinPoint, start, System.currentTimeMillis(), TaskExecuteStatusEnum.FAILURE, e);
            // 抛出异常确保事务等机制正常工作
            throw e;
        }
        return proceed;
    }


}
