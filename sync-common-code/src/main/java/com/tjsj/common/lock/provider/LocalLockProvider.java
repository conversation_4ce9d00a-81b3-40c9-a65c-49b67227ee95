package com.tjsj.common.lock.provider;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Stack;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * LocalLockProvider
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/06/23
 * @description 本地锁提供者
 */
@Slf4j
@Component
public class LocalLockProvider implements LockProvider {

    private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

    /**
     * 使用栈结构支持嵌套锁调用
     * 解决@SingleInstanceLock注解和distributedLockHelper.executeWithLock同时使用时的锁上下文覆盖问题
     */
    private final ThreadLocal<Stack<LockContext>> lockContextStack = new ThreadLocal<Stack<LockContext>>() {
        @Override
        protected Stack<LockContext> initialValue() {
            return new Stack<>();
        }
    };

    @Override
    public boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit waitTimeUnit, TimeUnit leaseTimeUnit) {
        try {
            ReentrantLock lock = lockMap.computeIfAbsent(lockKey, k -> new ReentrantLock());
            // 本地锁使用等待时间和等待时间单位，租约时间在本地锁中不适用（由JVM GC管理）
            boolean acquired = lock.tryLock(waitTime, waitTimeUnit);
            
            if (acquired) {
                // 将锁上下文压入栈中，支持嵌套锁调用
                LockContext lockContext = new LockContext(
                    lockKey,
                    Thread.currentThread().getName(), // 本地锁使用线程名作为实例ID
                    System.currentTimeMillis(),
                    "LOCAL"
                );
                lockContextStack.get().push(lockContext);

                log.debug("成功获取本地锁，锁Key: {}, 线程: {}, 栈深度: {}",
                         lockKey, Thread.currentThread().getName(), lockContextStack.get().size());
            } else {
                log.warn("获取本地锁失败，等待超时，锁Key: {}", lockKey);
            }
            
            return acquired;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("本地锁获取被中断，锁Key: {}", lockKey, e);
            return false;
        } catch (Exception e) {
            log.error("本地锁获取异常，锁Key: {}", lockKey, e);
            return false;
        }
    }

    @Override
    public boolean releaseLock(String lockKey) {
        try {
            Stack<LockContext> stack = lockContextStack.get();

            if (stack.isEmpty()) {
                log.warn("释放本地锁失败，锁上下文栈为空，锁Key: {}", lockKey);
                return false;
            }

            // 从栈顶获取最近的锁上下文
            LockContext lockContext = stack.peek();

            // 验证锁Key是否匹配（支持LIFO释放顺序）
            if (!lockKey.equals(lockContext.getLockKey())) {
                log.warn("释放本地锁失败，锁Key不匹配，期望: {}, 实际: {}, 栈深度: {}",
                        lockKey, lockContext.getLockKey(), stack.size());
                return false;
            }

            // 弹出锁上下文
            stack.pop();

            // 释放本地锁
            ReentrantLock lock = lockMap.get(lockKey);
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();

                // 如果没有线程等待，移除锁对象以避免内存泄漏
                if (!lock.hasQueuedThreads()) {
                    lockMap.remove(lockKey);
                }

                log.debug("成功释放本地锁，锁Key: {}, 线程: {}, 剩余栈深度: {}",
                         lockKey, Thread.currentThread().getName(), stack.size());

                // 如果栈为空，清理ThreadLocal
                if (stack.isEmpty()) {
                    lockContextStack.remove();
                }

                return true;
            } else {
                log.warn("释放本地锁失败，锁不存在或不属于当前线程，锁Key: {}", lockKey);

                // 如果栈为空，清理ThreadLocal
                if (stack.isEmpty()) {
                    lockContextStack.remove();
                }

                return false;
            }

        } catch (Exception e) {
            log.error("本地锁释放异常，锁Key: {}", lockKey, e);
            // 异常情况下清理ThreadLocal
            lockContextStack.remove();
            return false;
        }
    }

    @Override
    public LockType getLockType() {
        return LockType.LOCAL;
    }
}
