package com.tjsj.common.lock.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.lock.mapper.DistributedLockMapper;
import com.tjsj.common.lock.mapper.LocalDistributedLockMapper;
import com.tjsj.common.lock.model.entity.DistributedLockDO;
import com.tjsj.common.lock.service.DistributedLockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * DistributedLockServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/06/23
 * @description 分布式锁Service实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributedLockServiceImpl extends ServiceImpl<DistributedLockMapper, DistributedLockDO>
        implements DistributedLockService {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final DistributedLockMapper distributedLockMapper;

    private final LocalDistributedLockMapper localDistributedLockMapper;

    @Override
    @Deprecated
    public boolean tryLock(String lockKey, long leaseTime, String instanceId) {

        return false;
//        // 清理过期锁
//        cleanExpiredLocks();
//
//        // 计算过期时间
//        LocalDateTime expireTime = LocalDateTime.now().plusSeconds(leaseTime);
//        LocalDateTime createTime = LocalDateTime.now();
//
//        // 尝试获取锁
//        int affected = distributedLockMapper.tryLock(
//                lockKey,
//                instanceId,
//                expireTime.format(FORMATTER),
//                createTime.format(FORMATTER)
//        );
//
//        if (affected > 0) {
//            // 验证是否真的获取到了锁
//            return distributedLockMapper.verifyLockOwnership(lockKey, instanceId) > 0;
//        }
//
//        return false;

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean tryLockWithNewTransaction(String lockKey, long leaseTime, String instanceId) {

        try {
            // 清理过期锁
            cleanExpiredLocks();

            // 计算过期时间
            LocalDateTime expireTime = LocalDateTime.now().plusSeconds(leaseTime);
            LocalDateTime createTime = LocalDateTime.now();

            // 尝试获取锁
//            int affected = distributedLockMapper.tryLock(
//                lockKey,
//                instanceId,
//                expireTime.format(FORMATTER),
//                createTime.format(FORMATTER)
//            );
            int affected = localDistributedLockMapper.tryLock(
                    lockKey,
                    instanceId,
                    expireTime.format(FORMATTER),
                    createTime.format(FORMATTER)
            );

            if (affected > 0) {
                // 验证是否真的获取到了锁
//                return baseMapper.verifyLockOwnership(lockKey, instanceId) > 0;
                return localDistributedLockMapper.verifyLockOwnership(lockKey, instanceId) > 0;
            }

            return false;

        } catch (DataAccessException e) {
            log.error("数据库锁获取失败（新事务）, lockKey: {}, instanceId: {}", lockKey, instanceId, e);
            return false;
        }
    }

    @Override
    public boolean releaseLock(String lockKey, String instanceId) {
        try {
//            int affected = distributedLockMapper.releaseLock(lockKey, instanceId);
            int affected = localDistributedLockMapper.releaseLock(lockKey, instanceId);

            if (affected > 0) {
                log.debug("成功释放数据库锁, lockKey: {}, instanceId: {}", lockKey, instanceId);
                return true;
            } else {
                log.warn("释放数据库锁失败，可能锁不存在或不属于当前实例, lockKey: {}, instanceId: {}", lockKey,
                        instanceId);
                return false;
            }

        } catch (DataAccessException e) {
            log.error("数据库锁释放失败, lockKey: {}, instanceId: {}", lockKey, instanceId, e);
            return false;
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean releaseLockWithNewTransaction(String lockKey, String instanceId) {
        try {
//            int affected = distributedLockMapper.releaseLock(lockKey, instanceId);
            int affected = localDistributedLockMapper.releaseLock(lockKey, instanceId);

            if (affected > 0) {
                log.debug("成功释放数据库锁（新事务）, lockKey: {}, instanceId: {}", lockKey, instanceId);
                return true;
            } else {
                log.warn("释放数据库锁失败（新事务），可能锁不存在或不属于当前实例, lockKey: {}, instanceId: {}", lockKey,
                        instanceId);
                return false;
            }

        } catch (DataAccessException e) {
            log.error("数据库锁释放失败（新事务）, lockKey: {}, instanceId: {}", lockKey, instanceId, e);
            return false;
        }
    }


    @Override
    public int cleanExpiredLocks() {
        try {
//            int affected = distributedLockMapper.cleanExpiredLocks();
            int affected = localDistributedLockMapper.cleanExpiredLocks();

            if (affected > 0) {
                log.debug("清理了 {} 个过期的数据库锁", affected);
            }

            return affected;

        } catch (DataAccessException e) {
            log.warn("清理过期数据库锁失败", e);
            return 0;
        }
    }

    @Override
    public String getInstanceId() {

        // 可以使用机器名 + 进程ID + 时间戳等组合
        return System.getProperty("user.name") + "_" +
                System.getProperty("PID", "unknown") + "_" +
                System.currentTimeMillis();
    }

}
