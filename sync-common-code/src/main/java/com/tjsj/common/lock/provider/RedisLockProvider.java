package com.tjsj.common.lock.provider;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Stack;
import java.util.concurrent.TimeUnit;

/**
 * RedisLockProvider
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/06/23
 * @description Redis锁提供者
 */
@Slf4j
@Component
public class RedisLockProvider implements LockProvider {

    @Autowired(required = false)
    private RedissonClient redissonClient;

    /**
     * 使用栈结构支持嵌套锁调用
     * 解决@SingleInstanceLock注解和distributedLockHelper.executeWithLock同时使用时的锁上下文覆盖问题
     */
    private final ThreadLocal<Stack<LockContext>> lockContextStack = new ThreadLocal<Stack<LockContext>>() {
        @Override
        protected Stack<LockContext> initialValue() {
            return new Stack<>();
        }
    };

    @Override
    public boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit waitTimeUnit, TimeUnit leaseTimeUnit) {
        // 检查RedissonClient是否可用
        if (redissonClient == null) {
            log.warn("RedissonClient未配置，无法使用Redis锁，锁Key: {}", lockKey);
            throw new RuntimeException("RedissonClient未配置");
        }

        try {
            RLock lock = redissonClient.getLock(lockKey);
            // 将时间转换为毫秒，因为Redisson的tryLock需要统一的时间单位
            long waitTimeMs = waitTimeUnit.toMillis(waitTime);
            long leaseTimeMs = leaseTimeUnit.toMillis(leaseTime);
            boolean acquired = lock.tryLock(waitTimeMs, leaseTimeMs, TimeUnit.MILLISECONDS);
            
            if (acquired) {
                // 将锁上下文压入栈中，支持嵌套锁调用
                LockContext lockContext = new LockContext(
                    lockKey,
                    lock.getName(), // Redis锁使用锁名称作为实例ID
                    System.currentTimeMillis(),
                    "REDIS",
                    lock // 保存RLock对象用于释放
                );
                lockContextStack.get().push(lockContext);

//                log.debug("成功获取Redis分布式锁，锁Key: {}, 锁名称: {}, 栈深度: {}",
//                         lockKey, lock.getName(), lockContextStack.get().size());
            } else {
                log.warn("获取Redis分布式锁失败，等待超时，锁Key: {}", lockKey);
            }
            
            return acquired;
            
        } catch (Exception e) {
            log.error("Redis锁获取异常，锁Key: {}", lockKey, e);
            throw new RuntimeException("Redis锁获取异常", e);
        }
    }

    @Override
    public boolean releaseLock(String lockKey) {
        // 检查RedissonClient是否可用
        if (redissonClient == null) {
            log.warn("RedissonClient未配置，无法释放Redis锁，锁Key: {}", lockKey);
            return false;
        }

        try {
            Stack<LockContext> stack = lockContextStack.get();

            if (stack.isEmpty()) {
                log.warn("释放Redis锁失败，锁上下文栈为空，锁Key: {}", lockKey);
                return false;
            }

            // 从栈顶获取最近的锁上下文
            LockContext lockContext = stack.peek();

            // 验证锁Key是否匹配（支持LIFO释放顺序）
            if (!lockKey.equals(lockContext.getLockKey())) {
                log.warn("释放Redis锁失败，锁Key不匹配，期望: {}, 实际: {}, 栈深度: {}",
                        lockKey, lockContext.getLockKey(), stack.size());
                return false;
            }

            // 弹出锁上下文
            stack.pop();

            // 获取RLock对象并释放
            RLock lock = (RLock) lockContext.getLockObject();
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();

                log.debug("成功释放Redis分布式锁，锁Key: {}, 锁名称: {}, 剩余栈深度: {}",
                         lockKey, lock.getName(), stack.size());

                // 如果栈为空，清理ThreadLocal
                if (stack.isEmpty()) {
                    lockContextStack.remove();
                }

                return true;
            } else {
                log.warn("释放Redis锁失败，锁不存在或不属于当前线程，锁Key: {}", lockKey);

                // 如果栈为空，清理ThreadLocal
                if (stack.isEmpty()) {
                    lockContextStack.remove();
                }

                return false;
            }

        } catch (Exception e) {
            log.error("Redis锁释放异常，锁Key: {}", lockKey, e);
            // 异常情况下清理ThreadLocal
            lockContextStack.remove();
            return false;
        }
    }

    @Override
    public LockType getLockType() {
        return LockType.REDIS;
    }
}
