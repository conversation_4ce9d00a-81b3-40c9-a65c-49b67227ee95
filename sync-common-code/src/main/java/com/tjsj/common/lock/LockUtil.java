package com.tjsj.common.lock;


import com.tjsj.common.config.TarkinConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * LockUtil
 *
 * <AUTHOR>
 * @date 2025/07/02
 * @version 1.0.0
 * @description 锁工具
 */
@Component
public class LockUtil {

    @Resource
    private TarkinConfig tarkinConfig;


    /**
     * 生成锁密钥
     *
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2025/07/02
     */
    public String generateLockKey(String key) {

        return String.format("%s_%s_%s_%s", key, tarkinConfig.getProjectId(), tarkinConfig.getEnv(),
                tarkinConfig.getProfile());
    }

}
