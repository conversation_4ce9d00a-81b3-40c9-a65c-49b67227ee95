package com.tjsj.common.lock.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * DistributedLockDO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/06/23
 * @description 分布式锁实体类
 */
@Data
@Accessors(chain = true)
@TableName("tj_middle_ground.distributed_lock")
@Alias(value = "DistributedLockDO")
@Schema(name = "DistributedLockDO", description = "分布式锁实体")
public class DistributedLockDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 锁的键名（主键）
     */
    @TableId(value = "lock_key", type = IdType.INPUT)
    @Schema(description = "锁的键名（主键）")
    private String lockKey;

    /**
     * 实例ID（机器名+进程ID等）
     */
    @TableField("instance_id")
    @Schema(description = "实例ID（机器名+进程ID等）")
    private String instanceId;

    /**
     * 锁过期时间
     */
    @TableField("expire_time")
    @Schema(description = "锁过期时间")
    private LocalDateTime expireTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
