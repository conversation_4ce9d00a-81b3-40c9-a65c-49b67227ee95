package com.tjsj.common.lock.provider;

import lombok.Data;

/**
 * LockContext
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/06/23
 * @description 锁上下文，用于支持嵌套锁调用
 */
@Data
public class LockContext {
    
    /**
     * 锁的键
     */
    private String lockKey;
    
    /**
     * 实例ID
     */
    private String instanceId;
    
    /**
     * 锁获取时间戳
     */
    private long acquireTime;
    
    /**
     * 锁类型描述
     */
    private String lockType;

    /**
     * 锁对象（用于Redis锁保存RLock对象）
     */
    private Object lockObject;

    public LockContext(String lockKey, String instanceId, long acquireTime, String lockType) {
        this.lockKey = lockKey;
        this.instanceId = instanceId;
        this.acquireTime = acquireTime;
        this.lockType = lockType;
    }

    public LockContext(String lockKey, String instanceId, long acquireTime, String lockType, Object lockObject) {
        this.lockKey = lockKey;
        this.instanceId = instanceId;
        this.acquireTime = acquireTime;
        this.lockType = lockType;
        this.lockObject = lockObject;
    }
}
