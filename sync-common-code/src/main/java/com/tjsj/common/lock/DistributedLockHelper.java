package com.tjsj.common.lock;

import com.tjsj.common.config.DistributedLockConfig;
import com.tjsj.common.lock.provider.DatabaseLockProvider;
import com.tjsj.common.lock.provider.LocalLockProvider;
import com.tjsj.common.lock.provider.LockProvider;
import com.tjsj.common.lock.provider.RedisLockProvider;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * DistributedLockHelper
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/4/29 13:17
 * @description 分布式锁工具类
 */
@Slf4j
@Component
public class DistributedLockHelper {

    @Autowired(required = false)
    private RedissonClient redissonClient;

    @Autowired
    private DistributedLockConfig lockConfig;

    @Autowired
    private RedisLockProvider redisLockProvider;

    @Autowired
    private DatabaseLockProvider databaseLockProvider;

    @Autowired
    private LocalLockProvider localLockProvider;

    /**
     * 尝试获取锁并执行业务逻辑（支持返回值，自定义时间单位）
     *
     * @param key           锁的Key
     * @param waitTime      等待锁的最大时间
     * @param waitTimeUnit  等待时间的单位
     * @param leaseTime     持有锁的最大时间，超时自动释放
     * @param leaseTimeUnit 租约时间的单位
     * @param callback      成功拿到锁后要执行的逻辑
     * @param <T>           返回结果类型
     * @return 业务逻辑执行结果
     */
    public <T> T executeWithLock(String key, Long waitTime, TimeUnit waitTimeUnit,
                                 Long leaseTime, TimeUnit leaseTimeUnit, LockCallback<T> callback) {

        // 参数默认值处理
        long actualWaitTime = waitTime == null ? 100L : waitTime;
        TimeUnit actualWaitTimeUnit = waitTimeUnit == null ? TimeUnit.MINUTES : waitTimeUnit;
        long actualLeaseTime = leaseTime == null ? 1L : leaseTime;
        TimeUnit actualLeaseTimeUnit = leaseTimeUnit == null ? TimeUnit.HOURS : leaseTimeUnit;

        // 选择锁提供者并执行
        LockProvider lockProvider = selectLockProvider();
        return executeWithLockProvider(lockProvider, key, actualWaitTime, actualLeaseTime,
                actualWaitTimeUnit, actualLeaseTimeUnit, callback);
    }

    /**
     * 尝试获取锁并执行业务逻辑（支持返回值，默认分钟单位）
     *
     * @param key           锁的Key
     * @param waitTimeMin   等待锁的最大时间（分钟）
     * @param leaseTimeMin  持有锁的最大时间（分钟），超时自动释放
     * @param callback      成功拿到锁后要执行的逻辑
     * @param <T>           返回结果类型
     * @return 业务逻辑执行结果
     */
    public <T> T executeWithLock(String key, Long waitTimeMin, Long leaseTimeMin, LockCallback<T> callback) {
        return executeWithLock(key, waitTimeMin, TimeUnit.MINUTES, leaseTimeMin, TimeUnit.MINUTES, callback);
    }

    /**
     * 尝试获取锁并执行业务逻辑（无返回值，自定义时间单位）
     *
     * @param key           锁的Key
     * @param waitTime      等待锁的最大时间
     * @param waitTimeUnit  等待时间的单位
     * @param leaseTime     持有锁的最大时间，超时自动释放
     * @param leaseTimeUnit 租约时间的单位
     * @param callback      成功拿到锁后要执行的逻辑
     */
    public void executeWithLock(String key, Long waitTime, TimeUnit waitTimeUnit,
                                Long leaseTime, TimeUnit leaseTimeUnit, Runnable callback) {

        executeWithLock(key, waitTime, waitTimeUnit, leaseTime, leaseTimeUnit, () -> {
            callback.run();
            return null;
        });
    }

    /**
     * 尝试获取锁并执行业务逻辑（无返回值，默认分钟单位）
     *
     * @param key           锁的Key
     * @param waitTimeMin   等待锁的最大时间（分钟）
     * @param leaseTimeMin  持有锁的最大时间（分钟），超时自动释放
     * @param callback      成功拿到锁后要执行的逻辑
     */
    public void executeWithLock(String key, Long waitTimeMin, Long leaseTimeMin, Runnable callback) {

        executeWithLock(key, waitTimeMin, TimeUnit.MINUTES, leaseTimeMin, TimeUnit.MINUTES, callback);
    }

    /**
     * 使用默认参数执行带锁的业务逻辑（等待1分钟，持有锁10分钟）
     *
     * @param key      锁的Key
     * @param callback 业务逻辑
     * @param <T>      返回结果类型
     * @return 业务逻辑执行结果
     */
    public <T> T executeWithLock(String key, LockCallback<T> callback) {
        return executeWithLock(key, 1L, TimeUnit.MINUTES, 10L, TimeUnit.MINUTES, callback);
    }

    /**
     * 使用默认参数执行带锁的业务逻辑（无返回值）
     *
     * @param key      锁的Key
     * @param callback 业务逻辑
     */
    public void executeWithLock(String key, Runnable callback) {
        executeWithLock(key, 1L, TimeUnit.MINUTES, 10L, TimeUnit.MINUTES, callback);
    }

    /**
     * 选择锁提供者
     * 默认顺序：优先Redis锁，其次是数据库锁，最后是本地锁
     *
     *
     * @return 锁提供者
     * <AUTHOR> Ye
     * @date 2025/06/23
     */
    private LockProvider selectLockProvider() {

        // 优先尝试Redis锁
        if (redissonClient != null && lockConfig.isEnabled()) {
            try {
                // 简单的Redis连接测试
                redissonClient.getKeys().count();
                return redisLockProvider;
            } catch (Exception e) {
                log.warn("Redis连接异常，尝试降级策略: {}", e.getMessage());
            }
        }

        // Redis不可用时的降级策略
        // 如果两个降级策略都启用，则根据配置选择优先级
        if (lockConfig.isEnableDatabaseLockFallback() && lockConfig.isEnableLocalLockFallback()) {

            // 根据配置选择优先级
            if (lockConfig.isDatabaseLockPriority()) {
                log.info("Redis不可用，降级使用数据库锁");
                return databaseLockProvider;
            } else {
                log.info("Redis不可用，降级使用本地锁");
                return localLockProvider;
            }
        } else if (lockConfig.isEnableDatabaseLockFallback()) {
            log.info("Redis不可用，降级使用数据库锁");
            return databaseLockProvider;
        } else if (lockConfig.isEnableLocalLockFallback()) {
            log.info("Redis不可用，降级使用本地锁");
            return localLockProvider;
        } else {
            log.warn("所有锁降级策略都已禁用，将直接执行业务逻辑（无锁保护）");
            return null;
        }

    }

    /**
     * 使用指定的锁提供者执行业务逻辑
     *
     * @param lockProvider 锁提供者
     * @param key 钥匙
     * @param waitTime 等待时间
     * @param leaseTime 租约时间
     * @param waitTimeUnit 等待时间单位
     * @param leaseTimeUnit 租赁时间单位
     * @param callback 召回
     * @return {@link T }
     * <AUTHOR> Ye
     * @date 2025/06/23
     */
    private <T> T executeWithLockProvider(LockProvider lockProvider, String key,
                                          long waitTime, long leaseTime,
                                          TimeUnit waitTimeUnit, TimeUnit leaseTimeUnit,
                                          LockCallback<T> callback) {

        if (lockProvider == null) {
            log.warn("无可用的锁提供者，直接执行业务逻辑，锁Key: {}", key);
            return callback.doWithLock();
        }

        boolean locked = false;
        try {
            // 尝试获取锁
            locked = lockProvider.tryLock(key, waitTime, leaseTime, waitTimeUnit, leaseTimeUnit);

            if (locked) {
                log.debug("成功获取{}，开始执行业务逻辑，锁Key: {}",
                        lockProvider.getLockType().getDescription(), key);

                // 执行业务逻辑，区分业务异常和锁异常
                try {
                    return callback.doWithLock();
                } catch (Exception businessException) {
                    // 业务逻辑异常，直接抛出，不进行降级处理
                    log.error("业务逻辑执行异常，锁Key: {}", key, businessException);
                    throw businessException;
                }
            } else {
                log.warn("竞争获取{}失败，等待超时，锁Key: {}",
                        lockProvider.getLockType().getDescription(), key);
                return null;
            }

        } catch (Exception e) {
            // 检查是否为业务逻辑异常（已经在上面的try-catch中处理过的）
            if (locked) {
                // 如果已经获取到锁，说明异常来自业务逻辑，直接重新抛出
                throw e;
            }

            // 锁操作异常，记录日志并尝试降级
            log.error("使用{}时发生异常，锁Key: {}",
                    lockProvider.getLockType().getDescription(), key, e);

            // 如果当前锁提供者失败，尝试降级
            if (lockProvider.getLockType() == LockProvider.LockType.REDIS) {
                log.warn("Redis锁异常，尝试降级策略");
                LockProvider fallbackProvider = selectFallbackProvider();
                if (fallbackProvider != null) {
                    return executeWithLockProvider(fallbackProvider, key, waitTime, leaseTime,
                            waitTimeUnit, leaseTimeUnit, callback);
                }
            }

            // 如果没有降级策略或降级也失败，直接执行业务逻辑
            log.warn("锁获取失败，直接执行业务逻辑，锁Key: {}", key);
            return callback.doWithLock();

        } finally {
            // 释放锁
            if (locked) {
                try {
                    lockProvider.releaseLock(key);
                } catch (Exception e) {
                    log.warn("释放{}时发生异常，锁Key: {}，异常信息: {}",
                            lockProvider.getLockType().getDescription(), key, e.getMessage());
                }
            }
        }
    }

    /**
     * 选择降级锁提供者
     */
    private LockProvider selectFallbackProvider() {
        // 如果两个降级策略都启用，则根据配置选择优先级
        if (lockConfig.isEnableDatabaseLockFallback() && lockConfig.isEnableLocalLockFallback()) {
            return lockConfig.isDatabaseLockPriority() ? databaseLockProvider : localLockProvider;
        } else if (lockConfig.isEnableDatabaseLockFallback()) {
            return databaseLockProvider;
        } else if (lockConfig.isEnableLocalLockFallback()) {
            return localLockProvider;
        }
        // 如果两个降级策略都禁用，则直接返回null，即不进行锁保护
        return null;
    }

    /**
     * LockCallback
     *
     * <AUTHOR> Ye
     * @version 1.0.0
     * @date 2025/04/29
     * @description 执行成功拿到锁后要做的事情
     */
    @FunctionalInterface
    public interface LockCallback<T> {
        T doWithLock();
    }

}
