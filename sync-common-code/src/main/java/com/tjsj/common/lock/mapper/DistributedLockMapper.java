package com.tjsj.common.lock.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.common.lock.model.entity.DistributedLockDO;
import com.tjsj.datasource.DataSourceNames;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * DistributedLockMapper
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/06/23
 * @description 分布式锁Mapper接口
 */
@Mapper
@DS(DataSourceNames.TARGET_DB)
public interface DistributedLockMapper extends BaseMapper<DistributedLockDO> {

    /**
     * 尝试获取锁（INSERT ... ON DUPLICATE KEY UPDATE）
     *
     * @param lockKey    锁的键
     * @param instanceId 实例ID
     * @param expireTime 过期时间
     * @param createTime 创建时间
     * @return 受影响的行数
     */
    int tryLock(@Param("lockKey") String lockKey,
                @Param("instanceId") String instanceId,
                @Param("expireTime") String expireTime,
                @Param("createTime") String createTime);

    /**
     * 释放锁
     *
     * @param lockKey    锁的键
     * @param instanceId 实例ID
     * @return 受影响的行数
     */
    int releaseLock(@Param("lockKey") String lockKey,
                    @Param("instanceId") String instanceId);

    /**
     * 验证锁的所有权
     *
     * @param lockKey    锁的键
     * @param instanceId 实例ID
     * @return 匹配的记录数
     */
    int verifyLockOwnership(@Param("lockKey") String lockKey,
                            @Param("instanceId") String instanceId);

    /**
     * 清理过期的锁
     *
     * @return 清理的记录数
     */
    int cleanExpiredLocks();
}
