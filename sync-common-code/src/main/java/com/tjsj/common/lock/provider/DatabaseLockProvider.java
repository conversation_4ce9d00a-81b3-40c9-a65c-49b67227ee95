package com.tjsj.common.lock.provider;

import com.tjsj.common.lock.service.DistributedLockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Stack;
import java.util.concurrent.TimeUnit;

/**
 * DatabaseLockProvider
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/06/23
 * @description 数据库锁提供者
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseLockProvider implements LockProvider {

    @Resource
    private DistributedLockService distributedLockService;

    /**
     * 使用栈结构支持嵌套锁调用
     * 解决@SingleInstanceLock注解和distributedLockHelper.executeWithLock同时使用时的instanceId覆盖问题
     */
    private final ThreadLocal<Stack<LockContext>> lockContextStack = new ThreadLocal<Stack<LockContext>>() {
        @Override
        protected Stack<LockContext> initialValue() {
            return new Stack<>();
        }
    };

    @Override
    public boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit waitTimeUnit,
                           TimeUnit leaseTimeUnit) {
        try {
            String instanceId = distributedLockService.getInstanceId();
            // 数据库锁的租约时间需要转换为秒
            long leaseTimeSeconds = leaseTimeUnit.toSeconds(leaseTime);
            // 注意：数据库锁实现中waitTime参数当前未使用，但保留以备将来扩展

            // 使用新事务获取锁，确保锁记录立即提交到数据库
            boolean acquired = distributedLockService.tryLockWithNewTransaction(lockKey, leaseTimeSeconds, instanceId);

            if (acquired) {
                // 将锁上下文压入栈中，支持嵌套锁调用
                LockContext lockContext = new LockContext(
                    lockKey,
                    instanceId,
                    System.currentTimeMillis(),
                    "DATABASE"
                );
                lockContextStack.get().push(lockContext);

                log.info("成功获取数据库分布式锁，锁Key: {}, 实例ID: {}, 栈深度: {}",
                         lockKey, instanceId, lockContextStack.get().size());
            } else {
                log.info("获取数据库分布式锁失败，被其他线程持有，锁Key: {}, 实例ID: {}", lockKey, instanceId);
                return false;
            }

            return acquired;

        } catch (Exception e) {
            log.error("数据库锁获取异常，锁Key: {}", lockKey, e);
            return false;
        }

    }

    @Override
    public boolean releaseLock(String lockKey) {
        try {
            Stack<LockContext> stack = lockContextStack.get();

            if (stack.isEmpty()) {
                log.warn("释放数据库锁失败，锁上下文栈为空，锁Key: {}", lockKey);
                return false;
            }

            // 从栈顶获取最近的锁上下文
            LockContext lockContext = stack.peek();

            // 验证锁Key是否匹配（支持LIFO释放顺序）
            if (!lockKey.equals(lockContext.getLockKey())) {
                log.warn("释放数据库锁失败，锁Key不匹配，期望: {}, 实际: {}, 栈深度: {}",
                        lockKey, lockContext.getLockKey(), stack.size());
                return false;
            }

            // 弹出锁上下文
            stack.pop();

            // 使用新事务释放锁，确保锁记录立即从数据库中删除
            boolean released = distributedLockService.releaseLockWithNewTransaction(
                lockContext.getLockKey(),
                lockContext.getInstanceId()
            );

            if (released) {
                log.debug("成功释放数据库分布式锁，锁Key: {}, 实例ID: {}, 剩余栈深度: {}",
                         lockContext.getLockKey(), lockContext.getInstanceId(), stack.size());
            } else {
                log.warn("释放数据库锁失败，锁Key: {}, 实例ID: {}",
                        lockContext.getLockKey(), lockContext.getInstanceId());
            }

            // 如果栈为空，清理ThreadLocal
            if (stack.isEmpty()) {
                lockContextStack.remove();
            }

            return released;

        } catch (Exception e) {
            log.error("数据库锁释放异常，锁Key: {}", lockKey, e);
            // 异常情况下清理ThreadLocal
            lockContextStack.remove();
            return false;
        }
    }

    @Override
    public LockType getLockType() {
        return LockType.DATABASE;
    }

}
