package com.tjsj.common.lock.provider;

import java.util.concurrent.TimeUnit;

/**
 * LockProvider
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/06/23
 * @description 锁提供者接口，统一Redis锁和数据库锁的操作
 */
public interface LockProvider {

    /**
     * 尝试获取锁
     *
     * @param lockKey       锁的键
     * @param waitTime      等待时间
     * @param leaseTime     租约时间
     * @param waitTimeUnit  等待时间单位
     * @param leaseTimeUnit 租约时间单位
     * @return 是否成功获取锁
     */
    boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit waitTimeUnit, TimeUnit leaseTimeUnit);

    /**
     * 释放锁
     *
     * @param lockKey 锁的键
     * @return 是否成功释放锁
     */
    boolean releaseLock(String lockKey);

    /**
     * 获取锁提供者类型
     *
     * @return 锁类型
     */
    LockType getLockType();

    /**
     * 锁类型枚举
     */
    enum LockType {
        REDIS("Redis分布式锁"),
        DATABASE("数据库分布式锁"),
        LOCAL("本地锁");

        private final String description;

        LockType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

}
