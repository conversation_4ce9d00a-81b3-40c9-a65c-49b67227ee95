package com.tjsj.common.lock.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.common.lock.model.entity.DistributedLockDO;

/**
 * DistributedLockService
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/06/23
 * @description 分布式锁Service接口
 */
public interface DistributedLockService extends IService<DistributedLockDO> {

    /**
     * 尝试获取数据库锁
     *
     * @param lockKey    锁的键
     * @param leaseTime  租约时间（秒）
     * @param instanceId 实例ID
     * @return 是否成功获取锁
     */
    boolean tryLock(String lockKey, long leaseTime, String instanceId);

    /**
     * 尝试获取数据库锁（使用新事务，立即提交）
     * 解决在外层事务中锁记录不能立即生效的问题
     *
     * @param lockKey    锁的键
     * @param leaseTime  租约时间（秒）
     * @param instanceId 实例ID
     * @return 是否成功获取锁
     */
    boolean tryLockWithNewTransaction(String lockKey, long leaseTime, String instanceId);

    /**
     * 释放数据库锁
     *
     * @param lockKey    锁的键
     * @param instanceId 实例ID
     * @return 是否成功释放锁
     */
    boolean releaseLock(String lockKey, String instanceId);

    /**
     * 释放数据库锁（使用新事务，立即提交）
     *
     * @param lockKey    锁的键
     * @param instanceId 实例ID
     * @return 是否成功释放锁
     */
    boolean releaseLockWithNewTransaction(String lockKey, String instanceId);


    /**
     * 清理过期的锁
     *
     * @return 清理的记录数
     */
    int cleanExpiredLocks();

    /**
     * 获取当前实例ID
     *
     * @return 实例ID
     */
    String getInstanceId();
}
