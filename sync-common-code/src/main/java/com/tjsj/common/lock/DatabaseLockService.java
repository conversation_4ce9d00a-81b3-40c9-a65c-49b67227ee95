package com.tjsj.common.lock;

import com.tjsj.common.lock.service.DistributedLockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * DatabaseLockService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/06/23
 * @description 数据库分布式锁服务（Redis 降级方案）
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DatabaseLockService {

    private final DistributedLockService distributedLockService;

    /**
     * 尝试获取数据库锁
     *
     * @param lockKey    锁的键
     * @param leaseTime  租约时间（秒）
     * @param instanceId 实例ID
     * @return 是否成功获取锁
     */
//    public boolean tryLock(String lockKey, long leaseTime, String instanceId) {
//        return distributedLockService.tryLock(lockKey, leaseTime, instanceId);
//    }

    /**
     * 释放数据库锁
     *
     * @param lockKey    锁的键
     * @param instanceId 实例ID
     * @return 是否成功释放锁
     */
    public boolean releaseLock(String lockKey, String instanceId) {
        return distributedLockService.releaseLock(lockKey, instanceId);
    }

    /**
     * 获取当前实例ID
     *
     * @return 实例ID
     */
    public String getInstanceId() {
        return distributedLockService.getInstanceId();
    }
}
