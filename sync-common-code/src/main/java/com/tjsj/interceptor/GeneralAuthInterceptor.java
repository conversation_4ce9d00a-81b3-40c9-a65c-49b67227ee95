package com.tjsj.interceptor;

import com.alibaba.fastjson2.JSONArray;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tjsj.common.annotation.UpdateAnnotation;
import com.tjsj.common.annotation.authorize.ExpertOnly;
import com.tjsj.common.annotation.authorize.PassToken;
import com.tjsj.common.annotation.authorize.SystemUserOnly;
import com.tjsj.common.annotation.env.AllowedProfile;
import com.tjsj.common.constants.UserConsts;
import com.tjsj.common.enums.base.ProfileTypeEnum;
import com.tjsj.common.utils.crypto.TokenUtil;
import com.tjsj.common.utils.string.StrUtils;
import com.tjsj.modules.base.mapper.MyBaseMapper;
import com.tjsj.modules.base.model.ResultData;
import com.tjsj.modules.user.model.entity.UserDO;
import com.tjsj.modules.user.service.UserService;
import io.swagger.v3.oas.annotations.media.Schema;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * GeneralAuthInterceptor
 *
 * <AUTHOR> Ye
 * @date 2024/07/08
 * @description 通用认证拦截器
 */
@Component
@Schema(description = "基础http请求拦截器")
public class GeneralAuthInterceptor implements HandlerInterceptor {

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Resource
    private MyBaseMapper myBaseMapper;

    @Resource
    private UserService userService;

    private static final Logger logger = LoggerFactory.getLogger(GeneralAuthInterceptor.class);


    /**
     * 拦截器在方法到达controller层之前进行预处理
     * 主要用于验证token
     *
     * @param request  请求
     * @param response 响应
     * @param handler  处理器
     * @return boolean 是否继续执行
     * @throws Exception 异常
     */
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response,
                             @NotNull Object handler) throws Exception {

        try {
            // 如果不是映射到方法直接通过
            if (!(handler instanceof HandlerMethod)) {
                return true;
            }
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();

            // 检查方法注释
            if (Objects.equals(checkMethodAnnotation(method, response), false)) {
                return false;
            } else if (Objects.equals(checkMethodAnnotation(method, response), true)) {
                return true;
            }

            // 从 http 请求头中取出 token
            String token = request.getHeader(UserDO.Fields.token);
            if (checkToken(request, response, token)) {
                return true;
            }

            // 获取 token 中的 uid
            UserDO userDO = userService.getOne(Wrappers.<UserDO>lambdaQuery()
                    .eq(UserDO::getUserId, TokenUtil.getCurrentUID()));
            if (userDO == null) {
                return returnResult(response, "用户不存在，请登录！");
            }

            JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(userDO.getPassword())).build();
            jwtVerifier.verify(token);

        } catch (Exception e) {
            return returnResult(response, "账号登录状态已失效，请重新登录");
        }
        return true;
    }

    /**
     * 检查令牌
     *
     * @param request  请求
     * @param response 回答
     * @param token    令牌
     * @return boolean
     * <AUTHOR> Ye
     * @date 2024/07/08
     */
    private boolean checkToken(HttpServletRequest request, HttpServletResponse response,
                               String token) throws Exception {

        // 执行认证
        if (token == null) {
            return returnResult(response, "无鉴权token，请登录！");
        }

        // 特殊token校验通过
        String spealToken = "ao4:J_AE+WFV=uk~g=L2UB.szU";
        if (spealToken.equals(token)) {
            return true;
        }
        return true;
    }

    /**
     * 检查方法注释
     * <p>
     * 如果当前方法返回false,则直接返回false,不执行后续操作
     * 如果当前方法返回true，则直接返回true，preHandle中的其他认证代码不再执行，直接放行
     * </p>
     *
     * @param method   方法
     * @param response 响应
     * @return boolean 是否跳过认证
     * <AUTHOR> Ye
     * @date 2024/08/29
     */
    private Object checkMethodAnnotation(Method method, HttpServletResponse response) throws Exception {

        // 检验方法的执行环境
        if (method.isAnnotationPresent(AllowedProfile.class)) {
            AllowedProfile allowedProfile = method.getAnnotation(AllowedProfile.class);
            // 允许的执行环境数组
            ProfileTypeEnum[] profiles = allowedProfile.allow();
            long count = Arrays.stream(profiles)
                    .filter(profile -> profile.getCode().equals(activeProfile))
                    .count();
            if (count == 0) {
                return false;
            }
        }


        // 检查是否有passtoken注释，有则跳过认证
        if (method.isAnnotationPresent(PassToken.class)) {
            PassToken passToken = method.getAnnotation(PassToken.class);
            if (passToken.required()) {
                return true;
            }
        }
        // 检查是否有update注释，有则跳过认证
        if (method.isAnnotationPresent(UpdateAnnotation.class)) {
            if (UpdateAnnotation.class.isAnnotationPresent(PassToken.class)) {
                return true;
            }
        }

        // 判断是否仅系统管理员可用接口
        if (method.isAnnotationPresent(SystemUserOnly.class)) {
            if (!UserConsts.SYSTEM_USER.equals(TokenUtil.getCurrentUID())) {
                return returnResult(response, "非系统管理员，暂无此权限");
            }
        }


        return null;
    }

    /**
     * 返回结果
     *
     * @param response 响应
     * @param msg      消息
     * @return boolean 是否继续执行
     * @throws Exception 异常
     */
    private boolean returnResult(HttpServletResponse response, String msg) throws Exception {
        response.setCharacterEncoding("utf-8");
        response.getWriter().write(JSONArray.toJSONString(ResultData.FORBIDDEN(msg)));
        return false;
    }

}


