package com.tjsj.interceptor.backup;

/**
 * SqlLogInterceptor
 *
 * @description 该拦截器用于动态禁用和启用MyBatis SQL日志记录。
 * 当你想在特定方法中抑制日志记录但允许其他方法日志记录时非常有用。
 * 日志状态通过一个ThreadLocal变量进行控制。
 * @date 2024/07/18
 */
//@Intercepts({
//        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
//})
//@Component
//public class SqlLogInterceptor implements Interceptor {
//
//    // ThreadLocal变量，用于保存每个线程的日志状态（启用或禁用）。
//    private static final ThreadLocal<Boolean> ENABLE_LOG = ThreadLocal.withInitial(() -> true);
//
//    /**
//     * 设置日志状态。
//     *
//     * @param enable boolean值，用于启用或禁用日志记录。
//     */
//    public static void setEnableLog(boolean enable) {
//        ENABLE_LOG.set(enable);
//    }
//
//    /**
//     * 拦截方法调用。
//     *
//     * @param invocation 被拦截的方法调用。
//     * @return 方法调用的结果。
//     * @throws Throwable 如果方法调用过程中发生任何错误。
//     */
//    @Override
//    public Object intercept(Invocation invocation) throws Throwable {
//        if (!ENABLE_LOG.get()) {
//            // 通过设置一个自定义的NoLogging实现来暂时禁用日志记录
//            LogFactory.useCustomLogging(NoLogging.class);
//        }
//        try {
//            return invocation.proceed();
//        } finally {
//            // 方法调用后恢复标准的日志实现
//            LogFactory.useCustomLogging(StdOutImpl.class);
//        }
//    }
//
//    /**
//     * 包装目标对象。
//     *
//     * @param target 要包装的目标对象。
//     * @return 包装后的对象。
//     */
//    @Override
//    public Object plugin(Object target) {
//        return Plugin.wrap(target, this);
//    }
//
//    /**
//     * 设置拦截器的属性（在此实现中未使用）。
//     *
//     * @param properties 拦截器的属性。
//     */
//    @Override
//    public void setProperties(Properties properties) {
//    }
//}
//
