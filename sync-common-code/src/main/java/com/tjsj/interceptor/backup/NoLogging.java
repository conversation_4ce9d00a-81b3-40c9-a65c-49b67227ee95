package com.tjsj.interceptor.backup;

/**
 * NoLogging
 *
 * @description 一个自定义日志实现，不执行任何操作。用于临时禁用MyBatis日志记录。
 */
//public class NoLogging implements org.apache.ibatis.logging.Log {
//    public NoLogging(String clazz) {
//    }
//
//    @Override
//    public boolean isDebugEnabled() {
//        return false;
//    }
//
//    @Override
//    public boolean isTraceEnabled() {
//        return false;
//    }
//
//    @Override
//    public void error(String s, Throwable e) {
//    }
//
//    @Override
//    public void error(String s) {
//    }
//
//    @Override
//    public void debug(String s) {
//    }
//
//    @Override
//    public void warn(String s) {
//    }
//
//    @Override
//    public void trace(String s) {
//    }
//
//}
