# 东北证券测试数据库同步数据配置
server:
  port: 8870

spring:
  datasource:
    dynamic:
      primary: target
      datasource:
        target: #目标数据库
          url: ***********************************************************************************************************************************************************************************************************************************************************
          username: tarkin
          password: VcB-2wL+9k4U_et6
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
        source: #源数据库
          url: *****************************************************************************************************************************************************************************************************************************************************************
          username: syncdata_user
          password: ENC(HYxcLPlqP4nqTYrzc2yqFGGFBWoNp0SnhhG6aQCSJso=)
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
    druid:
      initial-size: 50
      max-active: 6000
      min-idle: 10
      max-wait: 600000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
      filter:
        wall:
          enabled: false
        stat:
          log-slow-sql: true
          slow-sql-millis: 10
          merge-sql: false
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 去除druid配置


mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.tjsj.**.modules.**.model
  global-config:
    banner: false
    db-config:
      id-type: auto
  configuration:
    cache-enabled: false
    call-setters-on-nulls: true
    #log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    auto-mapping-behavior: full
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    map-underscore-to-camel-case: false
    default-executor-type: batch

dbType:
  dbzq
env: dbzq

scheduler:
  cron:
    sync-cloud-to-local: "0 30 0 * * ?"
    heartbeat-test: "0/5 * * * * ?"
    quartz-heartbeat-test: "*/5 * * * * ?"

sync:
  auto:
    # 每批拉取数据量
    pull-batch-size: 2000
    # 每批插入数据量
    insert-batch-size: 2000
    # 重试间隔时间
    retry-delay-millis: 2000
    # 最大重试次数
    max-retry-times: 10