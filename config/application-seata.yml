# =====================================================
# Seata分布式事务配置 - File模式
# 适用于单机或小规模部署，无需外部注册中心
# =====================================================
seata:
  # 是否启用Seata分布式事务功能
  # true: 启用分布式事务，支持跨数据源ACID特性
  # false: 禁用分布式事务，使用本地事务模式
  # 默认值: false
  enabled: false

  # =====================================================
  # 基础标识配置
  # =====================================================
  # 应用标识符，用于在Seata Server中区分不同的应用
  # 建议使用项目名称，便于监控和管理
  application-id: data-sync-service

  # 事务服务组名称，用于事务路由和负载均衡
  # 必须与file.conf中的vgroupMapping配置对应
  # 格式: 字符串，建议使用项目名-group格式
  tx-service-group: data-sync-group

  # =====================================================
  # 数据源代理配置
  # =====================================================
  # 是否自动代理数据源，启用后Seata会自动拦截SQL操作
  # true: 自动代理，无需手动配置数据源代理
  # false: 手动代理，需要手动配置SeataDataSourceBeanPostProcessor
  # 推荐值: true（简化配置）
  enable-auto-data-source-proxy: true

  # 数据源代理模式，决定分布式事务的实现方式
  # AT: 自动事务模式，基于SQL解析和回滚日志，推荐使用
  # XA: XA事务模式，基于数据库XA协议，性能较低
  # SAGA: 长事务模式，适用于微服务场景
  # TCC: Try-Confirm-Cancel模式，需要业务代码配合
  data-source-proxy-mode: AT

  # 是否使用JDK动态代理
  # false: 使用CGLIB代理（推荐），支持类代理
  # true: 使用JDK动态代理，只支持接口代理
  # 推荐值: false
  use-jdk-proxy: false

  # =====================================================
  # 客户端配置
  # =====================================================
  client:
    # =====================================================
    # RM (Resource Manager) 资源管理器配置
    # 负责管理本地事务资源，如数据库连接
    # =====================================================
    rm:
      # 异步提交缓冲区限制，控制异步提交的批次大小
      # 值越大，批量提交效率越高，但内存占用也越大
      # 推荐值: 10000（适合大多数场景）
      async-commit-buffer-limit: 10000

      # 向TC报告状态的重试次数
      # 当网络异常时，RM会重试向TC报告分支事务状态
      # 推荐值: 5（平衡可靠性和性能）
      report-retry-count: 5

      # 是否启用表元数据检查
      # true: 启用检查，确保表结构一致性，但影响性能
      # false: 禁用检查，提高性能，适合稳定环境
      # 推荐值: false（生产环境）
      table-meta-check-enable: false

      # 是否上报分支事务成功状态
      # true: 上报成功状态，增加网络开销
      # false: 不上报成功状态，减少网络开销
      # 推荐值: false（减少网络流量）
      report-success-enable: false

      # =====================================================
      # 分布式锁配置
      # =====================================================
      lock:
        # 获取锁失败后的重试间隔（秒）
        # 当多个事务竞争同一资源时的等待时间
        # 推荐值: 10（避免频繁重试）
        retry-interval: 10

        # 获取锁的最大重试次数
        # 超过此次数后会抛出锁获取失败异常
        # 推荐值: 30（总等待时间约5分钟）
        retry-times: 30

        # 当发生锁冲突时是否回滚分支事务
        # true: 发生冲突时回滚，保证数据一致性
        # false: 发生冲突时等待，可能导致死锁
        # 推荐值: true（保证一致性）
        retry-policy-branch-rollback-on-conflict: true

    # =====================================================
    # TM (Transaction Manager) 事务管理器配置
    # 负责全局事务的开启、提交、回滚
    # =====================================================
    tm:
      # 全局事务提交失败时的重试次数
      # 当TC提交全局事务失败时，TM会重试提交
      # 推荐值: 5（平衡可靠性和性能）
      commit-retry-count: 5

      # 全局事务回滚失败时的重试次数
      # 当TC回滚全局事务失败时，TM会重试回滚
      # 推荐值: 5（确保回滚成功）
      rollback-retry-count: 5

      # 全局事务默认超时时间（毫秒）
      # 超过此时间未完成的事务将被自动回滚
      # 300000ms = 5分钟，适合数据同步场景
      # 可根据业务复杂度调整：简单业务60000ms，复杂业务600000ms
      # 注意：较短的超时时间有助于快速发现和处理异常停止的服务
      default-global-transaction-timeout: 300000

      # 是否启用事务降级检查
      # true: 启用降级，当TC不可用时自动降级为本地事务
      # false: 禁用降级，TC不可用时事务失败
      # 推荐值: false（保证分布式事务语义）
      degrade-check: false
    # =====================================================
    # Undo Log 回滚日志配置
    # AT模式下用于存储事务回滚信息
    # =====================================================
    undo:
      # 是否启用undo log数据校验
      # true: 启用校验，确保回滚数据的完整性，但影响性能
      # false: 禁用校验，提高性能，但可能存在数据不一致风险
      # 推荐值: true（保证数据一致性）
      data-validation: true

      # undo log的序列化方式
      # jackson: 使用Jackson进行JSON序列化（推荐）
      # fastjson: 使用FastJSON进行序列化
      # kryo: 使用Kryo进行二进制序列化（性能最好）
      # 推荐值: jackson（兼容性好）
      log-serialization: jackson

      # undo log表名
      # 存储事务回滚日志的数据库表名
      # 必须在每个参与分布式事务的数据库中创建此表
      log-table: undo_log

      # 是否只关注更新的列
      # true: 只记录被更新的列，减少存储空间
      # false: 记录所有列，增加存储空间但提高安全性
      # 推荐值: true（优化存储）
      only-care-update-columns: true

      # =====================================================
      # Undo Log 压缩配置
      # =====================================================
      compress:
        # 是否启用undo log压缩
        # true: 启用压缩，减少存储空间和网络传输
        # false: 禁用压缩，减少CPU开销
        # 推荐值: true（节省存储空间）
        enable: true

        # 压缩算法类型
        # zip: 标准ZIP压缩（推荐）
        # gzip: GZIP压缩
        # 推荐值: zip（兼容性好）
        type: zip

        # 压缩阈值，超过此大小才进行压缩
        # 64k: 64KB，小于此大小的数据不压缩
        # 可根据实际情况调整：32k、128k等
        threshold: 64k

  # =====================================================
  # 服务发现和路由配置
  # File模式下的服务注册和发现配置
  # =====================================================
  service:
    # =====================================================
    # 虚拟组映射配置
    # 将事务组映射到具体的Seata Server集群
    # =====================================================
    vgroup-mapping:
      # 事务组到集群的映射关系
      # data-sync-group: 事务组名称（必须与tx-service-group一致）
      # default: 集群名称，对应grouplist中的配置
      data-sync-group: default

    # =====================================================
    # Seata Server地址列表
    # File模式下直接指定Server地址，无需注册中心
    # =====================================================
    grouplist:
      # 集群名称: Seata Server地址列表
      # 格式: IP:PORT，多个地址用逗号分隔
      # 示例: 127.0.0.1:8091,127.0.0.1:8092
      default: 127.0.0.1:8091

    # 是否启用服务降级
    # true: 当Seata Server不可用时，自动降级为本地事务
    # false: 当Seata Server不可用时，事务失败
    # 推荐值: false（保证分布式事务语义）
    enable-degrade: false

    # 是否禁用全局事务
    # true: 禁用全局事务，所有事务都是本地事务
    # false: 启用全局事务，支持分布式事务
    # 推荐值: false（启用分布式事务）
    disable-global-transaction: false

  # =====================================================
  # 网络传输配置
  # 客户端与Seata Server之间的通信配置
  # =====================================================
  transport:
    # 传输协议类型
    # TCP: 使用TCP协议（推荐，稳定可靠）
    # UDP: 使用UDP协议（性能好但可靠性差）
    type: TCP

    # 网络通信模式
    # NIO: 非阻塞IO模式（推荐，高并发性能好）
    # BIO: 阻塞IO模式（简单但性能差）
    server: NIO

    # 是否启用心跳检测
    # true: 启用心跳，及时发现连接断开（推荐）
    # false: 禁用心跳，可能导致连接异常无法及时发现
    heartbeat: true

    # 序列化方式
    # seata: Seata默认序列化方式（推荐）
    # protobuf: Protocol Buffers序列化
    # kryo: Kryo序列化
    serialization: seata

    # 压缩算法
    # none: 不压缩（推荐，减少CPU开销）
    # gzip: GZIP压缩（减少网络传输）
    compressor: none

    # 是否启用客户端批量发送请求
    # true: 启用批量发送，提高网络效率（推荐）
    # false: 禁用批量发送，实时性更好但效率低
    enable-client-batch-send-request: true
  # 降级和容错配置
  degradation:
    # 是否启用自动降级（当Seata Server不可用时）
    # true: 启用自动降级，Seata不可用时使用本地事务
    # false: 禁用自动降级，Seata不可用时直接失败
    auto-fallback-enabled: true

    # 连接检查间隔（秒）
    connection-check-interval: 5

    # 连接超时时间（秒）
    connection-timeout: 3

  # =====================================================
  # 事务恢复配置
  # 用于处理服务异常停止导致的事务悬挂问题
  # =====================================================
  recovery:
    # 是否启用事务恢复服务
    # true: 启用事务状态跟踪和悬挂事务检测
    # false: 禁用事务恢复功能
    enabled: true

    # 悬挂事务检查间隔（分钟）
    # 定期检查长时间未完成的事务
    # 推荐值: 5分钟
    check-interval: 5

    # 事务超时阈值（分钟）
    # 超过此时间的事务被认为是悬挂事务
    # 推荐值: 10分钟（应大于全局事务超时时间）
    timeout-threshold: 10

# =====================================================
# 日志配置
# 用于调试和监控Seata分布式事务
# =====================================================
logging:
  level:
    # Seata框架日志级别
    # DEBUG: 详细调试信息，包括事务执行过程
    # INFO: 基本信息，包括事务开始、提交、回滚
    # WARN: 警告信息，包括重试、超时等
    # ERROR: 错误信息，包括事务失败原因
    # 推荐值: INFO（生产环境），DEBUG（开发环境）
    io.seata: INFO

    # Seata配置类日志级别，用于查看配置加载过程
    com.tjsj.sync.modules.sync.config: INFO

    # 业务代码日志级别，用于查看同步过程
    com.tjsj.sync: DEBUG