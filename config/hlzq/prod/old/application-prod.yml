# 华龙证券生产数据库同步数据配置
# 更新日期：2025-06-27
server:
  port: 8870

spring:
  datasource:
    dynamic:
      primary: target
      datasource:
        target: #目标数据库
          url: **************************************************************************************************************************************************************************************************************************************
          username: xyyw@xyyw#bhtsobb02
          password: XYyw_2024!
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
        source: #源数据库，塔金阿里云数据库
          url: *********************************************************************************************************************************************************************************************************************************************
          username: syncdata_user
          password: ENC(HYxcLPlqP4nqTYrzc2yqFGGFBWoNp0SnhhG6aQCSJso=)
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
    druid:
      initial-size: 50
      max-active: 6000
      min-idle: 10
      max-wait: 600000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
      filter:
        wall:
          enabled: false
        stat:
          log-slow-sql: true
          slow-sql-millis: 10
          merge-sql: false
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 去除druid配置

mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.tjsj.**.modules.**.model
  global-config:
    banner: false
    db-config:
      id-type: auto
  configuration:
    cache-enabled: false
    call-setters-on-nulls: true
    #log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    auto-mapping-behavior: full
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    map-underscore-to-camel-case: false
    default-executor-type: batch

# 项目自定义配置，生产环境没有特殊情况不需要修改
tarkin:
  # 项目运行环境
  env: hlzq
  # 券商本地数据库类型
  dbType: hlzq
  # 项目环境配置
  profile: prod

# 定时任务配置，生产环境没有特殊情况不需要修改
scheduler:
  cron:
    # 同步云数据库数据到本地数据库
    sync-cloud-to-local: "0 30 0 * * ?"
    # 心跳检测
    heartbeat-test: "0/5 * * * * ?"
    # 管理quartz任务
    manage-quartz-job: "*/5 * * * * ?"
    # 同步修复数据
    syncFixData: "0 0 0/2 * * ?"

# 同步方法配置，生产环境没有特殊情况不需要修改
sync:
  auto:
    # 每批拉取数据量
    pull-batch-size: 2000
    # 每批插入数据量
    insert-batch-size: 2000
    # 重试间隔时间
    retry-delay-millis: 2000
    # 最大重试次数
    max-retry-times: 10