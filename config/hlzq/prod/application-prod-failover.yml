#
# ████████████████████████████████████████████████████████████████████████████████
# █                                                                              █
# █              华龙证券生产环境 - Redis故障转移配置文件 (临时方案)                █
# █                                                                              █
# ████████████████████████████████████████████████████████████████████████████████
# 📋 文件作用：
#    - 提供Redis主从故障转移的临时配置方案
#    - 在无法配置Sentinel的情况下使用
#    - 需要手动切换配置文件
# 🔧 使用方法：
#    1. 当主Redis (*********) 故障时
#    2. 备份当前 application-prod.yml
#    3. 将此文件重命名为 application-prod.yml
#    4. 重启应用服务
# 📅 更新：2025-07-01
# ████████████████████████████████████████████████████████████████████████████████
#

# ████████████████████████████████████████████████████████████████████████████████
# █                              🌐 服务配置                                    █
# ████████████████████████████████████████████████████████████████████████████████

server:
  port: 8870  # 🟡 可根据端口规划调整服务端口号

# ████████████████████████████████████████████████████████████████████████████████
# █                              💾 数据源配置                                  █
# ████████████████████████████████████████████████████████████████████████████████
spring:
  datasource:
    dynamic:
      primary: target  # 🟢 主数据源指向目标数据库
      datasource:
        # ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
        # ┃                            🔴 目标数据库配置                            ┃
        # ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
        target: # 生产环境OceanBase数据库
          url: ***********************************************************************************************************************************************************************************************************************************************************************************************
          username: xyzh_ywpt
          password: ENC(LsgkhlUA/7986hViU9Dc9HjVzJQF51eBqWf5bm1TWbDA=)
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true

        # ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
        # ┃                            源数据库配置                              ┃
        # ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
        source: # 塔金阿里云数据库
          url: *****************************************************************************************************************************************************************************************************************************************************************
          username: syncdata_user
          password: ENC(HYxcLPlqP4nqTYrzc2yqFGGFBWoNp0SnhhG6aQCSJso=)
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
    druid:
      initial-size: 50
      max-active: 6000
      min-idle: 10
      max-wait: 600000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
      filter:
        wall:
          enabled: false
        stat:
          log-slow-sql: true
          slow-sql-millis: 10
          merge-sql: false
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure

# ████████████████████████████████████████████████████████████████████████████████
# █                              🗄️ 缓存服务配置 - 故障转移模式                 █
# ████████████████████████████████████████████████████████████████████████████████
# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                    🚨 Redis故障转移配置 - 连接从服务器                      ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
# ⚠️ 此配置用于主Redis服务器 (*********) 故障时的紧急切换
# 📝 使用说明：
#    1. 主服务器故障时，将从服务器提升为主服务器：
#       redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc
#       SLAVEOF NO ONE
#    2. 使用此配置文件替换原配置文件
#    3. 重启应用服务
  redis:
    database: 0  # 🟢 Redis数据库索引
    host: *********    # 🚨 故障转移：连接到原从服务器 (现在作为主服务器)
    port: 6379            # 🔴 Redis端口号
    password: 1QAZ2wsx3edc    # 🔴 Redis密码
    timeout: 5000ms  # 🟡 增加超时时间，应对网络延迟
    jedis:
      pool:
        max-active: 16  # 🟡 最大连接数
        max-idle: 25   # 🟡 最大空闲连接数
        min-idle: 10   # 🟡 最小空闲连接数
        max-wait: 5000ms  # 🟡 增加等待时间
    # ┌─────────────────────────────────────────────────────────────────────────────┐
    # │                          📋 故障转移操作记录                                │
    # └─────────────────────────────────────────────────────────────────────────────┘
    # 💡 故障转移步骤记录：
    #    1. 检测到主服务器 ********* 故障
    #    2. 将从服务器 ********* 提升为主服务器
    #    3. 应用配置切换到 *********
    #    4. 故障恢复后需要重新建立主从关系

# ████████████████████████████████████████████████████████████████████████████████
# █                              🗃️ MyBatis-Plus配置                           █
# ████████████████████████████████████████████████████████████████████████████████
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml
  typeAliasesPackage: com.tjsj.**.modules.**.model
  global-config:
    banner: false
    db-config:
      id-type: auto
  configuration:
    cache-enabled: false
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    auto-mapping-behavior: full
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
    map-underscore-to-camel-case: false
    default-executor-type: batch

# ████████████████████████████████████████████████████████████████████████████████
# █                              🏢 项目自定义配置                              █
# ████████████████████████████████████████████████████████████████████████████████
tarkin:
  project-id: hlzq_xyyw
  env: hlzq
  dbType: hlzq
  profile: prod
  quartz:
    enabled: true

# ████████████████████████████████████████████████████████████████████████████████
# █                              ⏰ 定时任务配置                                █
# ████████████████████████████████████████████████████████████████████████████████
scheduler:
  cron:
    sync-cloud-to-local: "0 30 0 * * ?"
    heartbeat-test: "0/5 * * * * ?"
    manage-quartz-job: "*/5 * * * * ?"
    sync-fix-data: "0 0 0/2 * * ?"
    sync-official-website-data: "0 */2 * * * ?"

# ████████████████████████████████████████████████████████████████████████████████
# █                              🔄 数据同步配置                                █
# ████████████████████████████████████████████████████████████████████████████████
sync:
  auto:
    pull-batch-size: 2000
    insert-batch-size: 2000
    retry-delay-millis: 2000
    max-retry-times: 10

# jasypt加密配置
jasypt:
  encryptor:
    password: yky2000
    algorithm: PBEWithMD5AndDES

com:
  bes:
    enterprise:
      licenseData:

#
# ████████████████████████████████████████████████████████████████████████████████
# █                              🚨 故障转移使用说明                            █
# ████████████████████████████████████████████████████████████████████████████████
#
# 🔄 故障转移操作流程：
#
# 1. 【检测故障】
#    - 监控发现主Redis (*********) 无法连接
#    - 应用出现Redis连接异常
#
# 2. 【提升从服务器】
#    redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc
#    SLAVEOF NO ONE
#
# 3. 【切换应用配置】
#    cp application-prod.yml application-prod.yml.backup
#    cp application-prod-failover.yml application-prod.yml
#
# 4. 【重启应用】
#    ./stop.sh
#    ./start.sh
#
# 5. 【验证服务】
#    curl http://localhost:8870/actuator/health
#
# 🔙 故障恢复流程：
#
# 1. 【恢复主服务器】
#    - 修复主Redis服务器 *********
#    - 启动Redis服务
#
# 2. 【重建主从关系】
#    # 方案A：*********作为从服务器
#    redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc
#    SLAVEOF ********* 6379
#
#    # 方案B：恢复原主从关系
#    redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc
#    SLAVEOF ********* 6379
#    # 然后切换应用配置回原配置文件
#
# ⚠️ 注意事项：
# - 故障转移期间可能有短暂的服务中断
# - 确保数据同步完成后再进行切换
# - 建议在业务低峰期进行故障转移操作
# - 及时更新监控和告警配置
#
