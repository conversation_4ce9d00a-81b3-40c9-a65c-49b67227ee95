# Redis主从模式故障转移指南

## 📋 当前配置概述

### 主从架构
- **主服务器 (Master)**: `*********:6379` - 处理所有读写操作
- **从服务器 (Slave)**: `*********:6379` - 数据备份和故障转移备用

### 应用配置
- 当前应用连接到主服务器 `*********`
- 从服务器自动从主服务器同步数据
- 密码：`1QAZ2wsx3edc` (主从服务器相同)

## 🚨 故障转移操作步骤

### 场景1：主服务器故障，需要切换到从服务器

#### 步骤1：检查从服务器状态
```bash
# 连接到从服务器检查状态
redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc
INFO replication
```

#### 步骤2：将从服务器提升为主服务器
```bash
# 在从服务器上执行
redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc
SLAVEOF NO ONE
```

#### 步骤3：修改应用配置
修改 `application-prod.yml` 中的Redis配置：
```yaml
redis:
  host: *********    # 切换到原从服务器
  port: 6379
  password: 1QAZ2wsx3edc
```

#### 步骤4：重启应用服务
```bash
# 停止当前服务
./stop.sh

# 启动服务
./start.sh
```

### 场景2：主服务器恢复，重新建立主从关系

#### 步骤1：将原主服务器设为从服务器
```bash
# 连接到原主服务器 (*********)
redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc
SLAVEOF ********* 6379
```

#### 步骤2：（可选）重新切换主从角色
如果希望恢复原来的主从关系：
```bash
# 1. 在新主服务器(*********)上执行
SLAVEOF ********* 6379

# 2. 在原主服务器(*********)上执行
SLAVEOF NO ONE

# 3. 修改应用配置回到原主服务器
# 4. 重启应用
```

## 🔧 高可用方案建议

### 方案1：Redis Sentinel (推荐)
配置Redis哨兵实现自动故障转移：

```yaml
spring:
  redis:
    sentinel:
      master: mymaster
      nodes:
        - *********:26379
        - *********:26379
        - *********:26379  # 第三个哨兵节点
    password: 1QAZ2wsx3edc
```

### 方案2：Redis Cluster
配置Redis集群模式：

```yaml
spring:
  redis:
    cluster:
      nodes:
        - *********:6379
        - *********:6379
        - *********:6379
        - 10.3.2.13:6379
        - 10.3.2.14:6379
        - 10.3.2.15:6379
    password: 1QAZ2wsx3edc
```

## 📊 监控和检查命令

### 检查主从状态
```bash
# 检查主服务器状态
redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc INFO replication

# 检查从服务器状态
redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc INFO replication
```

### 检查数据同步
```bash
# 在主服务器写入测试数据
redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc SET test_key "test_value"

# 在从服务器读取测试数据
redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc GET test_key
```

### 应用连接测试
```bash
# 测试应用是否能正常连接Redis
telnet ********* 6379
telnet ********* 6379
```

## ⚠️ 注意事项

1. **数据一致性**：故障转移时可能存在短暂的数据不一致
2. **应用重启**：切换Redis服务器后必须重启应用
3. **监控告警**：建议配置Redis监控和告警机制
4. **备份策略**：定期备份Redis数据到其他存储
5. **网络延迟**：主从同步受网络延迟影响

## 📞 紧急联系信息

- **运维负责人**：[待填写]
- **开发负责人**：[待填写]
- **故障处理流程**：[待填写]

---
**文档更新时间**：2025-07-01
**维护人员**：数据同步服务团队
