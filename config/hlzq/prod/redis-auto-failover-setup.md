# Redis自动故障转移配置指南

## 🎯 目标
解决Redis主服务器故障时应用无法自动切换到从服务器的问题。

## 📋 当前环境
- **主服务器**: *********:6379
- **从服务器**: *********:6379
- **密码**: 1QAZ2wsx3edc

## 🔧 方案一：Redis Sentinel 自动故障转移（推荐）

### 1. 安装和配置Redis Sentinel

#### 在主服务器 (*********) 上配置Sentinel
创建 `/etc/redis/sentinel.conf`:
```bash
# Sentinel端口
port 26379

# 监控主节点配置
# sentinel monitor <master-name> <ip> <port> <quorum>
sentinel monitor mymaster ********* 6379 2

# 主节点密码
sentinel auth-pass mymaster 1QAZ2wsx3edc

# 故障转移超时时间(毫秒)
sentinel down-after-milliseconds mymaster 5000

# 故障转移时允许的并行同步从节点数量
sentinel parallel-syncs mymaster 1

# 故障转移超时时间
sentinel failover-timeout mymaster 10000

# 日志文件
logfile /var/log/redis/sentinel.log

# 后台运行
daemonize yes
```

#### 在从服务器 (*********) 上配置Sentinel
创建相同的 `/etc/redis/sentinel.conf` 配置文件。

#### 启动Sentinel服务
```bash
# 在两台服务器上分别启动
redis-sentinel /etc/redis/sentinel.conf
```

### 2. 修改应用配置
应用配置已经在 `application-prod.yml` 中提供了Sentinel配置模板。

### 3. 验证Sentinel配置
```bash
# 连接到Sentinel检查状态
redis-cli -h ********* -p 26379
SENTINEL masters
SENTINEL slaves mymaster
```

## 🔧 方案二：应用层故障转移

如果无法配置Sentinel，可以在应用层实现故障转移：

### 1. 创建Redis配置类
```java
@Configuration
public class RedisFailoverConfig {
    
    @Value("${redis.master.host:*********}")
    private String masterHost;
    
    @Value("${redis.slave.host:*********}")
    private String slaveHost;
    
    @Value("${redis.port:6379}")
    private int port;
    
    @Value("${redis.password}")
    private String password;
    
    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
        // 创建主从连接工厂，支持读写分离和故障转移
        List<RedisNode> nodes = Arrays.asList(
            new RedisNode(masterHost, port),
            new RedisNode(slaveHost, port)
        );
        
        RedisStaticMasterReplicaConfiguration config = 
            new RedisStaticMasterReplicaConfiguration(masterHost, port);
        config.addNode(slaveHost, port);
        config.setPassword(password);
        
        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
            .readFrom(ReadFrom.MASTER_PREFERRED) // 优先从主节点读取，主节点故障时从从节点读取
            .build();
            
        return new LettuceConnectionFactory(config, clientConfig);
    }
}
```

### 2. 添加健康检查
```java
@Component
public class RedisHealthChecker {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Scheduled(fixedDelay = 5000) // 每5秒检查一次
    public void checkRedisHealth() {
        try {
            redisTemplate.opsForValue().set("health_check", System.currentTimeMillis());
            log.info("Redis连接正常");
        } catch (Exception e) {
            log.error("Redis连接异常: {}", e.getMessage());
            // 可以在这里实现告警逻辑
        }
    }
}
```

## 🔧 方案三：使用Spring Boot Redis Cluster

如果可以重新部署Redis，建议使用Cluster模式：

### 1. Redis Cluster配置
```yaml
spring:
  redis:
    cluster:
      nodes:
        - *********:6379
        - *********:6379
        - 10.3.2.12:6379  # 至少需要3个主节点
        - *********:6379
        - *********:6379
        - *********:6379
      max-redirects: 3
    password: 1QAZ2wsx3edc
    timeout: 3000ms
```

## 📊 故障转移测试

### 测试Sentinel故障转移
```bash
# 1. 停止主Redis服务
sudo systemctl stop redis

# 2. 检查Sentinel日志
tail -f /var/log/redis/sentinel.log

# 3. 检查新的主节点
redis-cli -h ********* -p 26379
SENTINEL get-master-addr-by-name mymaster

# 4. 测试应用连接
curl http://localhost:8870/actuator/health
```

### 测试应用层故障转移
```bash
# 1. 停止主Redis服务
sudo systemctl stop redis

# 2. 检查应用日志
tail -f logs/application.log

# 3. 测试Redis操作
# 应用应该自动切换到从节点
```

## ⚠️ 重要注意事项

### Sentinel模式注意事项
1. **至少需要3个Sentinel节点**（奇数个，避免脑裂）
2. **quorum值设置**：通常设为 (sentinel节点数 / 2) + 1
3. **网络分区**：确保Sentinel节点分布在不同网络区域
4. **配置同步**：所有Sentinel节点配置必须一致

### 应用层故障转移注意事项
1. **只读从节点**：从节点通常是只读的，写操作会失败
2. **数据一致性**：可能存在主从同步延迟
3. **连接池配置**：需要合理配置连接超时和重试机制

### 通用注意事项
1. **监控告警**：配置Redis和应用的监控告警
2. **数据备份**：定期备份Redis数据
3. **故障演练**：定期进行故障转移演练
4. **文档更新**：及时更新运维文档

## 🚀 推荐实施步骤

### 阶段一：快速解决（应用层）
1. 修改应用配置，增加连接超时和重试机制
2. 实现简单的健康检查和告警
3. 准备手动故障转移脚本

### 阶段二：完整解决（Sentinel）
1. 在测试环境部署Redis Sentinel
2. 验证自动故障转移功能
3. 在生产环境部署Sentinel
4. 更新应用配置使用Sentinel

### 阶段三：长期优化（Cluster）
1. 评估Redis Cluster的可行性
2. 规划Cluster部署方案
3. 逐步迁移到Cluster模式

---
**更新时间**: 2025-07-01
**维护团队**: 数据同步服务团队
