#
# ████████████████████████████████████████████████████████████████████████████████
# █                                                                              █
# █                    华龙证券生产环境 - 数据同步服务配置文件                      █
# █                                                                              █
# ████████████████████████████████████████████████████████████████████████████████
# 📋 文件作用：
#    - 兴业证券生产环境数据库同步服务配置
#    - 支持云端数据库到本地数据库的数据同步
#    - 包含定时任务、缓存、数据库连接等核心配置
# 🔧 维护说明：
#    🔴【必改】- 需要根据实际环境修改的配置
#    🟡【可选】- 可根据需要调整的配置
#    🟢【默认】- 通常不需要修改的配置
# 📅 更新：2025-07-01
# ████████████████████████████████████████████████████████████████████████████████
#

# ⚠️ ⚠️ ⚠️ 必改配置项快速定位索引 ⚠️ ⚠️ ⚠️
#
# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                            🔴 必改配置项索引                                  │
# ├─────────────────────────────────────────────────────────────────────────────┤
# │ 🔴 服务端口        - 服务配置 > server.port                                  │
# │ 🔴 目标数据库地址  - 数据源配置 > spring.datasource.dynamic.datasource.target.url │
# │ 🔴 目标数据库用户  - 数据源配置 > spring.datasource.dynamic.datasource.target.username │
# │ 🔴 目标数据库密码  - 数据源配置 > spring.datasource.dynamic.datasource.target.password │
# │ 🔴 Redis主服务器地址 - 缓存配置 > redis.host (主从模式-连接主服务器)           │
# │ 🔴 Redis端口       - 缓存配置 > redis.port                                   │
# │ 🔴 Redis密码       - 缓存配置 > redis.password                               │
# └─────────────────────────────────────────────────────────────────────────────┘
#

# ████████████████████████████████████████████████████████████████████████████████
# █                              🌐 服务配置                                    █
# ████████████████████████████████████████████████████████████████████████████████

server:
  port: 8870  # 🟡 可根据端口规划调整服务端口号
# ████████████████████████████████████████████████████████████████████████████████
# █                              💾 数据源配置                                  █
# ████████████████████████████████████████████████████████████████████████████████
spring:
  datasource:
    dynamic:
      primary: target  # 🟢 主数据源指向目标数据库
      datasource:
        # ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
        # ┃                            🔴 目标数据库配置                            ┃
        # ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
        target: # 生产环境OceanBase数据库
          # 🔴 请修改为实际的目标数据库连接地址
          url: ***********************************************************************************************************************************************************************************************************************************************************************************************
          username: xyzh_ywpt   # 🔴 请修改为实际的数据库用户名
          password: ENC(LsgkhlUA/7986hViU9Dc9HjVzJQF51eBqWf5bm1TWbDA=)          # 🔴 请修改为实际的数据库密码
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true

        # ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
        # ┃                            源数据库配置                              ┃
        # ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
        source: # 塔金阿里云数据库
          url: *****************************************************************************************************************************************************************************************************************************************************************
          username: syncdata_user
          password: ENC(HYxcLPlqP4nqTYrzc2yqFGGFBWoNp0SnhhG6aQCSJso=)
          druid:
            validation-query: SELECT 1 FROM DUAL
            connection-properties: allowMultiQueries=true
    # ┌─────────────────────────────────────────────────────────────────────────────┐
    # │                            🟡 数据库连接池配置                              │
    # └─────────────────────────────────────────────────────────────────────────────┘
    druid:
      initial-size: 50  # 🟡 初始连接数，可根据并发量调整
      max-active: 6000  # 🟡 最大连接数，可根据数据库性能调整
      min-idle: 10  # 🟡 最小空闲连接数
      max-wait: 600000  # 🟡 获取连接最大等待时间(毫秒)
      pool-prepared-statements: true  # 🟢 开启预编译语句缓存
      max-pool-prepared-statement-per-connection-size: 20  # 🟡 每个连接的预编译语句缓存大小
      time-between-eviction-runs-millis: 60000  # 🟡 连接回收检测间隔(毫秒)
      min-evictable-idle-time-millis: 300000  # 🟡 连接最小空闲时间(毫秒)
      validation-query: SELECT 1 FROM DUAL  # 🟢 连接有效性检测SQL
      test-while-idle: true  # 🟢 空闲时检测连接有效性
      test-on-borrow: false  # 🟢 获取连接时不检测
      test-on-return: false  # 🟢 归还连接时不检测
      stat-view-servlet:
        enabled: true  # 🟡 启用Druid监控页面
        url-pattern: /druid/*  # 🟢 监控页面访问路径
      filter:
        wall:
          enabled: false  # 🟢 关闭SQL防火墙
        stat:
          log-slow-sql: true  # 🟡 记录慢SQL日志
          slow-sql-millis: 10  # 🟡 慢SQL阈值(毫秒)
          merge-sql: false  # 🟢 不合并相同SQL统计
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure  # 🟢 排除Druid自动配置
# ████████████████████████████████████████████████████████████████████████████████
# █                              🗄️ 缓存服务配置                                    █
# ████████████████████████████████████████████████████████████████████████████████
# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                            🔴 Redis缓存配置 - 主从模式                           ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛
# 📝 主从模式说明：
#    - 主服务器：********* (读写操作)e
#    - 从服务器：********* (备份和故障转移)
#    - 应用连接主服务器，从服务器自动同步数据
  redis:
    database: 0  # 🟢 Redis数据库索引
    host: *********    # 🔴 Redis主服务器地址 (Master)
    port: 6379            # 🔴 Redis主服务器端口号
    password: 1QAZ2wsx3edc    # 🔴 Redis密码 (主从服务器密码相同)
    timeout: 2000ms  # 🟡 连接超时时间，可根据网络情况调整
    # ┌─────────────────────────────────────────────────────────────────────────────┐
    # │                          🟡 连接池配置 - 主从模式优化                        │
    # └─────────────────────────────────────────────────────────────────────────────┘
    jedis:
      pool:
        max-active: 16  # 🟡 最大连接数，主从模式建议适当增加
        max-idle: 25  # 🟡 最大空闲连接数
        min-idle: 10  # 🟡 最小空闲连接数
        max-wait: 3000ms  # 🟡 获取连接最大等待时间
    # ┌─────────────────────────────────────────────────────────────────────────────┐
    # │                          📋 主从模式备注信息                                │
    # └─────────────────────────────────────────────────────────────────────────────┘
    # 💡 主从模式配置说明：
    #    1. 当前连接主服务器 *********，所有读写操作在主服务器执行
    #    2. 从服务器 ********* 自动从主服务器同步数据，无需应用配置
    #    3. 如果主服务器故障，需要手动切换配置到从服务器
    #    4. 建议配置Redis Sentinel或Cluster实现自动故障转移

# ████████████████████████████████████████████████████████████████████████████████
# █                              🗃️ MyBatis-Plus配置                           █
# ████████████████████████████████████████████████████████████████████████████████
# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                                🟢 默认配置区                                │
# └─────────────────────────────────────────────────────────────────────────────┘
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml  # 🟢 Mapper文件扫描路径
  typeAliasesPackage: com.tjsj.**.modules.**.model  # 🟢 实体类扫描包路径
  global-config:
    banner: false  # 🟢 关闭MyBatis-Plus启动横幅
    db-config:
      id-type: auto  # 🟢 主键自增策略
  configuration:
    cache-enabled: false  # 🟢 关闭二级缓存
    call-setters-on-nulls: true  # 🟢 NULL值也调用setter方法
    # 🟡 可根据需要切换日志实现：NoLoggingImpl(关闭) / StdOutImpl(控制台输出)
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    auto-mapping-behavior: full  # 🟢 自动映射行为
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler  # 🟢 枚举处理器
    map-underscore-to-camel-case: false  # 🟢 下划线转驼峰命名
    default-executor-type: batch  # 🟢 默认执行器类型

# ████████████████████████████████████████████████████████████████████████████████
# █                              🏢 项目自定义配置                              █
# ████████████████████████████████████████████████████████████████████████████████
# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                                🟢 默认配置区                                │
# └─────────────────────────────────────────────────────────────────────────────┘
tarkin:
  project-id: hlzq_xyyw  # 🟢 项目ID
  env: hlzq  # 🟢 项目运行环境标识
  dbType: hlzq  # 🟢 券商本地数据库类型
  profile: prod  # 🟢 项目环境配置
  quartz:
    enabled: true  # 🟡 是否启用Quartz定时任务，可根据需要关闭
  # ████████████████████████████████████████████████████████████████████████████████
  # █                              ⏰ 定时任务配置                                █
  # ████████████████████████████████████████████████████████████████████████████████
  # ┌─────────────────────────────────────────────────────────────────────────────┐
  # │                                🟡 可选配置区                                │
  # └─────────────────────────────────────────────────────────────────────────────┘
  scheduler:
    enabled: true  # 🟡 是否启用定时任务，可根据需要关闭
    cron:
      # 🟡 可根据业务需要调整同步频率
      sync-cloud-to-local: "0 30 0 * * ?"  # 每天凌晨0:30执行云数据同步
      # 🟡 可根据监控需要调整心跳检测频率
      heartbeat-test: "0/5 * * * * ?"  # 每5秒执行一次心跳检测
      # 🟡 可根据任务管理需要调整频率
      manage-quartz-job: "*/5 * * * * ?"  # 每5秒管理一次Quartz任务
      # 🟡 可根据数据修复需要调整频率
      sync-fix-data: "0 0 0/2 * * ?"  # 每2小时执行一次数据修复
      # 🟡 可根据官网数据更新频率调整
      sync-official-website-data: "0 */2 * * * ?"  # 每2分钟同步一次官网数据

# ████████████████████████████████████████████████████████████████████████████████
# █                              🔄 数据同步配置                                █
# ████████████████████████████████████████████████████████████████████████████████
# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                                🟡 可选配置区                                │
# └─────────────────────────────────────────────────────────────────────────────┘
sync:
  auto:
    pull-batch-size: 2000  # 🟡 每批拉取数据量，可根据内存和性能调整
    insert-batch-size: 2000  # 🟡 每批插入数据量，可根据数据库性能调整
    retry-delay-millis: 2000  # 🟡 重试间隔时间(毫秒)，可根据网络情况调整
    max-retry-times: 10  # 🟡 最大重试次数，可根据稳定性要求调整

# jasypt加密配置，用来加密和解密配置文件中的敏感信息
jasypt:
  encryptor:
    password: yky2000  # 这是用来加密和解密的密钥
    algorithm: PBEWithMD5AndDES  # 加密算法

com:
  bes:
    enterprise:
      licenseData:

#
# ████████████████████████████████████████████████████████████████████████████████
# █                              🔧 维护提醒区域                                █
# ████████████████████████████████████████████████████████████████████████████████
#
# ⚠️ 配置修改注意事项：
#
# 1. 🔴【必改配置】修改前请确认：
#    - 数据库连接信息：确保地址、端口、用户名、密码正确
#    - Redis连接信息：确保服务可用且密码正确
#    - 服务端口：确保端口未被占用且符合规划
#
# 🔄 环境说明：
# - 当前配置适用于：兴业证券生产环境
# - 数据同步方向：塔金阿里云 → 兴业证券OceanBase
# - 支持功能：定时同步、心跳检测、数据修复、官网数据同步
#
# ⚠️ 修改后请务必：
# 1. 备份原配置文件
# 2. 在测试环境验证配置正确性
# 3. 重启服务并检查日志
# 4. 更新本文件头部的维护信息
#
#

