json.rule.enum.convert[groovy:it.isExtend("com.tjsj.common.enums.DescriptionAble")]=~#description

# 获取folderName文件夹名称
# folder.name=#description
folder.name=@io.swagger.v3.oas.annotations.tags.Tag#description
folder.name=#description

# 忽略类或接口
ignore=#ignore

# 字段设置默认null值
field.schema.permit.null=true
#field.schema.permit.null[groovy=!it.hasAnn('javax.validation.constraints.NotNull')]=true



# 字段设置必需
field.required=true
field.schema.format=@io.swagger.v3.oas.annotations.media.Schema#type

# 转换类型
json.rule.convert[com.tjsj.common.enums.base.CommonStatus]=java.lang.Integer
#json.rule.enum.convert[com.tjsj.common.enums.base.CommonStatus]=~#code
json.rule.enum.convert[groovy:it.isExtend("com.tjsj.common.enums.BaseEnum")]=~#code

#接口描述
method.description=@io.swagger.v3.oas.annotations.Operation#description
method.description[#deprecated]=groovy:"\n「已废弃」" + it.doc("deprecated")
method.description[@java.lang.Deprecated]=「已废弃」

#method.description[groovy:it.containingClass().hasDoc("deprecated")]=groovy:"\n「已废弃」" + it.containingClass().doc
# ("deprecated")
method.description[groovy:it.containingClass().hasAnn("java.lang.Deprecated")]=「已废弃」