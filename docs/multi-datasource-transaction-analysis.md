# 多数据源事务问题深度分析与解决方案 🎯

## 🔍 问题分析

### 问题现象
在 `BatchSyncStrategy.executeSyncData` 方法中，当使用 `@Transactional(rollbackFor = Exception.class)` 注解时，按以下顺序执行数据库操作会出现数据源切换问题：

1. **先操作 target 数据库**（通过 `localDataMapper`，使用 `@DS("target")`）
2. **再操作 source 数据库**（通过 `cloudDataMapper`，使用 `@DS("source")`）

### 根本原因分析

#### 1. **Spring事务管理器的单数据源限制** 🔒

```java
// 当前问题代码结构
@Transactional(rollbackFor = Exception.class)  // ❌ 问题所在
public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {
    // 1. 数据提取 - 使用 cloudDataMapper (@DS("source"))
    dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchSize);
    
    // 2. 数据插入 - 使用 localDataMapper (@DS("target"))  
    dataInserter.insertData(context, dataList, batchConfig);
}
```

**核心问题**：
- Spring的 `@Transactional` 注解在方法开始时会绑定一个数据源到当前事务
- 一旦事务开始，Spring事务管理器会"锁定"第一个使用的数据源
- 后续的 `@DS` 注解无法生效，因为事务已经绑定到特定的数据源

#### 2. **Dynamic-Datasource与事务的冲突** ⚔️

```java
// CloudDataMapper
@DS(DataSourceNames.SOURCE_DB)  // "source"
public interface CloudDataMapper {
    List<Map<String, Object>> selectTableDataByDataRange(...);
}

// LocalDataMapper  
@DS(DataSourceNames.TARGET_DB)  // "target"
public interface LocalDataMapper {
    void batchInsertTableData(...);
}
```

**冲突机制**：
1. **事务开始**：Spring创建事务，绑定到第一个访问的数据源
2. **数据源切换失败**：后续的 `@DS` 注解被事务管理器忽略
3. **连接复用**：所有操作都使用同一个数据库连接

#### 3. **事务传播机制的影响** 🌊

```java
// 当前配置中的事务传播行为
PROPAGATION_REQUIRED  // 如果存在事务则加入，否则创建新事务
```

这意味着一旦在 `executeSyncData` 方法中创建了事务，所有子方法调用都会加入这个事务，无法切换数据源。

## 🎯 解决方案

### 方案一：移除方法级事务注解（推荐） ✅

**核心思路**：让每个Mapper方法独立管理自己的数据源和事务。

```java
@Component
@Slf4j
@RequiredArgsConstructor
public class BatchSyncStrategy {

    /**
     * 执行同步数据 - 移除事务注解
     * 让每个数据源操作独立管理事务
     */
    // @Transactional(rollbackFor = Exception.class)  // ❌ 移除这个注解
    public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {
        log.debug("📦 执行批量同步策略 - 表: {}", context.getFullTableName());

        SyncBatchConfig batchConfig = prepareResult.getBatchConfig();
        LocalDateTime targetMaxTime = prepareResult.getTargetMaxTime();

        int totalDataNum = 0;
        int batchCount = 0;
        int offset = 0;

        List<Map<String, Object>> dataList;
        do {
            // 1. 数据提取 - 自动使用 source 数据源
            dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchConfig.getReadSize());

            // 2. 数据处理流水线
            dataList = dataPipeline.process(context, dataList);

            // 3. 哈希检查
            if (!hashChecker.checkDataChange(context, dataList, totalDataNum)) {
                if (dataList.size() != batchConfig.getReadSize()) {
                    break;
                }
            }

            // 4. 数据插入 - 自动使用 target 数据源
            if (!dataList.isEmpty()) {
                int insertedCount = dataInserter.insertData(context, dataList, batchConfig);
                totalDataNum += insertedCount;
                batchCount++;
            }

            offset += batchConfig.getReadSize();

        } while (dataList.size() == batchConfig.getReadSize());

        return SyncResult.builder()
                .totalDataNum(totalDataNum)
                .batchCount(batchCount)
                .success(true)
                .build();
    }
}
```

### 方案二：细粒度事务控制 🔧

如果需要事务控制，可以在具体的数据操作方法上添加事务注解：

```java
@Component
@Slf4j
@RequiredArgsConstructor
public class EnhancedDataInserter {

    private final LocalDataMapper localDataMapper;
    private final CloudDataMapper cloudDataMapper;

    /**
     * 插入数据 - 在具体操作方法上添加事务
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertDataWithTransaction(SyncContext context, List<Map<String, Object>> dataList, 
                                       SyncBatchConfig batchConfig) {
        
        // 这里的事务只管理单一数据源的操作
        return insertDataInternal(context, dataList, batchConfig);
    }

    /**
     * 内部插入逻辑
     */
    private int insertDataInternal(SyncContext context, List<Map<String, Object>> dataList, 
                                 SyncBatchConfig batchConfig) {
        SyncTableConfigDO config = context.getSyncTableConfig();
        SyncTaskTypeEnum taskType = config.getTaskType();

        if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            // 使用 @DS("target") 注解的 localDataMapper
            localDataMapper.batchInsertTableData(config, dataList);
        } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            // 使用 @DS("source") 注解的 cloudDataMapper
            cloudDataMapper.batchInsertTableData(config, dataList);
        }

        return dataList.size();
    }
}
```

### 方案三：编程式事务管理 💻

对于复杂的多数据源事务场景，可以使用编程式事务：

```java
@Component
@Slf4j
@RequiredArgsConstructor
public class ProgrammaticTransactionSyncStrategy {

    private final PlatformTransactionManager transactionManager;
    private final DataExtractor dataExtractor;
    private final DataInserter dataInserter;

    /**
     * 使用编程式事务管理多数据源操作
     */
    public SyncResult executeSyncDataWithProgrammaticTransaction(SyncPrepareResult prepareResult, 
                                                               SyncContext context) {
        
        // 创建事务定义
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        def.setIsolationLevel(TransactionDefinition.ISOLATION_DEFAULT);
        def.setTimeout(30); // 30秒超时

        // 开始事务
        TransactionStatus status = transactionManager.getTransaction(def);

        try {
            // 执行业务逻辑
            SyncResult result = executeSyncLogic(prepareResult, context);
            
            // 提交事务
            transactionManager.commit(status);
            return result;
            
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);
            throw new RuntimeException("同步执行失败", e);
        }
    }

    private SyncResult executeSyncLogic(SyncPrepareResult prepareResult, SyncContext context) {
        // 具体的同步逻辑实现
        // 注意：这里仍然会受到单事务单数据源的限制
        return null;
    }
}
```

### 方案四：分布式事务解决方案 🌐

对于真正需要跨数据源事务一致性的场景，可以考虑使用分布式事务：

```java
@Component
@Slf4j
@RequiredArgsConstructor
public class DistributedTransactionSyncStrategy {

    /**
     * 使用Seata分布式事务
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    public SyncResult executeSyncDataWithDistributedTransaction(SyncPrepareResult prepareResult, 
                                                              SyncContext context) {
        
        // 1. 从source数据库读取数据
        List<Map<String, Object>> dataList = extractDataFromSource(context);
        
        // 2. 写入target数据库
        int insertedCount = insertDataToTarget(context, dataList);
        
        // 分布式事务会确保两个操作的一致性
        return SyncResult.builder()
                .totalDataNum(insertedCount)
                .success(true)
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    public List<Map<String, Object>> extractDataFromSource(SyncContext context) {
        // 这个方法会在source数据源上执行
        return dataExtractor.extractData(context, null, 0, 1000);
    }

    @Transactional(rollbackFor = Exception.class)
    public int insertDataToTarget(SyncContext context, List<Map<String, Object>> dataList) {
        // 这个方法会在target数据源上执行
        return dataInserter.insertData(context, dataList, null);
    }
}
```

## 🛠️ 推荐实施方案

### 第一步：立即修复（方案一）

```java
// 修改 BatchSyncStrategy.java
@Component
@Slf4j
@RequiredArgsConstructor
public class BatchSyncStrategy {

    // 移除类级别或方法级别的 @Transactional 注解
    // @Transactional(rollbackFor = Exception.class)  // ❌ 注释掉这行
    public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {
        // 现有逻辑保持不变
        // Dynamic-Datasource会自动处理数据源切换
    }
}
```

### 第二步：验证数据源切换

添加日志验证数据源切换是否正常：

```java
@Component
@Slf4j
@RequiredArgsConstructor
public class DataSourceSwitchValidator {

    @Autowired
    private DataSource dataSource;

    public void logCurrentDataSource(String operation) {
        try {
            if (dataSource instanceof DynamicRoutingDataSource) {
                DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) dataSource;
                String currentDataSource = DynamicDataSourceContextHolder.peek();
                log.debug("🔄 {} - 当前数据源: {}", operation, currentDataSource);
            }
        } catch (Exception e) {
            log.warn("⚠️ 无法获取当前数据源信息", e);
        }
    }
}

// 在关键位置添加日志
public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {
    
    // 数据提取前
    dataSourceValidator.logCurrentDataSource("数据提取前");
    dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchSize);
    
    // 数据插入前  
    dataSourceValidator.logCurrentDataSource("数据插入前");
    dataInserter.insertData(context, dataList, batchConfig);
}
```

### 第三步：配置优化

确保Dynamic-Datasource配置正确：

```yaml
spring:
  datasource:
    dynamic:
      primary: target  # 默认数据源
      strict: false    # 非严格模式，允许数据源不存在时使用默认数据源
      datasource:
        target:
          # 目标数据库配置
        source:
          # 源数据库配置
```

## 📊 性能影响分析

### 移除事务注解的影响

| 方面 | 影响 | 说明 |
|------|------|------|
| **数据一致性** | ⚠️ 需要注意 | 单个批次内的操作不再有事务保护 |
| **性能** | ✅ 提升 | 减少事务开销，提高并发性能 |
| **数据源切换** | ✅ 正常 | Dynamic-Datasource可以正常工作 |
| **错误恢复** | ⚠️ 需要处理 | 需要在应用层处理部分失败的情况 |

### 建议的错误处理策略

```java
public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {
    int totalDataNum = 0;
    int failedBatches = 0;
    List<String> errorMessages = new ArrayList<>();

    do {
        try {
            // 数据提取
            dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchSize);
            
            // 数据插入
            if (!dataList.isEmpty()) {
                int insertedCount = dataInserter.insertData(context, dataList, batchConfig);
                totalDataNum += insertedCount;
            }
            
        } catch (Exception e) {
            failedBatches++;
            errorMessages.add("批次 " + batchCount + " 失败: " + e.getMessage());
            log.error("❌ 批次同步失败 - 表: {}, 批次: {}", context.getFullTableName(), batchCount, e);
            
            // 根据业务需求决定是否继续或中断
            if (failedBatches > 3) {  // 连续失败3次则中断
                break;
            }
        }
        
        offset += batchConfig.getReadSize();
        batchCount++;
        
    } while (dataList.size() == batchConfig.getReadSize());

    return SyncResult.builder()
            .totalDataNum(totalDataNum)
            .batchCount(batchCount)
            .failedBatches(failedBatches)
            .errorMessages(errorMessages)
            .success(failedBatches == 0)
            .build();
}
```

## 🎯 最佳实践总结

### ✅ 推荐做法

1. **移除跨数据源方法的事务注解**
2. **在单数据源操作方法上使用事务注解**
3. **使用应用层错误处理和重试机制**
4. **添加详细的数据源切换日志**
5. **定期验证数据一致性**

### ❌ 避免的做法

1. **在跨数据源方法上使用 `@Transactional`**
2. **假设Spring事务能自动处理多数据源**
3. **忽略数据源切换的验证**
4. **过度依赖事务来保证数据一致性**

## 🔧 具体修改建议

### 立即修改的代码

```java
// 文件：BatchSyncStrategy.java
@Component
@Slf4j
@RequiredArgsConstructor
public class BatchSyncStrategy {

    private final DataExtractor dataExtractor;
    private final DataPipeline dataPipeline;
    private final HashChecker hashChecker;
    private final DataInserter dataInserter;

    /**
     * 执行同步数据
     *
     * ⚠️ 重要：移除了 @Transactional 注解以支持多数据源切换
     * 原因：Spring事务会锁定第一个访问的数据源，导致后续@DS注解失效
     */
    // @Transactional(rollbackFor = Exception.class)  // ❌ 已移除
    public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {
        log.debug("📦 执行批量同步策略 - 表: {}", context.getFullTableName());

        SyncBatchConfig batchConfig = prepareResult.getBatchConfig();
        LocalDateTime targetMaxTime = prepareResult.getTargetMaxTime();

        int totalDataNum = 0;
        int batchCount = 0;
        int offset = 0;
        List<String> errorMessages = new ArrayList<>();

        List<Map<String, Object>> dataList;
        do {
            try {
                // 1. 数据提取 - 自动切换到 source 数据源
                dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchConfig.getReadSize());
                log.debug("📥 本批次提取数据: {} 条 - 表: {}, 偏移: {}",
                         dataList.size(), context.getFullTableName(), offset);

                // 2. 数据处理流水线
                dataList = dataPipeline.process(context, dataList);

                // 3. 哈希检查
                if (!hashChecker.checkDataChange(context, dataList, totalDataNum)) {
                    if (dataList.size() != batchConfig.getReadSize()) {
                        log.debug("🔍 数据哈希值未变化且已到最后一批，结束同步 - 表: {}", context.getFullTableName());
                        break;
                    }
                }

                // 4. 数据插入 - 自动切换到 target 数据源
                if (!dataList.isEmpty()) {
                    int insertedCount = dataInserter.insertData(context, dataList, batchConfig);
                    totalDataNum += insertedCount;
                    batchCount++;
                    log.debug("📤 本批次插入数据: {} 条 - 表: {}", insertedCount, context.getFullTableName());
                }

            } catch (Exception e) {
                String errorMsg = String.format("批次 %d 同步失败: %s", batchCount, e.getMessage());
                errorMessages.add(errorMsg);
                log.error("❌ {} - 表: {}", errorMsg, context.getFullTableName(), e);

                // 根据错误类型决定是否继续
                if (isFatalError(e)) {
                    log.error("💥 遇到致命错误，中断同步 - 表: {}", context.getFullTableName());
                    break;
                }
            }

            offset += batchConfig.getReadSize();

        } while (dataList.size() == batchConfig.getReadSize());

        boolean success = errorMessages.isEmpty();
        if (!success) {
            log.warn("⚠️ 同步完成但存在错误 - 表: {}, 错误数: {}",
                    context.getFullTableName(), errorMessages.size());
        }

        return SyncResult.builder()
                .totalDataNum(totalDataNum)
                .batchCount(batchCount)
                .errorMessages(errorMessages)
                .success(success)
                .build();
    }

    /**
     * 判断是否为致命错误
     */
    private boolean isFatalError(Exception e) {
        // 数据库连接错误、权限错误等为致命错误
        return e instanceof SQLException ||
               e.getMessage().contains("Access denied") ||
               e.getMessage().contains("Connection refused");
    }
}
```

### 数据源切换验证工具

```java
// 新增文件：DataSourceSwitchValidator.java
@Component
@Slf4j
public class DataSourceSwitchValidator {

    /**
     * 记录当前数据源信息
     */
    public void logCurrentDataSource(String operation) {
        try {
            String currentDataSource = DynamicDataSourceContextHolder.peek();
            log.debug("🔄 {} - 当前数据源: {}", operation,
                     currentDataSource != null ? currentDataSource : "default");
        } catch (Exception e) {
            log.debug("⚠️ 无法获取当前数据源信息: {}", e.getMessage());
        }
    }

    /**
     * 验证数据源切换是否正常
     */
    public boolean validateDataSourceSwitch() {
        try {
            // 测试切换到source
            DynamicDataSourceContextHolder.push("source");
            String sourceDS = DynamicDataSourceContextHolder.peek();

            // 测试切换到target
            DynamicDataSourceContextHolder.push("target");
            String targetDS = DynamicDataSourceContextHolder.peek();

            // 清理
            DynamicDataSourceContextHolder.clear();

            boolean isValid = "source".equals(sourceDS) && "target".equals(targetDS);
            log.info("🔍 数据源切换验证: {}", isValid ? "正常" : "异常");
            return isValid;

        } catch (Exception e) {
            log.error("❌ 数据源切换验证失败", e);
            return false;
        }
    }
}
```

## 🚀 部署和验证步骤

### 1. 代码修改
- 移除 `BatchSyncStrategy.executeSyncData` 方法的 `@Transactional` 注解
- 添加增强的错误处理逻辑
- 添加数据源切换验证工具

### 2. 测试验证
```bash
# 1. 启动应用，观察日志中的数据源切换信息
# 2. 执行一次同步任务，验证数据源切换是否正常
# 3. 检查同步结果的数据一致性
```

### 3. 监控指标
- 数据源切换成功率
- 同步任务成功率
- 错误类型分布
- 性能指标对比

---

**总结**：这个问题的根本原因是Spring事务管理器的单数据源限制与Dynamic-Datasource的多数据源切换机制冲突。通过移除跨数据源方法的事务注解，让每个Mapper独立管理自己的数据源，可以完美解决这个问题。同时通过应用层的错误处理和重试机制，确保数据同步的可靠性。🎯✨
