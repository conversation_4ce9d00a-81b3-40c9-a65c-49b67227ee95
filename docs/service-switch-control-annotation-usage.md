# 🎛️ 服务开关控制注解使用指南

## 📋 功能概述

`@ServiceSwitchControl` 注解允许您标记方法，当服务总开关为1（停用）时，被此注解标记的方法将不执行。

## 🚀 快速开始

### 1. 基本使用

在需要受服务开关控制的方法上添加 `@ServiceSwitchControl` 注解：

```java
@Service
public class DataSyncService {
    
    @ServiceSwitchControl("数据同步服务")
    public void syncData() {
        // 当服务开关为1（停用）时，此方法不会执行
        log.info("执行数据同步...");
    }
}
```

### 2. 控制器中使用

```java
@RestController
public class SyncController {
    
    @GetMapping("/sync")
    @ServiceSwitchControl("手动同步接口")
    public String manualSync() {
        // 当服务开关为1（停用）时，此方法不会执行
        return "同步完成";
    }
}
```

### 3. 定时任务中使用

```java
@Component
public class ScheduledTasks {
    
    @Scheduled(fixedRate = 60000)
    @ServiceSwitchControl("定时数据同步")
    public void scheduledSync() {
        // 当服务开关为1（停用）时，此方法不会执行
        log.info("执行定时同步...");
    }
}
```

## 🔧 注解参数

### value（描述信息）

用于日志记录，帮助识别被拦截的方法：

```java
@ServiceSwitchControl("用户数据同步服务")
public void syncUserData() {
    // 方法实现
}
```

### enabled（启用控制）

控制是否启用服务开关检查：

```java
// 启用服务开关控制（默认）
@ServiceSwitchControl(value = "重要服务", enabled = true)
public void importantService() {
    // 方法实现
}

// 禁用服务开关控制
@ServiceSwitchControl(value = "测试服务", enabled = false)
public void testService() {
    // 此方法不受服务开关影响，总是会执行
}
```

## 📊 返回值处理

当方法被服务开关拦截时，会根据方法返回类型返回相应的默认值：

| 返回类型 | 默认返回值 |
|---------|-----------|
| `void` / `Void` | `null` |
| `boolean` / `Boolean` | `false` |
| `int` / `Integer` | `0` |
| `long` / `Long` | `0L` |
| `double` / `Double` | `0.0` |
| `float` / `Float` | `0.0f` |
| `String` | `null` |
| 其他对象类型 | `null` |

## 🔄 工作流程

1. **方法调用** - 当调用被 `@ServiceSwitchControl` 标记的方法时
2. **状态检查** - AOP切面检查当前服务开关状态
3. **条件判断** - 如果服务开关为1（停用），则拦截方法执行
4. **日志记录** - 记录拦截信息到日志
5. **返回默认值** - 根据方法返回类型返回相应默认值

## 📝 日志示例

当方法被拦截时，会输出如下格式的日志：

```
╔═══════════════════════════════════════════════════════════════╗
║                    方法执行被服务开关拦截                     ║
╠═══════════════════════════════════════════════════════════════╣
║ 方法名称: DataSyncServiceImpl.syncManual                     ║
║ 描述信息: 手动数据同步                                        ║
║ 拦截原因: 服务总开关已停用                                    ║
║ 拦截时间: 2025-06-30T10:30:45.123                           ║
╚═══════════════════════════════════════════════════════════════╝
```

## 🧪 测试接口

项目提供了测试接口来验证功能：

```bash
# 测试受控方法
GET /test/service-switch/controlled-method

# 测试不受控方法
GET /test/service-switch/uncontrolled-method

# 获取服务状态
GET /test/service-switch/status
```

## ⚠️ 注意事项

1. **心跳检测不受影响** - 心跳检测方法不会被服务开关控制
2. **异常处理** - 如果服务开关状态检查出现异常，默认允许方法执行
3. **性能影响** - AOP切面会有轻微的性能开销
4. **事务处理** - 被拦截的方法不会开启事务

## 🔗 相关组件

- `ServiceSwitchManager` - 服务开关状态管理
- `ServiceSwitchControlAspect` - AOP切面实现
- `HeartBeatTestServiceImpl` - 心跳检测与状态更新

## 📚 更多信息

详细的服务开关系统文档请参考：`docs/service-switch-usage-guide.md`
