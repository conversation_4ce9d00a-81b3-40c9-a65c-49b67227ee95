# 🕐 定时任务开关控制注解使用指南

## 📋 功能概述

`@SchedulerSwitchControl` 注解允许您标记定时任务方法，当定时任务总开关为1（停用）时，被此注解标记的方法将不执行。

## 🚀 快速开始

### 1. 基本使用

在需要受定时任务开关控制的方法上添加 `@SchedulerSwitchControl` 注解：

```java
@Service
public class DataSyncService {
    
    @Scheduled(cron = "0 */5 * * * ?")
    @SchedulerSwitchControl("数据同步定时任务")
    public void scheduledSyncData() {
        // 当定时任务开关为1（停用）时，此方法不会执行
        log.info("执行定时数据同步...");
    }
}
```

### 2. 控制器中的定时任务使用

```java
@RestController
public class SyncController {
    
    @GetMapping("/sync")
    @Scheduled(cron = "0 30 0 * * ?")
    @SchedulerSwitchControl("手动同步定时任务")
    public String scheduledManualSync() {
        // 当定时任务开关为1（停用）时，此方法不会执行
        return "定时同步完成";
    }
}
```

### 3. Quartz定时任务中使用

```java
@Component
public class QuartzTasks {
    
    @Scheduled(cron = "*/5 * * * * ?")
    @SchedulerSwitchControl("Quartz任务管理")
    public void manageQuartzJob() {
        // 当定时任务开关为1（停用）时，此方法不会执行
        log.info("执行Quartz任务管理...");
    }
}
```

## 🔧 注解参数

### value（描述信息）

用于日志记录，帮助识别被拦截的定时任务：

```java
@SchedulerSwitchControl("用户数据定时同步任务")
public void scheduledSyncUserData() {
    // 方法实现
}
```

### enabled（启用控制）

控制是否启用定时任务开关检查：

```java
// 启用定时任务开关控制（默认）
@SchedulerSwitchControl(value = "重要定时任务", enabled = true)
public void importantScheduledTask() {
    // 方法实现
}

// 禁用定时任务开关控制
@SchedulerSwitchControl(value = "测试定时任务", enabled = false)
public void testScheduledTask() {
    // 此方法不受定时任务开关影响，总是会执行
}
```

## 📊 返回值处理

当定时任务被开关拦截时，会根据方法返回类型返回相应的默认值：

| 返回类型 | 默认返回值 |
|---------|-----------|
| `void` / `Void` | `null` |
| `boolean` / `Boolean` | `false` |
| `int` / `Integer` | `0` |
| `long` / `Long` | `0L` |
| `double` / `Double` | `0.0` |
| `float` / `Float` | `0.0f` |
| `String` | `null` |
| 其他对象类型 | `null` |

## 🔄 工作流程

1. **方法调用** - 当被 `@SchedulerSwitchControl` 标记的方法被调用时
2. **调用源检查** - AOP切面检查调用是否来自定时任务（Spring Scheduler、Quartz等）
3. **HTTP请求放行** - 如果是HTTP请求等非定时任务调用，直接执行方法
4. **状态检查** - 如果是定时任务调用，检查当前定时任务开关状态
5. **条件判断** - 如果定时任务开关为1（停用），则拦截方法执行
6. **日志记录** - 记录拦截信息到日志
7. **返回默认值** - 根据方法返回类型返回相应默认值

## 📝 日志示例

当定时任务被拦截时，会输出如下格式的日志：

```
╔═══════════════════════════════════════════════════════════════╗
║                    定时任务执行被开关拦截                     ║
╠═══════════════════════════════════════════════════════════════╣
║ 方法名称: TaskSyncJob.manageQuartzJob                        ║
║ 描述信息: 管理Quartz定时任务                                  ║
║ 拦截原因: 定时任务总开关已停用                                ║
║ 拦截时间: 2025-07-01T10:30:45.123                           ║
╚═══════════════════════════════════════════════════════════════╝
```

## 🧪 测试接口

项目提供了测试接口来验证功能：

```bash
# 测试受定时任务开关控制的方法
GET /test/service-switch/scheduler-controlled-method

# 测试不受定时任务开关控制的方法
GET /test/service-switch/scheduler-uncontrolled-method

# 获取服务和定时任务状态
GET /test/service-switch/status
```

## 🎯 智能调用源识别

`@SchedulerSwitchControl` 注解具有智能识别功能，只会拦截真正的定时任务调用：

### ✅ 会被拦截的调用
- Spring `@Scheduled` 注解触发的定时任务
- Quartz 定时任务
- `ScheduledThreadPoolExecutor` 执行的任务
- 其他定时调度框架的任务

### ❌ 不会被拦截的调用
- HTTP 请求（GET、POST等）
- 手动方法调用
- 其他非定时任务的调用

### 🔍 识别原理
通过检查当前线程的调用栈，查找是否包含定时任务相关的类：
```java
// 检查的关键类包括：
- org.springframework.scheduling.*
- org.quartz.*
- java.util.concurrent.ScheduledThreadPoolExecutor
- ScheduledMethodRunnable
```

## 🔗 与服务开关的区别

| 特性 | @ServiceSwitchControl | @SchedulerSwitchControl |
|------|----------------------|------------------------|
| 控制范围 | 所有服务方法 | 仅定时任务方法 |
| 开关字段 | serviceSwitch | schedulerSwitch |
| 使用场景 | HTTP接口、服务方法 | 定时任务、调度任务 |
| 调用源识别 | 无 | 智能识别定时任务 |
| 独立控制 | ✅ | ✅ |

## ⚠️ 注意事项

1. **智能识别调用源** - 注解只会拦截来自定时任务的调用，HTTP请求等其他调用会正常执行
2. **心跳检测不受影响** - 心跳检测方法不会被定时任务开关控制
3. **异常处理** - 如果定时任务开关状态检查出现异常，默认允许方法执行
4. **性能影响** - AOP切面会有轻微的性能开销
5. **事务处理** - 被拦截的定时任务不会开启事务
6. **双重控制** - 可以同时使用 `@ServiceSwitchControl` 和 `@SchedulerSwitchControl`
7. **调用栈检查** - 通过检查调用栈来判断是否为定时任务调用，支持Spring Scheduler和Quartz

## 🔗 相关组件

- `ServiceSwitchManager` - 服务和定时任务开关状态管理
- `SchedulerSwitchControlAspect` - 定时任务开关AOP切面实现
- `HeartBeatTestServiceImpl` - 心跳检测与状态更新
- `HeartBeatTestDO` - 开关状态数据模型

## 📚 更多信息

详细的服务开关系统文档请参考：
- `docs/service-switch-usage-guide.md` - 服务开关使用指南
- `docs/service-switch-control-annotation-usage.md` - 服务开关注解使用指南
