# Redis两节点自动故障转移解决方案

## 📋 需求分析

### 环境限制
- **只有两台Redis服务器**：********* (主) 和 ********* (从)
- **无法部署Sentinel**：Sentinel需要至少3个节点避免脑裂
- **业务需求**：主节点故障时自动切换到从节点进行读写操作

### 目标
实现在两台服务器环境下的Redis自动故障转移，确保服务高可用。

## 🔧 解决方案：Spring Boot Lettuce 主从故障转移

### 方案原理
使用Spring Boot + Lettuce客户端的主从配置，结合应用层故障检测，实现自动故障转移。

#### 核心机制
1. **连接管理**：Lettuce维护主从连接池
2. **故障检测**：客户端自动检测主节点健康状态
3. **自动切换**：主节点故障时自动路由到从节点
4. **读写策略**：可配置读写分离策略

## 💻 具体实现

### 1. Maven依赖配置

```xml
<dependencies>
    <!-- Spring Boot Redis Starter -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
    </dependency>
    
    <!-- Lettuce连接池 -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
    </dependency>
</dependencies>
```

### 2. Redis配置类

```java
@Configuration
@EnableConfigurationProperties(RedisProperties.class)
public class RedisFailoverConfig {

    @Value("${redis.master.host:*********}")
    private String masterHost;
    
    @Value("${redis.slave.host:*********}")
    private String slaveHost;
    
    @Value("${redis.port:6379}")
    private int port;
    
    @Value("${redis.password}")
    private String password;
    
    @Value("${redis.database:0}")
    private int database;

    /**
     * Redis连接工厂 - 支持主从故障转移
     */
    @Bean
    @Primary
    public LettuceConnectionFactory redisConnectionFactory() {
        // 创建主从静态配置
        RedisStaticMasterReplicaConfiguration config = 
            new RedisStaticMasterReplicaConfiguration(masterHost, port);
        
        // 添加从节点
        config.addNode(slaveHost, port);
        
        // 设置密码和数据库
        if (StringUtils.hasText(password)) {
            config.setPassword(password);
        }
        config.setDatabase(database);
        
        // Lettuce客户端配置
        LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
            .readFrom(ReadFrom.MASTER_PREFERRED) // 优先主节点，故障时切换从节点
            .commandTimeout(Duration.ofSeconds(3)) // 命令超时时间
            .shutdownTimeout(Duration.ofSeconds(2)) // 关闭超时时间
            .poolConfig(jedisPoolConfig()) // 连接池配置
            .build();
            
        LettuceConnectionFactory factory = new LettuceConnectionFactory(config, clientConfig);
        factory.setValidateConnection(true); // 启用连接验证
        return factory;
    }
    
    /**
     * 连接池配置
     */
    private GenericObjectPoolConfig jedisPoolConfig() {
        GenericObjectPoolConfig config = new GenericObjectPoolConfig();
        config.setMaxTotal(16); // 最大连接数
        config.setMaxIdle(25);  // 最大空闲连接
        config.setMinIdle(10);  // 最小空闲连接
        config.setMaxWaitMillis(3000); // 最大等待时间
        config.setTestOnBorrow(true);   // 获取连接时测试
        config.setTestOnReturn(false);  // 归还连接时不测试
        config.setTestWhileIdle(true);  // 空闲时测试连接
        return config;
    }

    /**
     * RedisTemplate配置
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(LettuceConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 设置序列化器
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LazyPropertyFilter.getDefaultTyper(), ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(mapper);
        
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(serializer);
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);
        template.afterPropertiesSet();
        
        return template;
    }
}
```

### 3. 健康检查和故障处理

```java
@Component
@Slf4j
public class RedisHealthMonitor {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private LettuceConnectionFactory connectionFactory;
    
    private static final String HEALTH_CHECK_KEY = "redis:health:check";
    private volatile boolean masterAvailable = true;
    
    /**
     * 定时健康检查
     */
    @Scheduled(fixedDelay = 5000) // 每5秒检查一次
    public void healthCheck() {
        try {
            // 执行简单的读写操作
            String timestamp = String.valueOf(System.currentTimeMillis());
            redisTemplate.opsForValue().set(HEALTH_CHECK_KEY, timestamp, Duration.ofMinutes(1));
            String result = (String) redisTemplate.opsForValue().get(HEALTH_CHECK_KEY);
            
            if (timestamp.equals(result)) {
                if (!masterAvailable) {
                    log.info("Redis连接已恢复");
                    masterAvailable = true;
                }
            }
        } catch (Exception e) {
            if (masterAvailable) {
                log.error("Redis连接异常，可能正在进行故障转移: {}", e.getMessage());
                masterAvailable = false;
                handleFailover();
            }
        }
    }
    
    /**
     * 处理故障转移
     */
    private void handleFailover() {
        try {
            // 检查从节点状态
            checkSlaveStatus();
            
            // 发送告警通知
            sendAlert("Redis主节点故障，已切换到从节点");
            
        } catch (Exception e) {
            log.error("故障转移处理失败: {}", e.getMessage());
            sendAlert("Redis故障转移失败，需要人工介入");
        }
    }
    
    /**
     * 检查从节点状态
     */
    private void checkSlaveStatus() {
        // 这里可以添加从节点状态检查逻辑
        log.info("检查从节点状态...");
    }
    
    /**
     * 发送告警
     */
    private void sendAlert(String message) {
        // 实现告警逻辑：邮件、短信、钉钉等
        log.warn("告警: {}", message);
    }
    
    /**
     * 获取当前Redis连接状态
     */
    public boolean isRedisAvailable() {
        return masterAvailable;
    }
}
```

### 4. 应用配置文件

```yaml
# application-prod.yml 中的Redis配置
redis:
  master:
    host: *********
  slave:
    host: *********
  port: 6379
  password: 1QAZ2wsx3edc
  database: 0
  timeout: 3000ms
```

## 🚨 故障转移操作流程

### 自动故障转移过程

1. **故障检测**
   ```
   应用检测到主节点 ********* 连接失败
   ↓
   Lettuce客户端自动尝试连接从节点 *********
   ↓
   健康检查组件记录故障状态
   ```

2. **从节点提升**
   ```bash
   # 需要手动或脚本自动执行
   redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc
   SLAVEOF NO ONE
   ```

3. **应用自动适配**
   ```
   Lettuce客户端检测到从节点可用
   ↓
   自动将读写操作路由到从节点
   ↓
   应用服务恢复正常
   ```

### 手动故障转移脚本

```bash
#!/bin/bash
# redis-failover.sh - Redis故障转移脚本

MASTER_HOST="*********"
SLAVE_HOST="*********"
REDIS_PORT="6379"
REDIS_PASSWORD="1QAZ2wsx3edc"

echo "开始Redis故障转移..."

# 检查主节点状态
if ! redis-cli -h $MASTER_HOST -p $REDIS_PORT -a $REDIS_PASSWORD ping > /dev/null 2>&1; then
    echo "主节点 $MASTER_HOST 不可用，开始故障转移"
    
    # 检查从节点状态
    if redis-cli -h $SLAVE_HOST -p $REDIS_PORT -a $REDIS_PASSWORD ping > /dev/null 2>&1; then
        echo "从节点 $SLAVE_HOST 可用，提升为主节点"
        
        # 提升从节点为主节点
        redis-cli -h $SLAVE_HOST -p $REDIS_PORT -a $REDIS_PASSWORD SLAVEOF NO ONE
        
        if [ $? -eq 0 ]; then
            echo "故障转移成功，新主节点: $SLAVE_HOST"
            
            # 可以在这里添加通知逻辑
            # send_notification "Redis故障转移完成"
        else
            echo "故障转移失败"
            exit 1
        fi
    else
        echo "从节点也不可用，需要人工介入"
        exit 1
    fi
else
    echo "主节点正常，无需故障转移"
fi
```

### 故障恢复脚本

```bash
#!/bin/bash
# redis-recovery.sh - Redis故障恢复脚本

ORIGINAL_MASTER="*********"
CURRENT_MASTER="*********"
REDIS_PORT="6379"
REDIS_PASSWORD="1QAZ2wsx3edc"

echo "开始Redis故障恢复..."

# 检查原主节点是否恢复
if redis-cli -h $ORIGINAL_MASTER -p $REDIS_PORT -a $REDIS_PASSWORD ping > /dev/null 2>&1; then
    echo "原主节点 $ORIGINAL_MASTER 已恢复"
    
    # 选择恢复策略
    read -p "选择恢复策略 [1: 恢复原主从关系, 2: 保持当前状态]: " choice
    
    case $choice in
        1)
            echo "恢复原主从关系..."
            # 将当前主节点设为从节点
            redis-cli -h $CURRENT_MASTER -p $REDIS_PORT -a $REDIS_PASSWORD SLAVEOF $ORIGINAL_MASTER $REDIS_PORT
            echo "已恢复原主从关系: $ORIGINAL_MASTER(主) -> $CURRENT_MASTER(从)"
            ;;
        2)
            echo "保持当前状态..."
            # 将原主节点设为从节点
            redis-cli -h $ORIGINAL_MASTER -p $REDIS_PORT -a $REDIS_PASSWORD SLAVEOF $CURRENT_MASTER $REDIS_PORT
            echo "新主从关系: $CURRENT_MASTER(主) -> $ORIGINAL_MASTER(从)"
            ;;
        *)
            echo "无效选择，退出"
            exit 1
            ;;
    esac
else
    echo "原主节点仍不可用"
    exit 1
fi
```

## 📊 监控和告警

### 关键监控指标

1. **连接状态监控**
   ```java
   // 监控Redis连接池状态
   @GetMapping("/redis/pool/status")
   public Map<String, Object> getPoolStatus() {
       Map<String, Object> status = new HashMap<>();
       // 获取连接池统计信息
       return status;
   }
   ```

2. **故障转移日志**
   ```java
   // 记录故障转移事件
   @EventListener
   public void handleFailoverEvent(RedisFailoverEvent event) {
       log.info("Redis故障转移事件: {}", event);
       // 发送告警通知
   }
   ```

### 告警配置示例

```yaml
# 告警配置
alert:
  redis:
    enabled: true
    channels:
      - email
      - sms
      - dingtalk
    thresholds:
      connection-failure: 3  # 连续失败3次触发告警
      response-time: 1000    # 响应时间超过1秒告警
```

## ⚠️ 注意事项和最佳实践

### 重要注意事项

1. **数据一致性**
   - 故障转移期间可能存在数据丢失风险
   - 建议在业务低峰期进行故障转移
   - 重要数据建议额外备份

2. **脑裂问题**
   - 两节点环境无法完全避免脑裂
   - 需要应用层逻辑配合处理
   - 建议增加外部仲裁机制

3. **网络分区**
   - 网络分区可能导致误判
   - 建议增加多种检测机制
   - 设置合理的超时和重试参数

### 最佳实践

1. **定期演练**
   ```bash
   # 定期执行故障转移演练
   ./redis-failover-test.sh
   ```

2. **监控告警**
   - 配置完善的监控体系
   - 设置多级告警机制
   - 建立故障处理流程

3. **备份策略**
   ```bash
   # 定期备份Redis数据
   redis-cli -h ********* -p 6379 -a 1QAZ2wsx3edc --rdb /backup/redis-backup-$(date +%Y%m%d).rdb
   ```

## 🎯 总结

这个方案在两台Redis服务器的限制下，通过Spring Boot + Lettuce的主从配置实现了自动故障转移，虽然不如Sentinel完美，但能够满足基本的高可用需求。

关键优势：
- ✅ 无需额外服务器资源
- ✅ 应用层自动故障检测
- ✅ 配置相对简单
- ✅ 支持自定义故障处理逻辑

建议在实施前进行充分测试，确保故障转移逻辑符合业务需求。
