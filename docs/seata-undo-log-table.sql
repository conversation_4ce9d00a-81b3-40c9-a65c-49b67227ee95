-- =====================================================
-- Seata AT模式 undo_log 表创建脚本
-- =====================================================
-- 说明：此表需要在参与分布式事务的每个数据库中创建
-- 用途：存储事务回滚日志，支持AT模式的自动补偿

-- =====================================================
-- 1. 在 target 数据库中创建 undo_log 表
-- =====================================================
USE target_db;

CREATE TABLE IF NOT EXISTS `undo_log`
(
    `branch_id`     BIGINT       NOT NULL COMMENT '分支事务ID',
    `xid`           VARCHAR(128) NOT NULL COMMENT '全局事务ID',
    `context`       VARCHAR(128) NOT NULL COMMENT 'undo_log上下文信息，如序列化类型',
    `rollback_info` LONGBLOB     NOT NULL COMMENT '回滚信息',
    `log_status`    INT(11)      NOT NULL COMMENT '日志状态：0-正常状态，1-防御状态',
    `log_created`   DATETIME(6)  NOT NULL COMMENT '创建时间',
    `log_modified`  DATETIME(6)  NOT NULL COMMENT '修改时间',
    UNIQUE KEY `ux_undo_log` (`xid`, `branch_id`)
) ENGINE = InnoDB 
  AUTO_INCREMENT = 1 
  DEFAULT CHARSET = utf8mb4 
  COMMENT = 'Seata AT事务模式undo表';

-- 创建索引提高查询性能
ALTER TABLE `undo_log` ADD INDEX `ix_log_created` (`log_created`);
ALTER TABLE `undo_log` ADD INDEX `ix_log_status` (`log_status`);

-- =====================================================
-- 2. 在 source 数据库中创建 undo_log 表
-- =====================================================
USE tj_middle_ground;

CREATE TABLE IF NOT EXISTS `undo_log`
(
    `branch_id`     BIGINT       NOT NULL COMMENT '分支事务ID',
    `xid`           VARCHAR(128) NOT NULL COMMENT '全局事务ID',
    `context`       VARCHAR(128) NOT NULL COMMENT 'undo_log上下文信息，如序列化类型',
    `rollback_info` LONGBLOB     NOT NULL COMMENT '回滚信息',
    `log_status`    INT(11)      NOT NULL COMMENT '日志状态：0-正常状态，1-防御状态',
    `log_created`   DATETIME(6)  NOT NULL COMMENT '创建时间',
    `log_modified`  DATETIME(6)  NOT NULL COMMENT '修改时间',
    UNIQUE KEY `ux_undo_log` (`xid`, `branch_id`)
) ENGINE = InnoDB 
  AUTO_INCREMENT = 1 
  DEFAULT CHARSET = utf8mb4 
  COMMENT = 'Seata AT事务模式undo表';

-- 创建索引提高查询性能
ALTER TABLE `undo_log` ADD INDEX `ix_log_created` (`log_created`);
ALTER TABLE `undo_log` ADD INDEX `ix_log_status` (`log_status`);

-- =====================================================
-- 3. 验证表创建结果
-- =====================================================

-- 验证 target_db 中的表
SELECT 
    TABLE_SCHEMA as '数据库',
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    CREATE_TIME as '创建时间'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'target_db' 
  AND TABLE_NAME = 'undo_log';

-- 验证 tj_middle_ground 中的表
SELECT 
    TABLE_SCHEMA as '数据库',
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    CREATE_TIME as '创建时间'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'tj_middle_ground' 
  AND TABLE_NAME = 'undo_log';

-- =====================================================
-- 4. 清理脚本（可选，用于测试时清理数据）
-- =====================================================

-- 清理 target_db 中的 undo_log 数据
-- DELETE FROM target_db.undo_log WHERE log_created < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 清理 tj_middle_ground 中的 undo_log 数据
-- DELETE FROM tj_middle_ground.undo_log WHERE log_created < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- =====================================================
-- 5. 监控查询（用于运维监控）
-- =====================================================

-- 查看当前活跃的事务
SELECT 
    xid as '全局事务ID',
    COUNT(*) as '分支事务数量',
    MIN(log_created) as '最早创建时间',
    MAX(log_created) as '最晚创建时间'
FROM target_db.undo_log 
WHERE log_status = 0 
GROUP BY xid 
ORDER BY MIN(log_created) DESC;

-- 查看异常状态的事务
SELECT 
    xid as '全局事务ID',
    branch_id as '分支事务ID',
    log_status as '日志状态',
    log_created as '创建时间',
    log_modified as '修改时间'
FROM target_db.undo_log 
WHERE log_status != 0 
ORDER BY log_created DESC;

-- =====================================================
-- 使用说明
-- =====================================================
/*
1. 表结构说明：
   - branch_id: 分支事务ID，由Seata自动生成
   - xid: 全局事务ID，格式如：192.168.1.100:8091:123456789
   - context: 序列化上下文信息
   - rollback_info: 回滚信息，包含原始数据快照
   - log_status: 0-正常，1-防御状态（用于防止脏读）
   - log_created/log_modified: 时间戳

2. 维护建议：
   - 定期清理过期的undo_log记录（建议保留7天）
   - 监控表大小，避免占用过多存储空间
   - 关注异常状态的记录，及时处理

3. 性能优化：
   - 已创建必要的索引
   - 可根据实际情况调整AUTO_INCREMENT起始值
   - 考虑分区表（如果数据量很大）

4. 故障排查：
   - 如果发现长时间未清理的记录，可能存在事务泄漏
   - log_status=1的记录需要特别关注
   - 通过xid可以追踪完整的分布式事务链路
*/
