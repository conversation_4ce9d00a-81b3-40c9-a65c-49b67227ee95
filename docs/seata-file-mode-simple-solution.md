# Seata File模式分布式事务简化解决方案 📁

## 🎯 方案概述

基于Seata File模式实现最简化的分布式事务控制，**无需Nacos、Redis、ZooKeeper等任何外部组件**，只需要Seata Server即可实现跨数据源的事务一致性。

### 核心优势
- ✅ **零依赖**：除了Seata Server，不需要任何其他中间件
- ✅ **配置简单**：几行配置即可启用分布式事务
- ✅ **快速上手**：5分钟即可完成集成
- ✅ **适合数据同步**：专为批量数据处理场景优化

## 🏗️ 架构设计

```mermaid
graph TD
    A[BatchSyncStrategy] --> B[DistributedSyncService]
    B --> C[@GlobalTransactional]
    C --> D[DataExtractor - Source DB]
    C --> E[DataInserter - Target DB]
    B --> F[Seata Server - File Mode]
    F --> G[file.conf]
    F --> H[registry.conf]
```

## 🔧 完整配置方案

### 1. Maven依赖（最精简）

```xml
<!-- pom.xml -->
<properties>
    <seata.version>1.7.1</seata.version>
</properties>

<dependencies>
    <!-- 只需要这一个依赖！ -->
    <dependency>
        <groupId>io.seata</groupId>
        <artifactId>seata-spring-boot-starter</artifactId>
        <version>${seata.version}</version>
    </dependency>
</dependencies>
```

### 2. Spring Boot配置文件

```yaml
# application-seata.yml
seata:
  enabled: true
  application-id: data-sync-service
  tx-service-group: data-sync-group
  enable-auto-data-source-proxy: true
  data-source-proxy-mode: AT
  use-jdk-proxy: false
  
  client:
    rm:
      async-commit-buffer-limit: 10000
      report-retry-count: 5
      table-meta-check-enable: false
      report-success-enable: false
      lock:
        retry-interval: 10
        retry-times: 30
        retry-policy-branch-rollback-on-conflict: true
    tm:
      commit-retry-count: 5
      rollback-retry-count: 5
      default-global-transaction-timeout: 300000  # 5分钟超时
      degrade-check: false
    undo:
      data-validation: true
      log-serialization: jackson
      log-table: undo_log
      only-care-update-columns: true
      compress:
        enable: true
        type: zip
        threshold: 64k
        
  service:
    vgroup-mapping:
      data-sync-group: default  # 事务组映射
    grouplist:
      default: 127.0.0.1:8091   # 直接指定Seata Server地址
    enable-degrade: false
    disable-global-transaction: false
    
  transport:
    type: TCP
    server: NIO
    heartbeat: true
    serialization: seata
    compressor: none
    enable-client-batch-send-request: true

# 数据源配置保持不变
spring:
  datasource:
    dynamic:
      primary: target
      strict: false
      seata: true  # 启用Seata数据源代理
      datasource:
        target:
          url: **********************************************************************************************************
          username: root
          password: password
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        source:
          url: jdbc:mysql://***********:3306/tj_middle_ground?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
          username: syncdata_user
          password: ENC(HYxcLPlqP4nqTYrzc2yqFGGFBWoNp0SnhhG6aQCSJso=)
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
```

### 3. Seata Server配置文件

#### registry.conf
```hocon
# datasync-service/src/main/resources/registry.conf
registry {
  type = "file"
  file {
    name = "file.conf"
  }
}

config {
  type = "file"
  file {
    name = "file.conf"
  }
}
```

#### file.conf
```hocon
# datasync-service/src/main/resources/file.conf
transport {
  type = "TCP"
  server = "NIO"
  heartbeat = true
  serialization = "seata"
  compressor = "none"
  enable-client-batch-send-request = true
}

service {
  vgroupMapping.data-sync-group = "default"
  default.grouplist = "127.0.0.1:8091"
  enableDegrade = false
  disableGlobalTransaction = false
}

client {
  rm {
    asyncCommitBufferLimit = 10000
    lock {
      retryInterval = 10
      retryTimes = 30
      retryPolicyBranchRollbackOnConflict = true
    }
    reportRetryCount = 5
    tableMetaCheckEnable = false
    reportSuccessEnable = false
  }
  tm {
    commitRetryCount = 5
    rollbackRetryCount = 5
    defaultGlobalTransactionTimeout = 300000
    degradeCheck = false
  }
  undo {
    dataValidation = true
    logSerialization = "jackson"
    logTable = "undo_log"
    onlyCareUpdateColumns = true
    compress {
      enable = true
      type = "zip"
      threshold = "64k"
    }
  }
}
```

## 💻 核心代码实现

### 1. Seata配置类

```java
// SeataFileConfig.java
package com.tjsj.sync.modules.sync.config;

import io.seata.spring.annotation.GlobalTransactionScanner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Seata File模式配置类
 * 
 * <AUTHOR> Ye
 * @date 2025/08/03
 * @description 基于File模式的Seata分布式事务配置，无需外部注册中心
 */
@Configuration
@Slf4j
public class SeataFileConfig {

    @Value("${seata.application-id:data-sync-service}")
    private String applicationId;

    @Value("${seata.tx-service-group:data-sync-group}")
    private String txServiceGroup;

    /**
     * 初始化全局事务扫描器
     */
    @Bean
    public GlobalTransactionScanner globalTransactionScanner() {
        log.info("🌐 初始化Seata全局事务扫描器 - File模式");
        log.info("   📋 应用ID: {}", applicationId);
        log.info("   🔗 事务组: {}", txServiceGroup);
        
        GlobalTransactionScanner scanner = new GlobalTransactionScanner(applicationId, txServiceGroup);
        
        log.info("✅ Seata File模式配置完成");
        return scanner;
    }
}
```

### 2. 分布式事务服务

```java
// DistributedSyncService.java
package com.tjsj.sync.modules.sync.service;

import com.tjsj.sync.modules.sync.component.processor.DataExtractor;
import com.tjsj.sync.modules.sync.component.processor.DataInserter;
import com.tjsj.sync.modules.sync.component.processor.DataPipeline;
import com.tjsj.sync.modules.sync.component.checker.HashChecker;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.bo.SyncPrepareResult;
import com.tjsj.sync.modules.sync.model.bo.SyncResult;
import com.tjsj.sync.modules.sync.model.bo.SyncBatchConfig;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 分布式事务同步服务
 * 
 * <AUTHOR> Ye
 * @date 2025/08/03
 * @description 基于Seata实现跨数据源的分布式事务同步
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DistributedSyncService {

    private final DataExtractor dataExtractor;
    private final DataPipeline dataPipeline;
    private final HashChecker hashChecker;
    private final DataInserter dataInserter;

    /**
     * 执行分布式事务同步
     * 
     * 🌐 使用@GlobalTransactional开启全局事务
     * 📊 支持批量数据处理，确保所有批次的原子性
     * 🔄 任何异常都会触发全局回滚
     */
    @GlobalTransactional(
        name = "batch-data-sync",
        rollbackFor = Exception.class,
        timeoutMills = 300000  // 5分钟超时
    )
    public SyncResult executeDistributedSync(SyncPrepareResult prepareResult, SyncContext context) {
        
        String xid = RootContext.getXID();
        String tableName = context.getFullTableName();
        
        log.info("🌐 开始分布式事务同步 - XID: {}, 表: {}", xid, tableName);
        
        try {
            // 执行同步逻辑
            SyncResult result = executeSyncLogic(prepareResult, context, xid);
            
            log.info("✅ 分布式事务同步成功 - XID: {}, 表: {}, 数据量: {}, 批次: {}", 
                    xid, tableName, result.getTotalDataNum(), result.getBatchCount());
            
            return result;
            
        } catch (Exception e) {
            log.error("❌ 分布式事务同步失败，将触发全局回滚 - XID: {}, 表: {}", xid, tableName, e);
            throw new RuntimeException("分布式事务同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行具体的同步逻辑
     */
    private SyncResult executeSyncLogic(SyncPrepareResult prepareResult, SyncContext context, String xid) {
        
        SyncBatchConfig batchConfig = prepareResult.getBatchConfig();
        LocalDateTime targetMaxTime = prepareResult.getTargetMaxTime();
        String tableName = context.getFullTableName();

        int totalDataNum = 0;
        int batchCount = 0;
        int offset = 0;

        List<Map<String, Object>> dataList;
        
        do {
            String batchId = String.format("batch-%d-%d", System.currentTimeMillis() % 10000, batchCount);
            
            try {
                log.debug("📦 处理批次 {} - XID: {}, 表: {}, 偏移: {}", batchId, xid, tableName, offset);

                // 1. 数据提取 - 从source数据库读取（自动切换数据源）
                dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchConfig.getReadSize());
                
                if (dataList.isEmpty()) {
                    log.debug("📭 批次 {} 无数据，结束同步 - XID: {}", batchId, xid);
                    break;
                }
                
                log.debug("📥 批次 {} 提取数据: {} 条 - XID: {}", batchId, dataList.size(), xid);

                // 2. 数据处理流水线
                dataList = dataPipeline.process(context, dataList);

                // 3. 哈希检查
                if (!hashChecker.checkDataChange(context, dataList, totalDataNum)) {
                    if (dataList.size() != batchConfig.getReadSize()) {
                        log.debug("🔍 批次 {} 数据哈希值未变化且已到最后一批，结束同步 - XID: {}", batchId, xid);
                        break;
                    }
                }

                // 4. 数据插入 - 写入target数据库（自动切换数据源）
                if (!dataList.isEmpty()) {
                    int insertedCount = dataInserter.insertData(context, dataList, batchConfig);
                    totalDataNum += insertedCount;
                    batchCount++;

                    log.debug("📤 批次 {} 插入数据: {} 条 - XID: {}", batchId, insertedCount, xid);
                }

                offset += batchConfig.getReadSize();

            } catch (Exception e) {
                log.error("❌ 批次 {} 处理失败 - XID: {}, 表: {}", batchId, xid, tableName, e);
                throw new RuntimeException("批次 " + batchId + " 处理失败: " + e.getMessage(), e);
            }

        } while (dataList.size() == batchConfig.getReadSize());

        log.info("🎉 事务内同步完成 - XID: {}, 表: {}, 总批次: {}, 总数据量: {}", 
                xid, tableName, batchCount, totalDataNum);

        return SyncResult.builder()
                .totalDataNum(totalDataNum)
                .batchCount(batchCount)
                .success(true)
                .build();
    }
}
```

### 3. 修改BatchSyncStrategy

```java
// 在BatchSyncStrategy中添加分布式事务支持
@Component
@Slf4j
@RequiredArgsConstructor
public class BatchSyncStrategy {

    private final DistributedSyncService distributedSyncService;
    
    // 保留原有组件作为fallback
    private final DataExtractor dataExtractor;
    private final DataPipeline dataPipeline;
    private final HashChecker hashChecker;
    private final DataInserter dataInserter;

    @Value("${seata.enabled:false}")
    private boolean seataEnabled;

    /**
     * 执行同步数据 - 支持分布式事务
     */
    public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {
        
        if (seataEnabled) {
            log.info("🌐 使用Seata分布式事务执行同步 - 表: {}", context.getFullTableName());
            return distributedSyncService.executeDistributedSync(prepareResult, context);
        } else {
            log.info("📦 使用本地事务执行同步 - 表: {}", context.getFullTableName());
            return executeLocalSync(prepareResult, context);
        }
    }

    /**
     * 本地事务同步（原有逻辑保留）
     */
    private SyncResult executeLocalSync(SyncPrepareResult prepareResult, SyncContext context) {
        // 保持原有的同步逻辑不变
        log.debug("📦 执行本地同步策略 - 表: {}", context.getFullTableName());

        SyncBatchConfig batchConfig = prepareResult.getBatchConfig();
        LocalDateTime targetMaxTime = prepareResult.getTargetMaxTime();

        int totalDataNum = 0;
        int batchCount = 0;
        int offset = 0;

        List<Map<String, Object>> dataList;
        do {
            // 1. 数据提取
            dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchConfig.getReadSize());

            // 2. 数据处理流水线
            dataList = dataPipeline.process(context, dataList);

            // 3. 哈希检查
            if (!hashChecker.checkDataChange(context, dataList, totalDataNum)) {
                if (dataList.size() != batchConfig.getReadSize()) {
                    break;
                }
            }

            // 4. 数据插入
            if (!dataList.isEmpty()) {
                int insertedCount = dataInserter.insertData(context, dataList, batchConfig);
                totalDataNum += insertedCount;
                batchCount++;
            }

            offset += batchConfig.getReadSize();

        } while (dataList.size() == batchConfig.getReadSize());

        return SyncResult.builder()
                .totalDataNum(totalDataNum)
                .batchCount(batchCount)
                .success(true)
                .build();
    }
}
```

## 🚀 部署步骤

### 1. 创建undo_log表

```sql
-- 在source和target数据库中都要创建
CREATE TABLE IF NOT EXISTS `undo_log`
(
    `branch_id`     BIGINT       NOT NULL COMMENT 'branch transaction id',
    `xid`           VARCHAR(128) NOT NULL COMMENT 'global transaction id',
    `context`       VARCHAR(128) NOT NULL COMMENT 'undo_log context,such as serialization',
    `rollback_info` LONGBLOB     NOT NULL COMMENT 'rollback info',
    `log_status`    INT(11)      NOT NULL COMMENT '0:normal status,1:defense status',
    `log_created`   DATETIME(6)  NOT NULL COMMENT 'create datetime',
    `log_modified`  DATETIME(6)  NOT NULL COMMENT 'modify datetime',
    UNIQUE KEY `ux_undo_log` (`xid`, `branch_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COMMENT ='AT transaction mode undo table';

-- 创建索引提高性能
ALTER TABLE `undo_log` ADD INDEX `ix_log_created` (`log_created`);
```

### 2. 下载并启动Seata Server

```bash
# 下载Seata Server
wget https://github.com/seata/seata/releases/download/v1.7.1/seata-server-1.7.1.zip
unzip seata-server-1.7.1.zip
cd seata-server-1.7.1

# 启动Seata Server（File模式）
sh bin/seata-server.sh -p 8091 -h 127.0.0.1 -m file

# 看到以下日志表示启动成功：
# Server started, listen port: 8091
# The server is running in file mode
```

### 3. 启动应用

```bash
# 启用Seata配置
mvn spring-boot:run -Dspring.profiles.active=dev,seata

# 或者在application.yml中设置
seata:
  enabled: true
```

### 4. 验证功能

```bash
# 查看应用日志，确认Seata连接成功
tail -f logs/application.log | grep -E "(Seata|Global|Transaction)"

# 应该看到类似日志：
# ✅ Seata File模式配置完成
# 🌐 初始化Seata全局事务扫描器 - File模式
# register success, cost xxx ms, version:1.7.1
```

## 📊 配置开关控制

```yaml
# 开启分布式事务
seata:
  enabled: true

# 关闭分布式事务（使用原有逻辑）
seata:
  enabled: false
```

## 🎯 最佳实践

### 1. 事务超时设置
```yaml
seata:
  client:
    tm:
      default-global-transaction-timeout: 300000  # 5分钟，根据数据量调整
```

### 2. 批次大小优化
```java
// 根据表大小动态调整批次
public int getOptimalBatchSize(String tableName) {
    if (isLargeTable(tableName)) {
        return 5000;   // 大表用小批次
    } else {
        return 10000;  // 小表用大批次
    }
}
```

### 3. 监控和日志
```java
@Component
public class SeataMonitor {
    
    @EventListener
    public void onGlobalTransactionEvent(GlobalTransactionEvent event) {
        log.info("🌐 全局事务事件 - XID: {}, 状态: {}, 耗时: {}ms", 
                event.getId(), event.getStatus(), event.getCostTime());
    }
}
```

## 🛡️ 故障排查指南

### 常见问题及解决方案

#### 1. 连接Seata Server失败
```bash
# 错误日志：can not connect to services-server
# 解决方案：
1. 检查Seata Server是否启动：netstat -an | grep 8091
2. 检查防火墙设置
3. 确认配置文件中的地址正确：127.0.0.1:8091
```

#### 2. 数据源代理失败
```yaml
# 错误：DataSource proxy failed
# 解决方案：确保配置正确
spring:
  datasource:
    dynamic:
      seata: true  # 必须设置为true
```

#### 3. undo_log表不存在
```sql
-- 错误：Table 'xxx.undo_log' doesn't exist
-- 解决方案：在每个数据库中创建undo_log表
CREATE TABLE IF NOT EXISTS `undo_log` (
    -- 表结构见上文
);
```

#### 4. 事务超时
```yaml
# 调整超时时间
seata:
  client:
    tm:
      default-global-transaction-timeout: 600000  # 增加到10分钟
```

## 🔧 性能调优建议

### 1. 连接池优化
```yaml
spring:
  datasource:
    dynamic:
      datasource:
        target:
          druid:
            initial-size: 10
            max-active: 50
            max-wait: 60000
        source:
          druid:
            initial-size: 10
            max-active: 50
            max-wait: 60000
```

### 2. Seata客户端优化
```yaml
seata:
  client:
    rm:
      async-commit-buffer-limit: 50000  # 增加异步提交缓冲区
      report-retry-count: 3             # 减少重试次数
    undo:
      compress:
        enable: true                    # 启用压缩
        type: gzip
        threshold: 32k
```

### 3. 批次大小动态调整
```java
@Component
public class BatchSizeCalculator {

    public int calculateBatchSize(SyncContext context) {
        String tableName = context.getFullTableName();

        // 根据表的特征动态调整
        if (tableName.contains("_large") || tableName.contains("_big")) {
            return 2000;   // 大表用小批次，减少锁定时间
        } else if (tableName.contains("_medium")) {
            return 5000;   // 中等表
        } else {
            return 10000;  // 小表用大批次，提高效率
        }
    }
}
```

## 📈 监控和告警

### 1. 事务监控
```java
@Component
@Slf4j
public class SeataTransactionMonitor {

    private final MeterRegistry meterRegistry;

    @EventListener
    public void handleGlobalTransactionEvent(GlobalTransactionEvent event) {
        String status = event.getStatus().name();
        long costTime = event.getCostTime();

        // 记录事务指标
        Timer.builder("seata.transaction.duration")
             .tag("status", status)
             .register(meterRegistry)
             .record(costTime, TimeUnit.MILLISECONDS);

        // 记录事务计数
        Counter.builder("seata.transaction.count")
               .tag("status", status)
               .register(meterRegistry)
               .increment();

        log.info("🌐 全局事务完成 - XID: {}, 状态: {}, 耗时: {}ms",
                event.getId(), status, costTime);

        // 异常情况告警
        if ("RollBack".equals(status)) {
            log.warn("⚠️ 全局事务回滚 - XID: {}, 耗时: {}ms", event.getId(), costTime);
        }
    }
}
```

### 2. 健康检查
```java
@Component
public class SeataHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        try {
            // 检查Seata连接状态
            boolean connected = checkSeataConnection();

            if (connected) {
                return Health.up()
                        .withDetail("seata-server", "connected")
                        .withDetail("mode", "file")
                        .build();
            } else {
                return Health.down()
                        .withDetail("seata-server", "disconnected")
                        .build();
            }
        } catch (Exception e) {
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }

    private boolean checkSeataConnection() {
        // 实现连接检查逻辑
        return true;
    }
}
```

## 🚀 快速验证脚本

### 1. 环境检查脚本
```bash
#!/bin/bash
# check-seata-env.sh

echo "🔍 检查Seata环境..."

# 检查Seata Server
if netstat -an | grep -q ":8091"; then
    echo "✅ Seata Server运行正常 (端口8091)"
else
    echo "❌ Seata Server未启动或端口不正确"
    exit 1
fi

# 检查数据库连接
mysql -h localhost -u root -p -e "SELECT 1" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
    exit 1
fi

# 检查undo_log表
mysql -h localhost -u root -p -e "DESC undo_log" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ undo_log表存在"
else
    echo "❌ undo_log表不存在，请创建"
    exit 1
fi

echo "🎉 环境检查完成，可以启动应用"
```

### 2. 功能测试脚本
```bash
#!/bin/bash
# test-seata-function.sh

echo "🧪 测试Seata分布式事务功能..."

# 启动应用
echo "启动应用..."
mvn spring-boot:run -Dspring.profiles.active=dev,seata &
APP_PID=$!

# 等待应用启动
sleep 30

# 检查应用是否正常启动
if ps -p $APP_PID > /dev/null; then
    echo "✅ 应用启动成功"
else
    echo "❌ 应用启动失败"
    exit 1
fi

# 执行测试同步任务
curl -X POST "http://localhost:8080/sync/manual/test-table" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 同步任务执行成功"
else
    echo "❌ 同步任务执行失败"
fi

# 清理
kill $APP_PID
echo "🎉 功能测试完成"
```

## 📋 部署检查清单

```markdown
## Seata File模式部署检查清单 ✅

### 环境准备
- [ ] JDK 8+ 已安装
- [ ] MySQL数据库可访问
- [ ] 网络端口8091可用

### Seata Server
- [ ] 下载seata-server-1.7.1.zip
- [ ] 解压到指定目录
- [ ] 启动命令：sh bin/seata-server.sh -p 8091 -h 127.0.0.1 -m file
- [ ] 确认启动日志：Server started, listen port: 8091

### 数据库准备
- [ ] 在source数据库创建undo_log表
- [ ] 在target数据库创建undo_log表
- [ ] 验证表结构正确

### 应用配置
- [ ] 添加seata-spring-boot-starter依赖
- [ ] 配置application-seata.yml
- [ ] 创建registry.conf和file.conf
- [ ] 设置seata.enabled=true

### 功能验证
- [ ] 应用启动无错误
- [ ] 日志显示Seata连接成功
- [ ] 执行同步任务测试
- [ ] 验证事务回滚功能

### 监控配置
- [ ] 配置事务监控
- [ ] 设置健康检查
- [ ] 配置告警通知
```

---

**总结**：这个File模式的Seata解决方案是最简化的分布式事务方案！零外部依赖，5分钟完成集成，一个Seata Server搞定一切。配置开关让你可以随时在分布式事务和本地事务之间切换，既保证了数据一致性，又控制了复杂度。就像是给你的数据同步系统装上了一个"简易版银行保险箱"，简单可靠！🎯✨
