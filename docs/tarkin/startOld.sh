#!/bin/bash

# 设置语言环境
export LANG="zh_CN.UTF-8"
export LC_ALL="zh_CN.UTF-8"

# 获取脚本所在目录
BASE_DIR=$(dirname "$0")

# 启动命令
start_project() {
    $JAVA_HOME/bin/java --add-exports java.base/sun.security.action=ALL-UNNAMED \
        -Dfile.encoding=UTF-8 \
        -Dspring.profiles.active=prod \
        -jar datasync-service-1.0.jar &

    # 检查启动是否成功
    if [ $? -ne 0 ]; then
        echo "$(date) Project failed to start, check configuration or logs." >> "$BASE_DIR/logs/startError.log"
    else
        echo "$(date) Project started successfully." >> "$BASE_DIR/logs/startup.log"
    fi
}

# 无限循环检查项目是否启动
while true; do
    # 使用 lsof 检查端口8870是否被占用
    if ! lsof -i:8870 > /dev/null; then
        echo "Starting project..."
        start_project
        sleep 100
    else
        echo "Project is already running."
    fi
    # 每 10 秒检查一次
    sleep 10
done
