#!/bin/bash

# ████████████████████████████████████████████████████████████████████████████████
# █                    SpringBoot服务启动脚本 v7.0                               █
# ████████████████████████████████████████████████████████████████████████████████
# 作用：通用SpringBoot服务启动脚本，支持批量启动和选择性启动
# 维护：修改下方配置区域即可适配不同项目
# 用法：./start.sh [-f 模糊匹配] [-e 精确匹配] [-h 帮助]
# ████████████████████████████████████████████████████████████████████████████████

# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                           🔧 配置区域 - 快速修改                              ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

# 🔴【必改】基础环境配置
ENVIRONMENT="prod"                # 运行环境：dev/test/prod
PROJECT_NAME="Tarkin"             # 项目名称，用于显示标识

# 🔴【必改】服务配置列表
# ┌─────────────────────────────────────────────────────────────────────────────┐
# │ 配置格式说明：                                                               │
# │ "服务名称|JAR文件匹配模式|JVM内存参数|额外JVM参数"                           │
# │                                                                             │
# │ 示例：                                                                      │
# │ "my-service|my-service-.*\.jar|-Xmx512m|--add-opens java.base/java.lang=ALL" │
# └─────────────────────────────────────────────────────────────────────────────┘
## 服务清单（格式：name|jar|memory|extraOpts）
SERVICES=(
  # name               |  jar                              | memory     | extra JVM options
  "datasync-service|datasync-service-.*\.jar|-Xmx6g|--add-exports java.base/sun.security.action=ALL-UNNAMED"
)

# ┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
# ┃                           🚀 脚本执行逻辑                                    ┃
# ┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛

# 🎨 颜色定义
GREEN="\033[32m"    # 成功状态
RED="\033[31m"      # 错误状态
YELLOW="\033[33m"   # 进行中状态
CYAN="\033[96m"     # 信息提示
RESET="\033[0m"     # 重置颜色

# 🔧 运行时变量
MATCH_MODE=""       # 匹配模式：fuzzy/exact
PATTERN=""          # 匹配模式的目标字符串
STARTED_COUNT=0     # 成功启动的服务计数

# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                              参数解析                                       │
# └─────────────────────────────────────────────────────────────────────────────┘
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--fuzzy)
            MATCH_MODE="fuzzy"
            PATTERN="$2"
            shift 2
            ;;
        -e|--exact)
            MATCH_MODE="exact"
            PATTERN="$2"
            shift 2
            ;;
        -h|--help)
            echo -e "${CYAN}用法: $0 [-f 模糊匹配] [-e 精确匹配] [-h 帮助]${RESET}"
            echo -e "${YELLOW}示例: $0 -f gateway    # 启动包含gateway的服务${RESET}"
            echo -e "${YELLOW}示例: $0 -e basic-service    # 启动basic-service服务${RESET}"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 错误参数: $1${RESET}"
            echo -e "${YELLOW}使用 $0 -h 查看帮助${RESET}"
            exit 1
            ;;
    esac
done

# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                              启动信息                                       │
# └─────────────────────────────────────────────────────────────────────────────┘
echo -e "${CYAN}🚀 ${PROJECT_NAME}环境服务启动 - $(date '+%Y-%m-%d %H:%M:%S')${RESET}"
[ -n "$MATCH_MODE" ] && echo -e "${YELLOW}🔍 匹配模式: $MATCH_MODE '$PATTERN'${RESET}"

# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                              服务启动循环                                   │
# └─────────────────────────────────────────────────────────────────────────────┘
for service in "${SERVICES[@]}"; do
    # 🔍 解析服务配置
    IFS="|" read SERVICE_NAME JAR_PATTERN MEMORY_OPTS EXTRA_JVM_OPTS <<< "$service"

    # 🎯 服务匹配检查
    if [ -n "$MATCH_MODE" ]; then
        case "$MATCH_MODE" in
            "fuzzy")
                [[ "$SERVICE_NAME" != *"$PATTERN"* ]] && continue
                ;;
            "exact")
                [[ "$SERVICE_NAME" != "$PATTERN" ]] && continue
                ;;
        esac
    fi

    echo -e "\n${YELLOW}➤ 正在启动: $SERVICE_NAME${RESET}"

    # 📦 查找JAR文件
    jar_files=($(ls $SERVICE_NAME-*.jar 2>/dev/null | grep -E "$JAR_PATTERN" | sort -V))
    if [ ${#jar_files[@]} -eq 0 ]; then
        echo -e "${RED}  ✘ 未找到匹配的JAR文件${RESET}"
        continue
    fi

    # 📄 使用最新版本的JAR文件
    SELECTED_JAR="${jar_files[-1]}"
    echo -e "  📄 JAR文件: $SELECTED_JAR"

    # 🚀 构建启动命令
    startup_command="nohup java"

    # 添加额外JVM参数（如果有）
    [ -n "$EXTRA_JVM_OPTS" ] && startup_command="$startup_command $EXTRA_JVM_OPTS"

    # 添加内存参数（如果有）
    [ -n "$MEMORY_OPTS" ] && startup_command="$startup_command $MEMORY_OPTS"

    # 添加Spring Boot基础参数
    startup_command="$startup_command -Dspring.profiles.active=$ENVIRONMENT -Dfile.encoding=UTF-8"

    # 添加JAR文件和输出重定向
    startup_command="$startup_command -jar $SELECTED_JAR > /dev/null 2>&1 &"

    # ⚡ 执行启动命令
    if eval $startup_command; then
        echo -e "${GREEN}  ✔ 启动成功${RESET}"
        STARTED_COUNT=$((STARTED_COUNT + 1))
    else
        echo -e "${RED}  ✘ 启动失败${RESET}"
    fi
done

# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                              启动结果统计                                   │
# └─────────────────────────────────────────────────────────────────────────────┘
echo -e "\n${CYAN}✅ 启动完成 - 成功启动 $STARTED_COUNT 个服务${RESET}"
echo -e "${CYAN}📅 完成时间: $(date '+%Y-%m-%d %H:%M:%S')${RESET}"