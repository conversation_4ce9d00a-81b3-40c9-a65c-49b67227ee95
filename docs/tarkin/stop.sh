#!/bin/bash

# 指定要停止的 JAR 文件名
JAR_NAME="datasync-service-1.0.jar"

# 查找正在运行的 Java 程序的 PID
# shellcheck disable=SC2009
PID=$(ps aux | grep $JAR_NAME | grep -v grep | awk '{print $2}')

# 检查是否找到该进程
if [ -z "$PID" ]; then
    echo "未找到正在运行的 $JAR_NAME 程序。"
    exit 1
else
    echo "找到 $JAR_NAME 进程，PID: $PID"
fi

# 尝试优雅地终止进程
kill $PID
echo "已发送终止信号给 PID $PID，等待进程停止..."

# 最多等待 5 秒让进程退出
for i in {1..5}; do
    sleep 1
    if ! ps -p $PID > /dev/null; then
        echo "$JAR_NAME 已成功停止。"
        exit 0
    fi
done

# 如果进程没有停止，则强制终止
echo "$JAR_NAME 未能优雅停止，准备强制终止..."
kill -9 $PID

# 检查进程是否已被终止
if ! ps -p $PID > /dev/null; then
    echo "$JAR_NAME 已被强制终止。"
else
    echo "无法终止 $JAR_NAME，请手动检查并终止。"
fi
