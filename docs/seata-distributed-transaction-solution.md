# Seata分布式事务完整解决方案 🌐

## 🎯 方案概述

基于Seata AT模式实现跨数据源的分布式事务控制，确保数据同步过程中的ACID特性。

### 核心架构

```mermaid
graph TD
    A[BatchSyncStrategy] --> B[Seata TM 事务管理器]
    B --> C[Source DB - RM1]
    B --> D[Target DB - RM2]
    C --> E[CloudDataMapper]
    D --> F[LocalDataMapper]
    B --> G[Seata Server]
    G --> H[Nacos注册中心]
```

## 🔧 完整配置方案

### 1. Maven依赖配置

```xml
<!-- pom.xml -->
<properties>
    <seata.version>1.7.1</seata.version>
    <spring-cloud-alibaba.version>2022.0.0.0</spring-cloud-alibaba.version>
</properties>

<dependencies>
    <!-- Seata 分布式事务 -->
    <dependency>
        <groupId>io.seata</groupId>
        <artifactId>seata-spring-boot-starter</artifactId>
        <version>${seata.version}</version>
    </dependency>
    
    <!-- Spring Cloud Alibaba Seata -->
    <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
        <version>${spring-cloud-alibaba.version}</version>
        <exclusions>
            <exclusion>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
            </exclusion>
        </exclusions>
    </dependency>
    
    <!-- Nacos 服务发现 -->
    <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        <version>${spring-cloud-alibaba.version}</version>
    </dependency>
    
    <!-- Nacos 配置中心 -->
    <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        <version>${spring-cloud-alibaba.version}</version>
    </dependency>
</dependencies>
```

### 2. Seata配置文件

```yaml
# application-seata.yml
seata:
  enabled: true
  application-id: data-sync-service
  tx-service-group: data-sync-group
  enable-auto-data-source-proxy: true
  data-source-proxy-mode: AT
  use-jdk-proxy: false
  
  client:
    rm:
      async-commit-buffer-limit: 10000
      report-retry-count: 5
      table-meta-check-enable: false
      report-success-enable: false
      saga-branch-register-enable: false
      saga-json-parser: fastjson
      saga-retry-persist-mode-update: false
      saga-compensate-persist-mode-update: false
      lock:
        retry-interval: 10
        retry-times: 30
        retry-policy-branch-rollback-on-conflict: true
    tm:
      commit-retry-count: 5
      rollback-retry-count: 5
      default-global-transaction-timeout: 60000
      degrade-check: false
      degrade-check-period: 2000
      degrade-check-allow-times: 10
      interceptor-order: -2147482648
    undo:
      data-validation: true
      log-serialization: jackson
      log-table: undo_log
      only-care-update-columns: true
      compress:
        enable: true
        type: zip
        threshold: 64k
    load-balance:
      type: RandomLoadBalance
      virtual-nodes: 10
      
  service:
    vgroup-mapping:
      data-sync-group: default
    grouplist:
      default: 127.0.0.1:8091
    enable-degrade: false
    disable-global-transaction: false
    
  transport:
    shutdown:
      wait: 3
    thread-factory:
      boss-thread-prefix: NettyBoss
      worker-thread-prefix: NettyServerNIOWorker
      server-executor-thread-prefix: NettyServerBizHandler
      share-boss-worker: false
      client-selector-thread-prefix: NettyClientSelector
      client-selector-thread-size: 1
      client-worker-thread-prefix: NettyClientWorkerThread
      worker-thread-size: default
      boss-thread-size: 1
    type: TCP
    server: NIO
    heartbeat: true
    serialization: seata
    compressor: none
    enable-client-batch-send-request: true
    
  config:
    type: nacos
    nacos:
      server-addr: 127.0.0.1:8848
      group: SEATA_GROUP
      namespace: seata
      dataId: seataServer.properties
      username: nacos
      password: nacos
      
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: 127.0.0.1:8848
      group: SEATA_GROUP
      namespace: seata
      username: nacos
      password: nacos
```

### 3. 数据源配置调整

```yaml
# application-datasource.yml
spring:
  datasource:
    dynamic:
      primary: target
      strict: false
      seata: true  # 🔥 启用Seata代理
      datasource:
        target:
          url: **********************************************************************************************************
          username: root
          password: password
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 5
            min-idle: 5
            max-active: 20
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            
        source:
          url: *******************************************************************************************************************
          username: syncdata_user
          password: ENC(HYxcLPlqP4nqTYrzc2yqFGGFBWoNp0SnhhG6aQCSJso=)
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            initial-size: 5
            min-idle: 5
            max-active: 20
            max-wait: 60000
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
```

### 4. Undo Log表创建

```sql
-- 在source和target数据库中都要创建此表
CREATE TABLE IF NOT EXISTS `undo_log`
(
    `branch_id`     BIGINT       NOT NULL COMMENT 'branch transaction id',
    `xid`           VARCHAR(128) NOT NULL COMMENT 'global transaction id',
    `context`       VARCHAR(128) NOT NULL COMMENT 'undo_log context,such as serialization',
    `rollback_info` LONGBLOB     NOT NULL COMMENT 'rollback info',
    `log_status`    INT(11)      NOT NULL COMMENT '0:normal status,1:defense status',
    `log_created`   DATETIME(6)  NOT NULL COMMENT 'create datetime',
    `log_modified`  DATETIME(6)  NOT NULL COMMENT 'modify datetime',
    UNIQUE KEY `ux_undo_log` (`xid`, `branch_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COMMENT ='AT transaction mode undo table';

-- 创建索引
ALTER TABLE `undo_log` ADD INDEX `ix_log_created` (`log_created`);
```

## 💻 代码实现

### 1. Seata配置类

```java
// SeataConfig.java
@Configuration
@EnableAutoConfiguration
@Slf4j
public class SeataConfig {

    /**
     * 初始化GlobalTransactionScanner
     */
    @Bean
    public GlobalTransactionScanner globalTransactionScanner() {
        String applicationName = "data-sync-service";
        String txServiceGroup = "data-sync-group";
        
        GlobalTransactionScanner scanner = new GlobalTransactionScanner(applicationName, txServiceGroup);
        log.info("🌐 Seata全局事务扫描器初始化完成 - 应用: {}, 事务组: {}", applicationName, txServiceGroup);
        return scanner;
    }

    /**
     * Seata数据源代理配置
     */
    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.dynamic")
    public DataSource dataSource() {
        return new DynamicDataSourceCreator().createDataSource();
    }
}
```

### 2. 分布式事务服务层

```java
// DistributedSyncService.java
@Service
@Slf4j
@RequiredArgsConstructor
public class DistributedSyncService {

    private final DataExtractor dataExtractor;
    private final DataPipeline dataPipeline;
    private final HashChecker hashChecker;
    private final DataInserter dataInserter;

    /**
     * 执行分布式事务同步
     * 
     * @GlobalTransactional 开启全局事务
     * - name: 事务名称，用于监控和日志
     * - rollbackFor: 触发回滚的异常类型
     * - timeoutMills: 事务超时时间（毫秒）
     */
    @GlobalTransactional(
        name = "data-sync-transaction",
        rollbackFor = Exception.class,
        timeoutMills = 300000  // 5分钟超时
    )
    public SyncResult executeDistributedSync(SyncPrepareResult prepareResult, SyncContext context) {
        
        String xid = RootContext.getXID();
        log.info("🌐 开始分布式事务同步 - XID: {}, 表: {}", xid, context.getFullTableName());
        
        try {
            // 执行同步逻辑
            SyncResult result = executeSyncWithTransaction(prepareResult, context);
            
            log.info("✅ 分布式事务同步成功 - XID: {}, 表: {}, 数据量: {}", 
                    xid, context.getFullTableName(), result.getTotalDataNum());
            
            return result;
            
        } catch (Exception e) {
            log.error("❌ 分布式事务同步失败 - XID: {}, 表: {}", xid, context.getFullTableName(), e);
            throw new RuntimeException("分布式事务同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行具体的同步逻辑
     */
    private SyncResult executeSyncWithTransaction(SyncPrepareResult prepareResult, SyncContext context) {
        
        SyncBatchConfig batchConfig = prepareResult.getBatchConfig();
        LocalDateTime targetMaxTime = prepareResult.getTargetMaxTime();

        int totalDataNum = 0;
        int batchCount = 0;
        int offset = 0;

        List<Map<String, Object>> dataList;
        do {
            String batchId = generateBatchId(context, batchCount);
            
            try {
                // 1. 数据提取 - 从source数据库读取
                dataList = extractDataFromSource(context, targetMaxTime, offset, batchConfig.getReadSize());
                
                log.debug("📥 批次 {} 提取数据: {} 条 - 表: {}", 
                         batchId, dataList.size(), context.getFullTableName());

                // 2. 数据处理流水线
                dataList = dataPipeline.process(context, dataList);

                // 3. 哈希检查
                if (!hashChecker.checkDataChange(context, dataList, totalDataNum)) {
                    if (dataList.size() != batchConfig.getReadSize()) {
                        log.debug("🔍 批次 {} 数据哈希值未变化且已到最后一批，结束同步", batchId);
                        break;
                    }
                }

                // 4. 数据插入 - 写入target数据库
                if (!dataList.isEmpty()) {
                    int insertedCount = insertDataToTarget(context, dataList, batchConfig);
                    totalDataNum += insertedCount;
                    batchCount++;

                    log.debug("📤 批次 {} 插入数据: {} 条 - 表: {}",
                             batchId, insertedCount, context.getFullTableName());
                }

                offset += batchConfig.getReadSize();

            } catch (Exception e) {
                log.error("❌ 批次 {} 处理失败 - 表: {}", batchId, context.getFullTableName(), e);
                throw new RuntimeException("批次处理失败: " + e.getMessage(), e);
            }

        } while (dataList.size() == batchConfig.getReadSize());

        log.info("🎉 事务内同步完成 - 表: {}, 总批次: {}, 总数据量: {}", 
                context.getFullTableName(), batchCount, totalDataNum);

        return SyncResult.builder()
                .totalDataNum(totalDataNum)
                .batchCount(batchCount)
                .success(true)
                .build();
    }

    /**
     * 从源数据库提取数据
     */
    @Transactional(rollbackFor = Exception.class)
    @DS("source")
    public List<Map<String, Object>> extractDataFromSource(SyncContext context, 
                                                          LocalDateTime targetMaxTime, 
                                                          int offset, 
                                                          int batchSize) {
        
        String xid = RootContext.getXID();
        log.debug("📥 从源数据库提取数据 - XID: {}, 表: {}", xid, context.getFullTableName());
        
        return dataExtractor.extractData(context, targetMaxTime, offset, batchSize);
    }

    /**
     * 向目标数据库插入数据
     */
    @Transactional(rollbackFor = Exception.class)
    @DS("target")
    public int insertDataToTarget(SyncContext context, 
                                List<Map<String, Object>> dataList, 
                                SyncBatchConfig batchConfig) {
        
        String xid = RootContext.getXID();
        log.debug("📤 向目标数据库插入数据 - XID: {}, 表: {}, 数据量: {}", 
                 xid, context.getFullTableName(), dataList.size());
        
        return dataInserter.insertData(context, dataList, batchConfig);
    }

    /**
     * 生成批次ID
     */
    private String generateBatchId(SyncContext context, int batchCount) {
        return String.format("%s-batch-%d-%d", 
                context.getFullTableName().replace(".", "_"), 
                System.currentTimeMillis() % 10000, 
                batchCount);
    }
}
```

### 3. 修改BatchSyncStrategy

```java
// BatchSyncStrategy.java
@Component
@Slf4j
@RequiredArgsConstructor
public class BatchSyncStrategy {

    private final DistributedSyncService distributedSyncService;
    
    // 原有的组件保留作为fallback
    private final DataExtractor dataExtractor;
    private final DataPipeline dataPipeline;
    private final HashChecker hashChecker;
    private final DataInserter dataInserter;

    @Value("${seata.enabled:false}")
    private boolean seataEnabled;

    /**
     * 执行同步数据 - 支持分布式事务
     */
    public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {
        
        if (seataEnabled) {
            log.info("🌐 使用Seata分布式事务执行同步 - 表: {}", context.getFullTableName());
            return distributedSyncService.executeDistributedSync(prepareResult, context);
        } else {
            log.info("📦 使用本地事务执行同步 - 表: {}", context.getFullTableName());
            return executeLocalSync(prepareResult, context);
        }
    }

    /**
     * 本地事务同步（原有逻辑保留）
     */
    private SyncResult executeLocalSync(SyncPrepareResult prepareResult, SyncContext context) {
        // 原有的同步逻辑
        log.debug("📦 执行本地同步策略 - 表: {}", context.getFullTableName());

        SyncBatchConfig batchConfig = prepareResult.getBatchConfig();
        LocalDateTime targetMaxTime = prepareResult.getTargetMaxTime();

        int totalDataNum = 0;
        int batchCount = 0;
        int offset = 0;

        List<Map<String, Object>> dataList;
        do {
            // 1. 数据提取
            dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchConfig.getReadSize());

            // 2. 数据处理流水线
            dataList = dataPipeline.process(context, dataList);

            // 3. 哈希检查
            if (!hashChecker.checkDataChange(context, dataList, totalDataNum)) {
                if (dataList.size() != batchConfig.getReadSize()) {
                    break;
                }
            }

            // 4. 数据插入
            if (!dataList.isEmpty()) {
                int insertedCount = dataInserter.insertData(context, dataList, batchConfig);
                totalDataNum += insertedCount;
                batchCount++;
            }

            offset += batchConfig.getReadSize();

        } while (dataList.size() == batchConfig.getReadSize());

        return SyncResult.builder()
                .totalDataNum(totalDataNum)
                .batchCount(batchCount)
                .success(true)
                .build();
    }
}
```

## 🚀 部署和启动

### 1. Seata Server部署

```bash
# 下载Seata Server
wget https://github.com/seata/seata/releases/download/v1.7.1/seata-server-1.7.1.zip
unzip seata-server-1.7.1.zip
cd seata-server-1.7.1

# 配置registry.conf
registry {
  type = "nacos"
  nacos {
    application = "seata-server"
    serverAddr = "127.0.0.1:8848"
    group = "SEATA_GROUP"
    namespace = "seata"
    cluster = "default"
    username = "nacos"
    password = "nacos"
  }
}

config {
  type = "nacos"
  nacos {
    serverAddr = "127.0.0.1:8848"
    group = "SEATA_GROUP"
    namespace = "seata"
    dataId = "seataServer.properties"
    username = "nacos"
    password = "nacos"
  }
}

# 启动Seata Server
sh bin/seata-server.sh -p 8091 -h 127.0.0.1 -m file
```

### 2. 应用配置

```yaml
# application.yml
spring:
  profiles:
    active: dev,seata,datasource
  application:
    name: data-sync-service
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: seata
        group: SEATA_GROUP

# 启用Seata
seata:
  enabled: true

# 日志配置
logging:
  level:
    io.seata: DEBUG
    com.tjsj.sync: DEBUG
```

## 📊 性能优化建议

### 1. 批次大小优化

```java
// 根据数据量动态调整批次大小
@Component
public class BatchSizeOptimizer {
    
    public int calculateOptimalBatchSize(SyncContext context) {
        // 小表：批次大小 1000
        // 中表：批次大小 5000  
        // 大表：批次大小 10000
        
        String tableName = context.getFullTableName();
        if (isLargeTable(tableName)) {
            return 10000;
        } else if (isMediumTable(tableName)) {
            return 5000;
        } else {
            return 1000;
        }
    }
}
```

### 2. 事务超时配置

```yaml
seata:
  client:
    tm:
      default-global-transaction-timeout: 300000  # 5分钟
```

### 3. 连接池优化

```yaml
spring:
  datasource:
    dynamic:
      datasource:
        target:
          druid:
            max-active: 50        # 增加连接池大小
            initial-size: 10      # 初始连接数
            max-wait: 30000       # 最大等待时间
```

## 🛡️ 异常处理和监控

### 1. 全局异常处理

```java
@Component
@Slf4j
public class SeataExceptionHandler {

    @EventListener
    public void handleGlobalTransactionEvent(GlobalTransactionEvent event) {
        log.info("🌐 全局事务事件 - XID: {}, 状态: {}", event.getId(), event.getStatus());
    }

    public void handleSeataException(String xid, Exception e) {
        log.error("❌ Seata事务异常 - XID: {}", xid, e);
        
        // 发送告警通知
        alertService.sendAlert("Seata事务异常", 
                String.format("XID: %s, 错误: %s", xid, e.getMessage()));
    }
}
```

### 2. 监控指标

```java
@Component
public class SeataMetrics {
    
    private final MeterRegistry meterRegistry;
    
    public void recordTransactionSuccess(String tableName, long duration) {
        Timer.Sample.start(meterRegistry)
             .stop(Timer.builder("seata.transaction.duration")
                        .tag("table", tableName)
                        .tag("status", "success")
                        .register(meterRegistry));
    }
    
    public void recordTransactionFailure(String tableName, String errorType) {
        Counter.builder("seata.transaction.failure")
               .tag("table", tableName)
               .tag("error", errorType)
               .register(meterRegistry)
               .increment();
    }
}
```

## 🎯 最佳实践和注意事项

### 1. 事务边界设计原则

```java
// ✅ 推荐：合理的事务边界
@GlobalTransactional(timeoutMills = 60000)  // 1分钟超时
public void syncSmallBatch() {
    // 处理少量数据，快速完成
}

// ❌ 避免：过大的事务边界
@GlobalTransactional(timeoutMills = 3600000)  // 1小时超时
public void syncAllData() {
    // 处理大量数据，长时间占用资源
}
```

### 2. 数据一致性检查

```java
@Component
public class DataConsistencyChecker {

    /**
     * 同步后数据一致性验证
     */
    public boolean verifyDataConsistency(SyncContext context, SyncResult result) {
        try {
            // 1. 检查数据量一致性
            long sourceCount = getSourceDataCount(context);
            long targetCount = getTargetDataCount(context);

            if (sourceCount != targetCount) {
                log.error("❌ 数据量不一致 - 表: {}, 源: {}, 目标: {}",
                         context.getFullTableName(), sourceCount, targetCount);
                return false;
            }

            // 2. 检查关键字段哈希值
            String sourceHash = calculateSourceDataHash(context);
            String targetHash = calculateTargetDataHash(context);

            if (!Objects.equals(sourceHash, targetHash)) {
                log.error("❌ 数据哈希不一致 - 表: {}", context.getFullTableName());
                return false;
            }

            log.info("✅ 数据一致性验证通过 - 表: {}", context.getFullTableName());
            return true;

        } catch (Exception e) {
            log.error("❌ 数据一致性检查异常 - 表: {}", context.getFullTableName(), e);
            return false;
        }
    }
}
```

### 3. 性能调优配置

```yaml
# 高性能配置示例
seata:
  client:
    rm:
      async-commit-buffer-limit: 50000      # 增加异步提交缓冲区
      report-retry-count: 3                 # 减少重试次数
      table-meta-check-enable: false        # 关闭表元数据检查
    tm:
      commit-retry-count: 3                 # 减少提交重试次数
      rollback-retry-count: 3               # 减少回滚重试次数
    undo:
      compress:
        enable: true                        # 启用undo log压缩
        type: gzip                          # 使用gzip压缩
        threshold: 32k                      # 32KB以上才压缩
```

### 4. 故障恢复机制

```java
@Component
@Slf4j
public class SeataRecoveryService {

    /**
     * 检查并恢复悬挂事务
     */
    @Scheduled(fixedDelay = 300000)  // 每5分钟检查一次
    public void checkAndRecoverHangingTransactions() {
        try {
            // 查询超时的全局事务
            List<String> hangingXids = findHangingTransactions();

            for (String xid : hangingXids) {
                log.warn("⚠️ 发现悬挂事务，尝试恢复 - XID: {}", xid);

                try {
                    // 尝试回滚悬挂事务
                    GlobalTransactionContext.reload(xid).rollback();
                    log.info("✅ 悬挂事务回滚成功 - XID: {}", xid);
                } catch (Exception e) {
                    log.error("❌ 悬挂事务回滚失败 - XID: {}", xid, e);
                }
            }

        } catch (Exception e) {
            log.error("❌ 悬挂事务检查异常", e);
        }
    }

    private List<String> findHangingTransactions() {
        // 实现查询逻辑，从Seata Server或数据库中查询超时事务
        return new ArrayList<>();
    }
}
```

### 5. 监控和告警

```java
@Component
public class SeataMonitoringService {

    private final MeterRegistry meterRegistry;
    private final AlertService alertService;

    /**
     * 记录事务指标
     */
    public void recordTransactionMetrics(String xid, String operation, boolean success, long duration) {
        // 记录事务执行时间
        Timer.builder("seata.transaction.duration")
             .tag("operation", operation)
             .tag("status", success ? "success" : "failure")
             .register(meterRegistry)
             .record(duration, TimeUnit.MILLISECONDS);

        // 记录事务计数
        Counter.builder("seata.transaction.count")
               .tag("operation", operation)
               .tag("status", success ? "success" : "failure")
               .register(meterRegistry)
               .increment();

        // 失败时发送告警
        if (!success) {
            alertService.sendAlert("Seata事务失败",
                    String.format("XID: %s, 操作: %s", xid, operation));
        }
    }

    /**
     * 健康检查
     */
    @EventListener
    @Async
    public void onApplicationReady(ApplicationReadyEvent event) {
        // 启动时检查Seata连接
        checkSeataConnection();
    }

    private void checkSeataConnection() {
        try {
            // 执行一个简单的全局事务来测试连接
            testGlobalTransaction();
            log.info("✅ Seata连接检查通过");
        } catch (Exception e) {
            log.error("❌ Seata连接检查失败", e);
            alertService.sendAlert("Seata连接异常", e.getMessage());
        }
    }

    @GlobalTransactional(timeoutMills = 5000)
    public void testGlobalTransaction() {
        // 简单的测试事务
        log.debug("🧪 执行Seata连接测试事务");
    }
}
```

### 6. 部署检查清单

```markdown
## Seata部署检查清单 ✅

### 基础环境
- [ ] Nacos服务正常运行
- [ ] MySQL数据库可访问
- [ ] 网络连通性正常

### Seata Server
- [ ] Seata Server启动成功
- [ ] 注册到Nacos成功
- [ ] 配置文件正确加载

### 应用配置
- [ ] Maven依赖正确引入
- [ ] 配置文件语法正确
- [ ] 数据源代理启用
- [ ] undo_log表已创建

### 功能验证
- [ ] 全局事务正常开启
- [ ] 分支事务正常注册
- [ ] 异常回滚正常工作
- [ ] 监控指标正常上报

### 性能测试
- [ ] 事务吞吐量满足要求
- [ ] 响应时间在可接受范围
- [ ] 资源使用率正常
- [ ] 无内存泄漏
```

## 🚀 快速启动指南

### 1. 环境准备
```bash
# 启动Nacos
docker run -d -p 8848:8848 --name nacos nacos/nacos-server:latest

# 启动Seata Server
docker run -d -p 8091:8091 --name seata-server seataio/seata-server:latest
```

### 2. 配置验证
```bash
# 检查Nacos中的Seata配置
curl "http://localhost:8848/nacos/v1/cs/configs?dataId=seataServer.properties&group=SEATA_GROUP"

# 检查Seata Server状态
curl "http://localhost:8091/health"
```

### 3. 应用启动
```bash
# 启动应用
mvn spring-boot:run -Dspring.profiles.active=dev,seata

# 检查应用日志
tail -f logs/application.log | grep -E "(Seata|Global|Transaction)"
```

---

**总结**：这个完整的Seata分布式事务解决方案不仅提供了跨数据源的ACID保证，还包含了完整的监控、告警、故障恢复机制。通过合理的配置和最佳实践，可以在保证数据一致性的同时，维持良好的系统性能。记住：分布式事务是把双刃剑，要在一致性和性能之间找到最佳平衡点！🌐⚖️✨
