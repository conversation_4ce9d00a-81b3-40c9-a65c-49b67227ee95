# Seata快速启动指南 🚀

## 🎯 5分钟快速集成步骤

### 第1步：下载并启动Seata Server（2分钟）

```bash
# 1. 下载Seata Server
wget https://github.com/seata/seata/releases/download/v1.7.1/seata-server-1.7.1.zip

# 2. 解压
unzip seata-server-1.7.1.zip
cd seata-server-1.7.1

# 3. 启动Seata Server（File模式）
sh bin/seata-server.sh -p 8091 -h 127.0.0.1 -m file

# 看到以下日志表示启动成功：
# Server started, listen port: 8091
# The server is running in file mode
```

### 第2步：创建undo_log表（1分钟）

```sql
-- 在target_db数据库中执行
CREATE TABLE IF NOT EXISTS `undo_log`
(
    `branch_id`     BIGINT       NOT NULL COMMENT '分支事务ID',
    `xid`           VARCHAR(128) NOT NULL COMMENT '全局事务ID',
    `context`       VARCHAR(128) NOT NULL COMMENT 'undo_log上下文信息',
    `rollback_info` LONGBLOB     NOT NULL COMMENT '回滚信息',
    `log_status`    INT(11)      NOT NULL COMMENT '日志状态：0-正常，1-防御',
    `log_created`   DATETIME(6)  NOT NULL COMMENT '创建时间',
    `log_modified`  DATETIME(6)  NOT NULL COMMENT '修改时间',
    UNIQUE KEY `ux_undo_log` (`xid`, `branch_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = 'Seata AT事务模式undo表';

-- 在tj_middle_ground数据库中执行相同的SQL
```

### 第3步：启动应用（1分钟）

```bash
# 启动应用（已自动包含seata配置）
mvn spring-boot:run

# 或者指定profile
mvn spring-boot:run -Dspring.profiles.active=dev,seata
```

### 第4步：验证功能（1分钟）

```bash
# 查看应用日志，确认Seata连接成功
tail -f logs/application.log | grep -E "(Seata|Global|Transaction)"

# 应该看到类似日志：
# ✅ Seata File模式配置完成
# 🌐 初始化Seata全局事务扫描器 - File模式
# register success, cost xxx ms, version:1.7.1
```

## 🔧 配置开关控制

### 启用分布式事务
```yaml
# application-seata.yml 或 application.yml
seata:
  enabled: true
```

### 禁用分布式事务（使用原有逻辑）
```yaml
seata:
  enabled: false
```

## 📊 验证分布式事务效果

### 1. 正常同步测试
```bash
# 执行同步任务，观察日志
curl -X POST "http://localhost:8080/sync/manual/test-table"

# 日志应显示：
# 🌐 使用Seata分布式事务执行同步 - 表: xxx
# 🆔 全局事务ID: *************:8091:123456789
# ✅ 分布式事务同步成功
```

### 2. 异常回滚测试
```bash
# 人为制造异常（如断开target数据库连接）
# 观察日志应显示：
# ❌ 分布式事务同步失败，将触发全局回滚
# 🔄 将触发全局事务回滚
```

### 3. 查看undo_log表
```sql
-- 查看事务日志
SELECT xid, branch_id, log_status, log_created 
FROM undo_log 
ORDER BY log_created DESC 
LIMIT 10;
```

## 🛠️ 故障排查

### 问题1：连接Seata Server失败
```bash
# 错误：can not connect to services-server
# 解决：
1. 检查Seata Server是否启动：netstat -an | grep 8091
2. 检查防火墙设置
3. 确认配置文件中的地址：127.0.0.1:8091
```

### 问题2：数据源代理失败
```yaml
# 错误：DataSource proxy failed
# 解决：确保配置正确
spring:
  datasource:
    dynamic:
      seata: true  # 必须设置为true
```

### 问题3：undo_log表不存在
```sql
-- 错误：Table 'xxx.undo_log' doesn't exist
-- 解决：在每个数据库中创建undo_log表
```

## 📈 性能对比测试

### 测试脚本
```bash
#!/bin/bash
echo "🧪 性能对比测试开始..."

# 1. 测试本地事务模式
echo "📦 测试本地事务模式..."
curl -X PUT "http://localhost:8080/config/seata/enabled/false"
time curl -X POST "http://localhost:8080/sync/manual/test-table"

# 2. 测试分布式事务模式  
echo "🌐 测试分布式事务模式..."
curl -X PUT "http://localhost:8080/config/seata/enabled/true"
time curl -X POST "http://localhost:8080/sync/manual/test-table"

echo "🎉 性能对比测试完成"
```

## 🎯 最佳实践

### 1. 生产环境建议
```yaml
seata:
  client:
    tm:
      default-global-transaction-timeout: 300000  # 5分钟超时
    undo:
      compress:
        enable: true    # 启用压缩
        type: gzip
        threshold: 32k  # 32KB以上才压缩
```

### 2. 监控配置
```yaml
# 启用详细日志
logging:
  level:
    io.seata: INFO
    com.tjsj.sync: INFO
```

### 3. 定期维护
```sql
-- 定期清理过期的undo_log（建议每天执行）
DELETE FROM undo_log 
WHERE log_created < DATE_SUB(NOW(), INTERVAL 7 DAY);
```

## 🔄 回滚方案

如果遇到问题需要快速回滚：

### 方法1：配置开关
```yaml
# 立即禁用Seata
seata:
  enabled: false
```

### 方法2：移除profile
```yaml
# 修改application.yml
spring:
  profiles:
    active: dev  # 移除seata
```

### 方法3：注释依赖
```xml
<!-- 临时注释Seata依赖 -->
<!--
<dependency>
    <groupId>io.seata</groupId>
    <artifactId>seata-spring-boot-starter</artifactId>
    <version>${seata.version}</version>
</dependency>
-->
```

## 📞 技术支持

### 常用命令
```bash
# 查看Seata Server状态
netstat -an | grep 8091

# 查看应用Seata连接状态
curl http://localhost:8080/actuator/health

# 查看当前事务模式
curl http://localhost:8080/sync/status
```

### 配置说明
我们使用Spring Boot的YAML配置方式，所有Seata配置都在 `application.yml` 中：
- ✅ **无需** `registry.conf` 文件
- ✅ **无需** `file.conf` 文件
- ✅ **只需** `application.yml` 中的seata配置段

### 日志关键字
- `🌐` - 分布式事务相关
- `📦` - 本地事务相关  
- `✅` - 操作成功
- `❌` - 操作失败
- `🔄` - 事务回滚

---

**总结**：这个快速启动指南让你在5分钟内完成Seata集成！通过配置开关可以随时在分布式事务和本地事务之间切换，既保证了数据一致性，又控制了风险。🎯✨
