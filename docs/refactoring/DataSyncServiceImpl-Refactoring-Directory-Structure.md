# DataSyncServiceImpl 重构后类目录结构规范

## 📋 概述

本文档定义了 `DataSyncServiceImpl` 重构后新创建类的目录结构规范，确保代码组织符合项目架构和Java开发最佳实践。

## 🏗️ 项目当前目录结构分析

### 现有目录结构
```
datasync-service/src/main/java/com/tjsj/sync/modules/
├── business/           # 业务相关模块
├── common/            # 通用配置和工具
├── sync/              # 数据同步核心模块
│   ├── common/        # 同步通用配置
│   ├── controller/    # 控制器层
│   ├── mapper/        # 数据访问层
│   ├── model/         # 数据模型
│   ├── service/       # 服务层
│   │   └── impl/      # 服务实现
│   └── utils/         # 同步工具类
├── tailored/          # 定制化模块
├── test/              # 测试相关
├── transaction/       # 事务处理模块
└── util/              # 全局工具类
```

### 设计原则
1. **分层架构**：按照MVC分层原则组织代码
2. **职责分离**：相同职责的类放在同一目录
3. **模块化**：按功能模块划分目录结构
4. **可扩展性**：为未来扩展预留合理的目录结构

## 📁 重构后新类目录分配方案

### 1. 核心服务层类

#### 1.1 主服务类 (保持原位置)
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/service/impl/
└── DataSyncServiceImpl.java  # 重构后的主服务类
```
**说明**：保持在原有位置，只是内部实现重构

#### 1.2 同步上下文类
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/model/
└── SyncContext.java  # 同步上下文数据模型
```
**职责**：封装同步过程中的状态信息
**选择理由**：作为数据传输对象，属于模型层

### 2. 同步组件层

#### 2.1 创建新的组件目录
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/component/
├── checker/           # 检查器组件
├── preparer/          # 准备器组件  
├── synchronizer/      # 同步器组件
├── processor/         # 处理器组件
└── handler/           # 处理器组件
```

#### 2.2 前置检查器
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/component/checker/
├── SyncPreChecker.java           # 同步前置检查器
└── TimeComparisonService.java    # 时间比较服务
```

#### 2.3 同步准备器
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/component/preparer/
└── SyncPreparer.java  # 同步准备器
```

#### 2.4 数据同步器
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/component/synchronizer/
├── DataSynchronizer.java      # 数据同步器
├── SyncStrategyFactory.java   # 同步策略工厂
└── strategy/                  # 策略子目录
    ├── SyncStrategy.java      # 同步策略接口
    ├── BatchSyncStrategy.java # 批量同步策略
    └── IncrementalSyncStrategy.java  # 增量同步策略(预留)
```

#### 2.5 数据处理器
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/component/processor/
├── DataExtractor.java     # 数据提取器
├── DataProcessor.java     # 数据处理器
├── DataInserter.java      # 数据插入器
├── ColumnFilter.java      # 字段过滤器
├── HashChecker.java       # 哈希检查器
└── KeywordProcessor.java  # 关键字处理器
```

#### 2.6 后置处理器和异常处理器
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/component/handler/
├── SyncPostProcessor.java     # 同步后置处理器
└── SyncExceptionHandler.java  # 同步异常处理器
```

### 3. 配置管理层

#### 3.1 配置类
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/common/config/
├── SyncConfigManager.java  # 同步配置管理器
└── SyncConfig.java         # 同步配置类 (如果不存在)
```
**说明**：如果 `SyncConfig.java` 已存在于 `utils` 目录，可保持原位置

### 4. 工具类层

#### 4.1 保持现有工具类位置
```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/utils/
├── MySqlKeyword.java    # MySQL关键字处理 (已存在)
├── SyncUtil.java        # 同步工具类 (已存在)
└── HashUtil.java        # 哈希工具类 (如需新建)
```

## 📋 完整目录结构图

```
datasync-service/src/main/java/com/tjsj/sync/modules/sync/
├── common/
│   └── config/
│       ├── SyncConfigManager.java      # 🆕 配置管理器
│       └── SyncConfig.java             # 配置类
├── component/                          # 🆕 组件目录
│   ├── checker/                        # 🆕 检查器组件
│   │   ├── SyncPreChecker.java         # 🆕 前置检查器
│   │   └── TimeComparisonService.java  # 🆕 时间比较服务
│   ├── preparer/                       # 🆕 准备器组件
│   │   └── SyncPreparer.java           # 🆕 同步准备器
│   ├── synchronizer/                   # 🆕 同步器组件
│   │   ├── DataSynchronizer.java       # 🆕 数据同步器
│   │   ├── SyncStrategyFactory.java    # 🆕 策略工厂
│   │   └── strategy/                   # 🆕 策略子目录
│   │       ├── SyncStrategy.java       # 🆕 策略接口
│   │       └── BatchSyncStrategy.java  # 🆕 批量策略
│   ├── processor/                      # 🆕 处理器组件
│   │   ├── DataExtractor.java          # 🆕 数据提取器
│   │   ├── DataProcessor.java          # 🆕 数据处理器
│   │   ├── DataInserter.java           # 🆕 数据插入器
│   │   ├── ColumnFilter.java           # 🆕 字段过滤器
│   │   ├── HashChecker.java            # 🆕 哈希检查器
│   │   └── KeywordProcessor.java       # 🆕 关键字处理器
│   └── handler/                        # 🆕 处理器组件
│       ├── SyncPostProcessor.java      # 🆕 后置处理器
│       └── SyncExceptionHandler.java   # 🆕 异常处理器
├── controller/
│   └── DatabaseSyncController.java     # 现有控制器
├── mapper/
│   ├── CloudDataMapper.java            # 现有Mapper
│   └── LocalDataMapper.java            # 现有Mapper
├── model/
│   ├── SyncContext.java                # 🆕 同步上下文
│   └── DataSynParam.java               # 现有模型
├── service/
│   ├── DataSyncService.java            # 现有接口
│   └── impl/
│       └── DataSyncServiceImpl.java    # 🔄 重构后的实现
└── utils/
    ├── MySqlKeyword.java               # 现有工具类
    ├── SyncUtil.java                   # 现有工具类
    └── HashUtil.java                   # 🆕 哈希工具类(如需要)
```

## 🎯 目录分配原则说明

### 1. **component 目录设计理念**
- **职责分离**：每个组件目录包含相关的功能类
- **可扩展性**：便于添加新的组件类型
- **清晰性**：通过目录名称直观了解组件职责

### 2. **分层架构遵循**
- **model**：数据模型和上下文对象
- **service**：业务逻辑接口和实现
- **component**：业务组件和策略实现
- **common/config**：配置管理相关类
- **utils**：工具类和辅助方法

### 3. **命名规范**
- **目录名**：小写，使用复数形式
- **类名**：大驼峰命名，体现具体职责
- **包名**：遵循Java包命名规范

## 📦 Maven 包结构

```xml
<!-- 重构后的包结构 -->
com.tjsj.sync.modules.sync
├── .common.config          # 配置管理
├── .component.checker      # 检查器组件
├── .component.preparer     # 准备器组件
├── .component.synchronizer # 同步器组件
├── .component.processor    # 处理器组件
├── .component.handler      # 处理器组件
├── .controller             # 控制器层
├── .mapper                 # 数据访问层
├── .model                  # 数据模型层
├── .service                # 服务层
└── .utils                  # 工具类层
```

## 🔄 迁移建议

### 阶段一：创建目录结构
1. 创建 `component` 主目录及子目录
2. 创建各个组件的包结构
3. 准备类文件模板

### 阶段二：逐步迁移类文件
1. 先创建接口和抽象类
2. 再创建具体实现类
3. 最后更新依赖注入配置

### 阶段三：测试验证
1. 单元测试验证各组件功能
2. 集成测试验证整体流程
3. 包结构和依赖关系检查

## ⚠️ 注意事项

1. **Spring 注解扫描**：确保新包路径在Spring组件扫描范围内
2. **依赖注入**：新类需要正确配置Spring依赖注入
3. **包导入**：更新相关类的import语句
4. **测试类**：同步更新测试类的包结构

## 📝 类创建清单

### 🆕 需要新建的类文件

| 序号 | 类名 | 完整路径 | 类型 | 优先级 | 依赖关系 |
|------|------|----------|------|--------|----------|
| 1 | `SyncContext` | `model/SyncContext.java` | 数据模型 | 高 | 无 |
| 2 | `SyncPreChecker` | `component/checker/SyncPreChecker.java` | 组件 | 高 | SyncTableConfigService |
| 3 | `TimeComparisonService` | `component/checker/TimeComparisonService.java` | 服务 | 中 | DataMapper |
| 4 | `SyncPreparer` | `component/preparer/SyncPreparer.java` | 组件 | 高 | SyncTableConfigService |
| 5 | `DataSynchronizer` | `component/synchronizer/DataSynchronizer.java` | 组件 | 高 | SyncStrategyFactory |
| 6 | `SyncStrategy` | `component/synchronizer/strategy/SyncStrategy.java` | 接口 | 高 | 无 |
| 7 | `BatchSyncStrategy` | `component/synchronizer/strategy/BatchSyncStrategy.java` | 实现 | 高 | SyncStrategy |
| 8 | `SyncStrategyFactory` | `component/synchronizer/SyncStrategyFactory.java` | 工厂 | 高 | SyncStrategy |
| 9 | `DataExtractor` | `component/processor/DataExtractor.java` | 组件 | 高 | DataMapper |
| 10 | `DataProcessor` | `component/processor/DataProcessor.java` | 组件 | 高 | ColumnFilter |
| 11 | `DataInserter` | `component/processor/DataInserter.java` | 组件 | 高 | DataMapper |
| 12 | `ColumnFilter` | `component/processor/ColumnFilter.java` | 组件 | 中 | DataMapper |
| 13 | `HashChecker` | `component/processor/HashChecker.java` | 组件 | 中 | HashUtil |
| 14 | `KeywordProcessor` | `component/processor/KeywordProcessor.java` | 组件 | 低 | MySqlKeyword |
| 15 | `SyncPostProcessor` | `component/handler/SyncPostProcessor.java` | 组件 | 高 | DatabaseSyncHistoryService |
| 16 | `SyncExceptionHandler` | `component/handler/SyncExceptionHandler.java` | 组件 | 高 | 无 |
| 17 | `SyncConfigManager` | `common/config/SyncConfigManager.java` | 配置 | 中 | 无 |

### 🔄 需要重构的现有类

| 序号 | 类名 | 路径 | 重构内容 | 优先级 |
|------|------|------|----------|--------|
| 1 | `DataSyncServiceImpl` | `service/impl/DataSyncServiceImpl.java` | 主方法重构，依赖注入新组件 | 高 |

### 📋 创建顺序建议

#### 第一批：基础类 (无依赖)
1. `SyncContext.java` - 数据模型
2. `SyncStrategy.java` - 策略接口
3. `SyncConfigManager.java` - 配置管理
4. `SyncExceptionHandler.java` - 异常处理

#### 第二批：核心组件 (基础依赖)
5. `SyncPreChecker.java` - 前置检查
6. `SyncPreparer.java` - 同步准备
7. `SyncPostProcessor.java` - 后置处理
8. `DataExtractor.java` - 数据提取
9. `DataInserter.java` - 数据插入

#### 第三批：处理组件 (组合依赖)
10. `ColumnFilter.java` - 字段过滤
11. `HashChecker.java` - 哈希检查
12. `KeywordProcessor.java` - 关键字处理
13. `DataProcessor.java` - 数据处理

#### 第四批：策略和工厂 (复杂依赖)
14. `BatchSyncStrategy.java` - 批量策略
15. `SyncStrategyFactory.java` - 策略工厂
16. `DataSynchronizer.java` - 数据同步器

#### 第五批：主服务重构
17. `DataSyncServiceImpl.java` - 主服务重构

## 🛠️ 开发工具脚本

### 批量创建目录脚本 (Windows)
```batch
@echo off
set BASE_PATH=datasync-service\src\main\java\com\tjsj\sync\modules\sync

mkdir "%BASE_PATH%\component"
mkdir "%BASE_PATH%\component\checker"
mkdir "%BASE_PATH%\component\preparer"
mkdir "%BASE_PATH%\component\synchronizer"
mkdir "%BASE_PATH%\component\synchronizer\strategy"
mkdir "%BASE_PATH%\component\processor"
mkdir "%BASE_PATH%\component\handler"
mkdir "%BASE_PATH%\common\config"

echo 目录创建完成！
```

### 批量创建目录脚本 (Linux/Mac)
```bash
#!/bin/bash
BASE_PATH="datasync-service/src/main/java/com/tjsj/sync/modules/sync"

mkdir -p "$BASE_PATH/component/checker"
mkdir -p "$BASE_PATH/component/preparer"
mkdir -p "$BASE_PATH/component/synchronizer/strategy"
mkdir -p "$BASE_PATH/component/processor"
mkdir -p "$BASE_PATH/component/handler"
mkdir -p "$BASE_PATH/common/config"

echo "目录创建完成！"
```

## 🔍 代码检查清单

### Spring 配置检查
- [ ] 确保新包路径在 `@ComponentScan` 范围内
- [ ] 验证所有 `@Component`、`@Service` 注解正确
- [ ] 检查依赖注入 `@Autowired` 配置

### 包导入检查
- [ ] 更新所有相关类的 `import` 语句
- [ ] 检查循环依赖问题
- [ ] 验证包名与目录结构一致

### 测试配置检查
- [ ] 更新测试类的包路径
- [ ] 验证 Mock 对象配置
- [ ] 检查集成测试配置

## 📚 参考资料

### Java 包命名规范
- 包名全部小写
- 使用反向域名命名
- 避免使用Java关键字
- 包名应该反映功能职责

### Spring Boot 最佳实践
- 组件按功能分层组织
- 使用 `@ComponentScan` 自动扫描
- 避免循环依赖
- 合理使用 `@Qualifier` 注解

---
**文档版本**: v1.0
**创建时间**: 2025-01-27
**维护人员**: 开发团队
**状态**: ✅ 规范制定完成
