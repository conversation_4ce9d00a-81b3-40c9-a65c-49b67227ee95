<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>
    <packaging>pom</packaging>
    <groupId>com.tjsj</groupId>
    <artifactId>sync-common-pom</artifactId>
    <version>1.0</version>
    <name>sync-common-pom</name>
    <description>common-pom for Spring Boot</description>
    <properties>
        <!-- Java 基础配置 -->
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- Spring 相关依赖 -->
        <spring-cloud.version>2021.0.1</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.7.RELEASE</spring-cloud-alibaba.version>
        <spring-cloud-starter-openfeign.version>2.1.0.RELEASE</spring-cloud-starter-openfeign.version>

        <!-- 数据库相关依赖 -->
        <mysql.version>8.0.33</mysql.version>
        <mssql.version>4.0</mssql.version>
        <oracle.version>********</oracle.version>
        <mybatisplus.spring.boot.version>3.5.5</mybatisplus.spring.boot.version>
        <pagehelper-spring-boot-starter.version>1.4.7</pagehelper-spring-boot-starter.version>
        <tk-mybitis-mapper-spring-boot-starter.version>2.0.0</tk-mybitis-mapper-spring-boot-starter.version>

        <!-- 缓存与工具类依赖 -->
        <lombok.version>1.18.34</lombok.version>
        <caffeine.version>3.1.8</caffeine.version> <!-- 注意 caffeine 3.0 版本要求 JDK 11 -->
        <hutool-all.version>5.8.26</hutool-all.version>
        <kaptcha.version>0.0.9</kaptcha.version>
        <!-- Excel 处理库 -->
        <easyexcel.version>3.3.4</easyexcel.version>
        <feign-okhttp.version>13.3</feign-okhttp.version>
        <joda.time.version>2.12.7</joda.time.version>
        <jetbrains.annotations.version>24.1.0</jetbrains.annotations.version>

        <!--Maven相关配置-->
        <maven.compiler.plugin.version>3.13.0</maven.compiler.plugin.version>


        <!-- 日志相关依赖 -->
        <log4j.version>1.2.17</log4j.version>

        <!-- JSON 处理相关依赖 -->
        <gson.version>2.10.1</gson.version>
        <fastjson2.version>2.0.51</fastjson2.version>
        <fastjson2-extension.version>2.0.49</fastjson2-extension.version>
        <fastjson2-extension-spring5.version>2.0.51</fastjson2-extension-spring5.version>

        <!-- 安全与认证相关依赖 -->
        <jwt.version>0.7.0</jwt.version>
        <java.jwt.version>3.8.2</java.jwt.version>
        <!-- <shiro.version>1.11.0</shiro.version> --> <!-- 如果需要Shiro的话可以取消注释 -->

        <!-- 定时任务 -->
        <quartz.version>2.3.2</quartz.version>

        <!-- Apache Commons 库 -->
        <commons.lang.version>2.6</commons.lang.version>
        <commons.lang3.version>3.7</commons.lang3.version>
        <commons.fileupload.version>1.2.2</commons.fileupload.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.codec.version>1.10</commons.codec.version>
        <commons.configuration.version>1.10</commons.configuration.version>
        <commons-httpclient.version>3.1</commons-httpclient.version>

        <!-- MySQL 数据库连接池 -->
        <druid.version>1.2.21</druid.version> <!-- 注意这个版本曾在 2024-05-26 出现 IDEA 解析问题 -->

        <!-- Seata 分布式事务 -->
        <seata.version>1.7.1</seata.version>
    </properties>


    <dependencies>

        <!--springboot基础包相关-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.springframework.boot</groupId>-->
<!--                    <artifactId>spring-boot-starter-tomcat</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.bes.appserver</groupId>-->
<!--            <artifactId>bes-lite-spring-boot-2.x-starter</artifactId>-->
<!--            <version>9.5.5</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <!--mysql数据库相关包-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.11</version>
        </dependency>
        <dependency>
            <groupId>com.alipay.oceanbase</groupId>
            <artifactId>oceanbase-client</artifactId>
            <version>2.4.12</version>
        </dependency>
        <!--druid数据库连接池,作用：监控数据库连接池,防止数据库连接泄露,提高数据库连接的稳定性,
            提高数据库连接的效率,提高数据库连接的并发量,提高数据库连接的可用性,提高数据库连接的安全性,提高数据库连接的可靠性-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.5.1</version>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>${jetbrains.annotations.version}</version>
            <scope>compile</scope>
        </dependency>

        <!--mybatis-plus工具包-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatisplus.spring.boot.version}</version>
        </dependency>
        <!--lombok插件,简化代码,生成get/set方法,toString方法,log日志,注解等-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--导入pagehelper相关依赖-->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper-spring-boot-starter.version}</version>
        </dependency>
        <!--定时任务相关工具包-->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>${quartz.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.mchange</groupId>
                    <artifactId>c3p0</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--jwt插件 token的处理相关-->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>${java.jwt.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>${fastjson2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2-extension</artifactId>
            <version>${fastjson2-extension.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2-extension-spring5</artifactId>
            <version>${fastjson2-extension-spring5.version}</version>
        </dependency>

        <!--日期时间工具类包-->
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda.time.version}</version>
        </dependency>
        <!--其他相关commons包-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <!--最近更新时间是2005-10-15-->
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>${commons-httpclient.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons.lang.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>${commons.fileupload.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons.io.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons.codec.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>${commons.configuration.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool-all.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.16</version>
        </dependency>
        <!--easyexcel导出工具-->
        <!--无论哪个版本，org.apache.commons:commons-compress都vulnerable-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jdbc</artifactId>
        </dependency>
        <!--knife4j接口文档 swagger ui -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>3.0.3</version>
        </dependency>
        <!--redis相关包-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.14.0</version>
        </dependency>

        <!-- Seata 分布式事务 -->
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
            <version>${seata.version}</version>
        </dependency>

        <!-- Hessian序列化器（解决Seata警告） -->
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
            <version>4.0.66</version>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <!--作用：编译时添加参数,可以通过反射获取方法参数名-->
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <!--作用：编译时添加注解，而不是在运行时添加注解-->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*.yml</include>
                    <include>application-base.yml</include>
                    <include>application-base-${spring.active}.yml</include>
                    <include>application-${spring.active}.yml</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>

