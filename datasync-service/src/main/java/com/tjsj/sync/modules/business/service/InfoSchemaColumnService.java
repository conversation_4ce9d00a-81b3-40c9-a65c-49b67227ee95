package com.tjsj.sync.modules.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.sync.modules.business.model.InfoSchemaColumn;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/10/12 19:08
 * @description
 */

public interface InfoSchemaColumnService extends IService<InfoSchemaColumn> {


    /**
     * 获取表列
     *
     * @param schemaName 架构名称
     * @param tableName  表名称
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2024/10/12
     */
    List<String> getTableColumns(String schemaName, String tableName);

}
