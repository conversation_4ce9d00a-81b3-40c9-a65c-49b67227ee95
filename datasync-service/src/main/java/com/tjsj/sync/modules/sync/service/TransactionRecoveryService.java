package com.tjsj.sync.modules.sync.service;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * TransactionRecoveryService
 *
 * <AUTHOR>
 * @date 2025/08/03
 * @version 1.0.0
 * @description 事务恢复服务
 * 
 * <p>🛡️ 核心功能：</p>
 * <ul>
 *   <li><strong>事务状态跟踪</strong>：记录和跟踪分布式事务状态</li>
 *   <li><strong>悬挂事务检测</strong>：定期检测长时间未完成的事务</li>
 *   <li><strong>自动恢复机制</strong>：尝试恢复或清理异常事务</li>
 *   <li><strong>故障诊断</strong>：提供事务故障诊断信息</li>
 * </ul>
 */
@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "seata.enabled", havingValue = "true")
public class TransactionRecoveryService {

    @Value("${seata.degradation.auto-fallback-enabled:true}")
    private boolean autoFallbackEnabled;

    // 事务状态跟踪
    private final ConcurrentMap<String, TransactionInfo> activeTransactions = new ConcurrentHashMap<>();

    /**
     * 事务信息
     */
    @Getter
    public static class TransactionInfo {
        // Getters
        private final String xid;
        private final String tableName;
        private final LocalDateTime startTime;
        private final String status;

        public TransactionInfo(String xid, String tableName, String status) {
            this.xid = xid;
            this.tableName = tableName;
            this.startTime = LocalDateTime.now();
            this.status = status;
        }

        public long getDurationMinutes() {
            return java.time.Duration.between(startTime, LocalDateTime.now()).toMinutes();
        }
    }

    @PostConstruct
    public void init() {
        log.info("🛡️ 事务恢复服务初始化完成");
        log.info("🔧 自动降级: {}", autoFallbackEnabled ? "启用" : "禁用");
    }

    /**
     * 注册事务开始
     */
    public void registerTransactionStart(String xid, String tableName) {
        if (xid != null) {
            activeTransactions.put(xid, new TransactionInfo(xid, tableName, "ACTIVE"));
            log.debug("📝 注册事务开始 - XID: {}, 表: {}", xid, tableName);
        }
    }

    /**
     * 注册事务完成
     */
    public void registerTransactionComplete(String xid, boolean success) {
        if (xid != null) {
            TransactionInfo info = activeTransactions.remove(xid);
            if (info != null) {
                String status = success ? "COMMITTED" : "ROLLBACK";
                log.debug("📝 注册事务完成 - XID: {}, 状态: {}, 耗时: {}分钟", 
                         xid, status, info.getDurationMinutes());
            }
        }
    }

    /**
     * 定期检查悬挂事务
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void checkPendingTransactions() {
        if (activeTransactions.isEmpty()) {
            return;
        }

        log.info("🔍 开始检查悬挂事务，当前活跃事务数: {}", activeTransactions.size());

        LocalDateTime now = LocalDateTime.now();
        int timeoutCount = 0;
        int warningCount = 0;

        for (TransactionInfo info : activeTransactions.values()) {
            long durationMinutes = info.getDurationMinutes();

            if (durationMinutes > 10) { // 超过10分钟认为超时
                timeoutCount++;
                log.warn("⚠️ 发现超时事务 - XID: {}, 表: {}, 运行时间: {}分钟", 
                        info.getXid(), info.getTableName(), durationMinutes);
                
                // 尝试处理超时事务
                handleTimeoutTransaction(info);
                
            } else if (durationMinutes > 5) { // 超过5分钟给出警告
                warningCount++;
                log.warn("⏰ 长时间运行事务 - XID: {}, 表: {}, 运行时间: {}分钟", 
                        info.getXid(), info.getTableName(), durationMinutes);
            }
        }

        if (timeoutCount > 0 || warningCount > 0) {
            log.warn("🔍 悬挂事务检查完成 - 超时: {}, 警告: {}, 总计: {}", 
                    timeoutCount, warningCount, activeTransactions.size());
        } else {
            log.debug("🔍 悬挂事务检查完成 - 所有事务正常");
        }
    }

    /**
     * 处理超时事务
     */
    private void handleTimeoutTransaction(TransactionInfo info) {
        try {
            log.warn("🔄 尝试处理超时事务 - XID: {}", info.getXid());
            
            // 这里可以实现具体的超时事务处理逻辑
            // 例如：
            // 1. 尝试查询Seata Server中的事务状态
            // 2. 如果事务已经不存在，从本地移除
            // 3. 如果事务仍然存在但超时，尝试回滚
            
            // 暂时从活跃事务中移除（避免重复处理）
            activeTransactions.remove(info.getXid());
            
            log.warn("✅ 超时事务处理完成 - XID: {}", info.getXid());
            
        } catch (Exception e) {
            log.error("❌ 处理超时事务失败 - XID: {}, 错误: {}", info.getXid(), e.getMessage(), e);
        }
    }

    /**
     * 获取当前活跃事务信息
     */
    public String getActiveTransactionsInfo() {
        if (activeTransactions.isEmpty()) {
            return "当前无活跃事务";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("当前活跃事务 (").append(activeTransactions.size()).append("个):\n");
        
        for (TransactionInfo info : activeTransactions.values()) {
            sb.append(String.format("- XID: %s, 表: %s, 开始时间: %s, 运行时间: %d分钟\n",
                    info.getXid(),
                    info.getTableName(),
                    info.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    info.getDurationMinutes()));
        }
        
        return sb.toString();
    }

    /**
     * 清理所有活跃事务记录
     */
    public void clearActiveTransactions() {
        int count = activeTransactions.size();
        activeTransactions.clear();
        log.info("🧹 已清理所有活跃事务记录，共 {} 个", count);
    }

    /**
     * 获取活跃事务数量
     */
    public int getActiveTransactionCount() {
        return activeTransactions.size();
    }
}
