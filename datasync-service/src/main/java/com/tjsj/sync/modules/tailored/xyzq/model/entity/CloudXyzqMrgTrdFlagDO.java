package com.tjsj.sync.modules.tailored.xyzq.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * CloudXyzqMrgTrdFlagDO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 20:33
 * @description
 */
@Schema(description = "兴业证券官网-数据表爬虫采集标志")
@Data
@Accessors(chain = true)
@TableName("credit.t_xyzq_rzrq_flag")
public class CloudXyzqMrgTrdFlagDO implements Serializable {

	@Schema(description = "")
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	/**
	 * 日期
	 */
	@Schema(description = "日期")
	@TableField("date")
	private LocalDate date;

	/**
	 * 表名
	 */
	@Schema(description = "表名")
	@TableField("table_name")
	private String tableName;

	/**
	 * 标志
	 */
	@Schema(description = "标志")
	@TableField("flag")
	private Integer flag;

	/**
	 * 数据源
	 */
	@Schema(description = "数据源")
	@TableField("source")
	private String source;

	/**
	 * 创建时间
	 */
	@Schema(description = "创建时间")
	@TableField("create_time")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@Schema(description = "更新时间")
	@TableField("update_time")
	private LocalDateTime updateTime;

	@Serial
	private static final long serialVersionUID = 1L;


}