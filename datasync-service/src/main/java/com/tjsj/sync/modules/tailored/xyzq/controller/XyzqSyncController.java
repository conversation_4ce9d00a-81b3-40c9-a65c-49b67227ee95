package com.tjsj.sync.modules.tailored.xyzq.controller;

import com.tjsj.common.annotation.PassLogInfo;
import com.tjsj.common.annotation.SchedulerSwitchControl;
import com.tjsj.common.annotation.ServiceSwitchControl;
import com.tjsj.common.annotation.authorize.PassToken;
import com.tjsj.common.annotation.lock.SingleInstanceLock;
import com.tjsj.sync.modules.tailored.xyzq.service.XyzqSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * XyzqSyncController
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 21:26
 * @description 兴业证券-数据同步
 */
@Tag(name = "XyzqSyncController", description = "兴业证券-数据同步")
// 生效条件：tarkin.env=xyzq
@ConditionalOnProperty(name = "tarkin.env", havingValue = "xyzq")
@RestController
@RequestMapping("/xyzq")
@RequiredArgsConstructor
@Slf4j
public class XyzqSyncController {


    private final XyzqSyncService xyzqSyncService;

    /**
     * @description 兴业证券-数据同步
     */
    @Operation(summary = "同步官网数据")
    @GetMapping("/syncOfficialWebsiteData")
    // 默认每隔2分钟同步一次
    @Scheduled(cron = "${tarkin.scheduler.cron.sync-official-website-data:0 */2 * * * ?}")
    // 默认持有锁一小时，防止多次同步
    @SingleInstanceLock(leaseTime = 60)
    @PassLogInfo
    @PassToken
    @ServiceSwitchControl
    @SchedulerSwitchControl(value = "同步兴业证券官网数据", scheduled = true)
    public void syncTableData() {


        xyzqSyncService.syncOfficialWebsiteData();

    }


}
