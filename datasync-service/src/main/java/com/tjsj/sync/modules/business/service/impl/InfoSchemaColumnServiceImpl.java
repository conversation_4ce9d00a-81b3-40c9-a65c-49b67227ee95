package com.tjsj.sync.modules.business.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.sync.modules.business.mapper.InfoSchemaColumnMapper;
import com.tjsj.sync.modules.business.model.InfoSchemaColumn;
import com.tjsj.sync.modules.business.service.InfoSchemaColumnService;
import org.springframework.stereotype.Service;

import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * InfoSchemaColumnServiceImpl
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @date 2024/10/12 19:08
 * @description
 */
@Service
public class InfoSchemaColumnServiceImpl extends ServiceImpl<InfoSchemaColumnMapper, InfoSchemaColumn> implements InfoSchemaColumnService {

    @Override
    public List<String> getTableColumns(String schemaName, String tableName) {

        return this.list(Wrappers.<InfoSchemaColumn>lambdaQuery()
                        .eq(InfoSchemaColumn::getTableSchema, schemaName)
                        .eq(InfoSchemaColumn::getTableName, tableName))
                .stream()
                .map(InfoSchemaColumn::getColumnName)
                .collect(toList());
    }


}
