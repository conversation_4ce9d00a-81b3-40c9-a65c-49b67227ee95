package com.tjsj.sync.modules.tailored.xyzq.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.sync.modules.tailored.xyzq.mapper.CloudXyzqZslMapper;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqZslDO;
import com.tjsj.sync.modules.tailored.xyzq.service.CloudXyzqZslService;
/**
 * <AUTHOR>
 * @date 2025/5/19 21:48
 * @description
 * @version 1.0.0
 */

@Service
public class CloudXyzqZslServiceImpl extends ServiceImpl<CloudXyzqZslMapper, CloudXyzqZslDO> implements CloudXyzqZslService{

}
