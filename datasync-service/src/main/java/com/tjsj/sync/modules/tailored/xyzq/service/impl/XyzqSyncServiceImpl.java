package com.tjsj.sync.modules.tailored.xyzq.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import com.tjsj.sync.modules.tailored.xyzq.mapper.LocalXyzqZslMapper;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.*;
import com.tjsj.sync.modules.tailored.xyzq.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * XyzqSyncServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/20 10:04
 * @description 兴业证券-数据同步服务实现类
 */
@Service
@Slf4j
public class XyzqSyncServiceImpl implements XyzqSyncService {
    @Resource
    private CloudXyzqRzrqFlagService cloudXyzqRzrqFlagService;

    @Resource
    private CloudXyzqZslService cloudXyzqZslService;
    @Resource
    private LocalXyzqZslService localXyzqZslService;
    @Resource
    private CloudXyzqBdzqService cloudXyzqBdzqService;
    @Resource
    private LocalXyzqBdzqService localXyzqBdzqService;
    @Resource
    private LocalXyzqMrgTrdFlagService localXyzqMrgTrdFlagService;

    @Resource
    private CloudDataMapper cloudDataMapper;

    @Resource
    private LocalDataMapper localDataMapper;

    @Resource
    private LocalXyzqZslMapper localXyzqZslMapper;


    @Override
    public void syncOfficialWebsiteData() {

        // 250701更新：使用带有实体类的service和mapper进行插入操作会产生报错，报错日志记录在docs/xyzq目录下。
        // 所以暂时使用localDataMapper进行本地库插入操作

        // 获取本地采集标志表最新记录
        LocalXyzqMrgTrdFlagDO latestRecordId =
                Optional.ofNullable(localXyzqMrgTrdFlagService.getOne(Wrappers.<LocalXyzqMrgTrdFlagDO>lambdaQuery()
                                .orderByDesc(LocalXyzqMrgTrdFlagDO::getCreateTime)
                                .last("limit 1")))
                        .orElse(new LocalXyzqMrgTrdFlagDO());
        // 获取云端增量新数据
        List<CloudXyzqMrgTrdFlagDO> cloudXyzqMrgTrdFlagIncreList =
                cloudXyzqRzrqFlagService.list(Wrappers.<CloudXyzqMrgTrdFlagDO>lambdaQuery()
                        .gt(null != latestRecordId.getCreateTime(), CloudXyzqMrgTrdFlagDO::getCreateTime,
                                latestRecordId.getCreateTime()));
        // 如果云端没有增量数据，则直接返回
        if (cloudXyzqMrgTrdFlagIncreList.isEmpty()) {
            return;
        }

        // 获取当前日期的云端标的证券数据量
        long currentDataUnderlyingCount = cloudXyzqMrgTrdFlagIncreList.stream()
                .filter(cloudXyzqMrgTrdFlagDO ->
                        cloudXyzqMrgTrdFlagDO.getDate().equals(LocalDate.now()) &&
                                "t_rzrq_xyzq_bdzq".equals(cloudXyzqMrgTrdFlagDO.getTableName()))
                .count();
        // 如果有标的证券数据，则删除本地标的证券数据，并插入云端标的证券数据
        if (currentDataUnderlyingCount > 0) {

            List<CloudXyzqBdzqDO> currentDataBdzqList =
                    cloudXyzqBdzqService.list(Wrappers.<CloudXyzqBdzqDO>lambdaQuery()
                            .eq(CloudXyzqBdzqDO::getSseDate, LocalDate.now()));
            localXyzqBdzqService.remove(Wrappers.<LocalXyzqBdzqDO>lambdaQuery()
                    .eq(LocalXyzqBdzqDO::getSseDate, LocalDate.now()));

            if (!currentDataBdzqList.isEmpty()) {
//                localXyzqBdzqService.insertBatchSomeColumn(currentDataBdzqList.stream()
//                        .map(cloudXyzqBdzqDO ->
//                                BeanUtil.copyProperties(cloudXyzqBdzqDO, LocalXyzqBdzqDO.class))
//                        .toList());
                localDataMapper.insertBatchXyzqBdzq(currentDataBdzqList.stream()
                        .map(cloudXyzqBdzqDO ->
                                BeanUtil.copyProperties(cloudXyzqBdzqDO, LocalXyzqBdzqDO.class))
                        .toList());
            }
        }

        // 如果有折算率数据，则删除本地折算率数据，并插入云端折算率数据
        long currentDateCollateralCount = cloudXyzqMrgTrdFlagIncreList.stream()
                .filter(cloudXyzqMrgTrdFlagDO ->
                        cloudXyzqMrgTrdFlagDO.getDate().equals(LocalDate.now())
                                && "t_rzrq_xyzq_zsl".equals(cloudXyzqMrgTrdFlagDO.getTableName()))
                .count();
        if (currentDateCollateralCount > 0) {

            List<CloudXyzqZslDO> currentDataZslList =
                    cloudXyzqZslService.list(Wrappers.<CloudXyzqZslDO>lambdaQuery()
                            .eq(CloudXyzqZslDO::getSseDate, LocalDate.now()));
            localXyzqZslService.remove(Wrappers.<LocalXyzqZslDO>lambdaQuery()
                    .eq(LocalXyzqZslDO::getSseDate, LocalDate.now()));

            if (!currentDataZslList.isEmpty()) {
//                localXyzqZslService.insertBatchSomeColumn(currentDataZslList.stream()
//                        .map(cloudXyzqZslDO ->
//                                BeanUtil.copyProperties(cloudXyzqZslDO, LocalXyzqZslDO.class))
//                        .toList());
                localDataMapper.insertBatchXyzqZsl(currentDataZslList.stream()
                        .map(cloudXyzqZslDO ->
                                BeanUtil.copyProperties(cloudXyzqZslDO, LocalXyzqZslDO.class))
                        .toList());
            }

        }


        // 将采集标志表云端增量新数据插入本地数据库
        List<LocalXyzqMrgTrdFlagDO> localXyzqMrgTrdFlagIncreList = cloudXyzqMrgTrdFlagIncreList.stream()
                .map(cloudXyzqMrgTrdFlagDO ->
                        BeanUtil.copyProperties(cloudXyzqMrgTrdFlagDO, LocalXyzqMrgTrdFlagDO.class))
                .toList();
        List<Integer> increRecordIdList = localXyzqMrgTrdFlagIncreList.stream().map(LocalXyzqMrgTrdFlagDO::getId)
                .toList();
//        localXyzqMrgTrdFlagService.removeByIds(increRecordIdList);
//        localXyzqMrgTrdFlagService.insertBatchSomeColumn(localXyzqMrgTrdFlagIncreList);
        localDataMapper.removeXyzqMrgTrdFlagByIds(increRecordIdList);
        localDataMapper.insertBatchXyzqMrgTrdFlag(localXyzqMrgTrdFlagIncreList);

    }


}
