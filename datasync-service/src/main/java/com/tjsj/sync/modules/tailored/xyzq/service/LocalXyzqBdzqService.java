package com.tjsj.sync.modules.tailored.xyzq.service;

import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqBdzqDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 21:17
 * @description
 */

public interface LocalXyzqBdzqService extends IService<LocalXyzqBdzqDO> {

	/**
	 * 批量插入
	 *
	 * @param list 列表
	 * <AUTHOR>
	 * @date 2025/05/20
	 */
	void insertBatchSomeColumn(List<LocalXyzqBdzqDO> list);

}
