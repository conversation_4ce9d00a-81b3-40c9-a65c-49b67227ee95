package com.tjsj.sync.modules.sync.component.processor;

import cn.hutool.core.collection.CollUtil;
import com.tjsj.sync.modules.sync.utils.MySqlKeyword;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * KeywordProcessor
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 关键字处理器
 * <p>负责处理数据库字段名中的MySQL关键字，避免SQL语法错误</p>
 */
@Component
@Schema(description = "关键字处理器")
@Slf4j
@RequiredArgsConstructor
public class KeywordProcessor {

    /**
     * 处理数据列表中的MySQL关键字
     * <p>将字段名为MySQL关键字的字段用反引号包裹，避免SQL语法错误</p>
     *
     * @param dataList 待处理的数据列表
     * @return 处理后的数据列表
     * <AUTHOR>
     * @date 2025/08/01
     */
    public List<Map<String, Object>> processKeywords(List<Map<String, Object>> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            log.debug("📭 数据列表为空，跳过关键字处理");
            return dataList;
        }

        log.debug("🔧 开始处理MySQL关键字 - 数据量: {} 条", dataList.size());

        List<Map<String, Object>> processedList = dataList.stream()
                .map(this::processKeywords)
                .collect(Collectors.toList());

        log.debug("✅ MySQL关键字处理完成 - 数据量: {} 条", processedList.size());

        return processedList;
    }

    /**
     * 处理单条记录中的MySQL关键字
     * <p>将字段名为MySQL关键字的字段用反引号包裹</p>
     *
     * @param record 待处理的记录
     * @return 处理后的记录
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public Map<String, Object> processKeywords(Map<String, Object> record) {
        if (CollUtil.isEmpty(record)) {
            return record;
        }

        return MySqlKeyword.processKeywords(record);
    }

}
