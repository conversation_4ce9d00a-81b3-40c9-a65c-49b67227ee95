package com.tjsj.sync.modules.sync.component.processor;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * DataPipeline
 *
 * <AUTHOR> Ye
 * @date 2025/08/01
 * @version 1.0.0
 * @description 数据管道处理流水线
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DataPipeline {

    private final LocalDataMapper localDataMapper;
    private final CloudDataMapper cloudDataMapper;
    private final KeywordProcessor keywordProcessor;


    /**
     * 数据处理流水线
     *
     * @param context 同步上下文
     * @param dataList 数据列表
     * @return 处理后的数据列表
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public List<Map<String, Object>> process(SyncContext context, List<Map<String, Object>> dataList) {

        if (CollUtil.isEmpty(dataList)) {
            return dataList;
        }

        log.debug("🔄 数据流水线处理 - 表: {}, 数据量: {} 条", context.getFullTableName(), dataList.size());

        List<Map<String, Object>> result = dataList;

        // 1. 过滤表字段
        filterTableColumns(context, result);

        // 2. 过滤数据字段
        filterDataColumns(context, result);

        // 3. 处理关键字
        result = keywordProcessor.processKeywords(result);

        log.debug("✅ 数据流水线处理完成 - 表: {}, 处理后数量: {} 条",
                context.getFullTableName(), result.size());

        return result;
    }

    /**
     * 过滤数据字段
     * <p>根据配置的包含/排除规则过滤数据字段</p>
     * <p>处理优先级：包含字段(白名单) -> 排除字段(黑名单)</p>
     * <p><strong>注意：此方法会直接修改传入的数据列表</strong></p>
     *
     * @param context 同步上下文，包含表配置信息
     * @param dataList 待过滤的数据列表（会被直接修改）
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    private void filterDataColumns(SyncContext context, List<Map<String, Object>> dataList) {
        // TODO: 250724 处理数据的createTime或者updateTime字段值为0000-00-00 00:00:00的情况，不过暂时不用处理

        SyncTableConfigDO tableConfig = context.getSyncTableConfig();

        if (tableConfig == null || CollUtil.isEmpty(dataList)) {
            return;
        }

        String includeColumn = tableConfig.getIncludeColumn();
        String excludeColumn = tableConfig.getExcludeColumn();

        // 如果没有配置过滤规则，直接返回
        if (StrUtil.isEmpty(includeColumn) && StrUtil.isEmpty(excludeColumn)) {
            log.debug("⏭️ 无过滤规则配置 - 表: {}, 跳过字段过滤", context.getFullTableName());
            return;
        }

        log.debug("🔍 开始过滤数据字段 - 表: {}, 包含字段: {}, 排除字段: {}",
                context.getFullTableName(), includeColumn, excludeColumn);

        // 保留指定列，包含字段优先级低于排除字段，用，分隔
        if (StrUtil.isNotEmpty(includeColumn)) {
            List<String> includeColumnList = Arrays.stream(includeColumn.split(","))
                    .map(String::trim)
                    .toList();
            dataList.forEach(data -> data.keySet().retainAll(includeColumnList));

            log.debug("📋 应用包含字段过滤 - 表: {}, 字段: {}", context.getFullTableName(), includeColumn);
        }

        // 排除指定列，排除字段优先级高于包含字段，用，分隔，如：“id,updateTime”
        if (StrUtil.isNotEmpty(excludeColumn)) {
            List<String> excludeColumnList = Arrays.stream(excludeColumn.split(","))
                    .map(String::trim)
                    .toList();
            dataList.forEach(data -> excludeColumnList.forEach(data::remove));

            log.debug("🚫 应用排除字段过滤 - 表: {}, 字段: {}", context.getFullTableName(), excludeColumn);
        }
    }


    /**
     * 过滤表字段
     * <p>过滤数据源表中存在，但是目标表不存在的字段</p>
     *
     * @param context 同步上下文
     * @param dataList 数据列表
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public void filterTableColumns(SyncContext context, List<Map<String, Object>> dataList) {
        // 从上下文中获取表配置和同步历史记录
        SyncTableConfigDO tableConfig = context.getSyncTableConfig();
        DatabaseSyncHistoryDO syncHistoryRecord = context.getSyncHistory();

        if (tableConfig == null) {
            log.warn("⚠️ 表配置为空，跳过字段过滤");
            return;
        }

        // 将所有表字段转换为小写
        String dataSchemaName = tableConfig.getDataSchemaName();
        String dataTableName = tableConfig.getDataTableName();
        Set<String> cloudTableColumns = new HashSet<>(cloudDataMapper.listTableColumns(dataSchemaName, dataTableName));
        cloudTableColumns = cloudTableColumns.stream().map(String::toLowerCase).collect(Collectors.toSet());

        String schemaName = tableConfig.getSchemaName();
        String tableName = tableConfig.getTableName();
        Set<String> localTableColumns = new HashSet<>(localDataMapper.listTableColumns(schemaName, tableName));
        localTableColumns = localTableColumns.stream().map(String::toLowerCase).collect(Collectors.toSet());

        // 计算出数据源表存在，但是目标表不存在的字段
        cloudTableColumns.removeAll(localTableColumns);
        if (CollUtil.isEmpty(cloudTableColumns)) {
            return;
        }

        // 将字段列表拼接成逗号分隔的字符串
        String columnsString = CollUtil.join(cloudTableColumns, ",");
        // 拼接最终 remark 字符串
        String remark = StrUtil.format("数据源表存在，但是目标表不存在的字段：{}", columnsString);

        log.debug("🗑️ 移除不存在于目标表的字段 - 表: {}, 字段: {}", context.getFullTableName(), columnsString);

        if (syncHistoryRecord != null) {
            syncHistoryRecord.setRemark(remark);
        }

        // 过滤 dataList，将不存在于目标表的字段删除
        Set<String> finalCloudTableColumns = cloudTableColumns;
        dataList.forEach(data -> finalCloudTableColumns.forEach(data::remove));

    }


}
