package com.tjsj.sync.modules.tailored.xyzq.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.base.mapper.IBaseMapper;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqBdzqDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * LocalXyzqBdzqMapper
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 21:17
 * @description
 */
@Mapper
@DS(DataSourceNames.TARGET_DB)
public interface LocalXyzqBdzqMapper extends IBaseMapper<LocalXyzqBdzqDO> {


    /**
     * 插入批量一些列
     *
     * @param list 列表
     *
     * <AUTHOR>
     * @date 2025/07/01
     */
    void insertBatch(@Param("list") List<LocalXyzqBdzqDO> list);
}