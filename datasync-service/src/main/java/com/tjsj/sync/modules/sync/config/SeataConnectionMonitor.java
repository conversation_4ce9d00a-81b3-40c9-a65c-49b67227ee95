package com.tjsj.sync.modules.sync.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * SeataConnectionMonitor
 *
 * <AUTHOR>
 * @date 2025/08/03
 * @version 1.0.0
 * @description Seata连接状态监控器
 * 
 * <p>🎯 核心功能：</p>
 * <ul>
 *   <li><strong>连接检测</strong>：实时检测Seata Server连接状态</li>
 *   <li><strong>状态缓存</strong>：缓存连接状态，避免频繁检测</li>
 *   <li><strong>降级支持</strong>：为事务降级提供状态判断</li>
 * </ul>
 */
@Component
@Slf4j
public class SeataConnectionMonitor {

    @Value("${seata.service.grouplist.default:127.0.0.1:8091}")
    private String seataServerAddress;

    @Value("${seata.enabled:false}")
    private boolean seataEnabled;

    // 从配置文件读取降级配置
    @Value("${seata.degradation.auto-fallback-enabled:true}")
    private boolean autoFallbackEnabled;

    @Value("${seata.degradation.connection-check-interval:5}")
    private int connectionCheckInterval;

    @Value("${seata.degradation.connection-timeout:3}")
    private int connectionTimeout;

    // 连接状态缓存
    private final AtomicBoolean isConnected = new AtomicBoolean(false);

    // 上次检测时间
    private volatile long lastCheckTime = 0;

    /**
     * 检查Seata Server是否可用
     *
     * @return true: 可用，false: 不可用
     */
    public boolean isSeataServerAvailable() {
        if (!seataEnabled) {
            return false;
        }

        // 如果禁用了自动降级，总是返回true（让Seata自己处理连接问题）
        if (!autoFallbackEnabled) {
            log.debug("🔧 自动降级已禁用，跳过连接检查");
            return true;
        }

        long currentTime = System.currentTimeMillis();
        long checkIntervalMs = connectionCheckInterval * 1000L; // 转换为毫秒

        // 如果距离上次检测时间小于间隔，直接返回缓存结果
        if (currentTime - lastCheckTime < checkIntervalMs) {
            return isConnected.get();
        }

        // 执行连接检测
        boolean available = checkConnection();
        isConnected.set(available);
        lastCheckTime = currentTime;

        if (available) {
            log.debug("✅ Seata Server连接正常 - {}", seataServerAddress);
        } else {
            log.warn("❌ Seata Server连接失败 - {}", seataServerAddress);
        }

        return available;
    }

    /**
     * 强制刷新连接状态
     * 
     * @return true: 可用，false: 不可用
     */
    public boolean forceRefreshStatus() {
        lastCheckTime = 0; // 重置检测时间，强制重新检测
        return isSeataServerAvailable();
    }

    /**
     * 获取当前缓存的连接状态
     * 
     * @return true: 可用，false: 不可用
     */
    public boolean getCachedStatus() {
        return isConnected.get();
    }

    /**
     * 执行实际的连接检测
     * 
     * @return true: 连接成功，false: 连接失败
     */
    private boolean checkConnection() {
        try {
            String[] parts = seataServerAddress.split(":");
            if (parts.length != 2) {
                log.error("❌ Seata Server地址格式错误: {}", seataServerAddress);
                return false;
            }

            String host = parts[0];
            int port = Integer.parseInt(parts[1]);

            int timeoutMs = connectionTimeout * 1000; // 转换为毫秒
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(host, port), timeoutMs);
                return true;
            }

        } catch (IOException e) {
            log.debug("🔍 Seata Server连接检测失败: {}", e.getMessage());
            return false;
        } catch (NumberFormatException e) {
            log.error("❌ Seata Server端口格式错误: {}", seataServerAddress);
            return false;
        } catch (Exception e) {
            log.error("❌ Seata Server连接检测异常", e);
            return false;
        }
    }

    /**
     * 获取连接状态描述
     *
     * @return 状态描述字符串
     */
    public String getStatusDescription() {
        if (!seataEnabled) {
            return "Seata未启用";
        }

        if (!autoFallbackEnabled) {
            return String.format("Seata Server: %s, 自动降级: 已禁用", seataServerAddress);
        }

        boolean status = getCachedStatus();
        long timeSinceLastCheck = System.currentTimeMillis() - lastCheckTime;

        return String.format("Seata Server: %s, 地址: %s, 上次检测: %d秒前, 检测间隔: %d秒, 超时: %d秒",
                status ? "可用" : "不可用",
                seataServerAddress,
                timeSinceLastCheck / 1000,
                connectionCheckInterval,
                connectionTimeout);
    }

    /**
     * 获取降级配置信息
     *
     * @return 配置信息
     */
    public String getDegradationConfig() {
        return String.format("自动降级: %s, 检测间隔: %d秒, 连接超时: %d秒",
                autoFallbackEnabled ? "启用" : "禁用",
                connectionCheckInterval,
                connectionTimeout);
    }

    /**
     * 是否启用自动降级
     *
     * @return true: 启用，false: 禁用
     */
    public boolean isAutoFallbackEnabled() {
        return autoFallbackEnabled;
    }
}
