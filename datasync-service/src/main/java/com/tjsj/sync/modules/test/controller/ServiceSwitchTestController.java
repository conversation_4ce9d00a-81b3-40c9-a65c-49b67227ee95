package com.tjsj.sync.modules.test.controller;

import com.tjsj.common.annotation.SchedulerSwitchControl;
import com.tjsj.common.annotation.ServiceSwitchControl;
import com.tjsj.common.manager.ServiceSwitchManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * ServiceSwitchTestController
 *
 * <AUTHOR>
 * @date 2025/06/30
 * @description 服务开关测试控制器 - 用于演示服务开关控制功能
 */
@RestController
@RequestMapping("/test/service-switch")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "服务开关测试", description = "用于测试服务开关控制功能的接口")
public class ServiceSwitchTestController {

    private final ServiceSwitchManager serviceSwitchManager;

    @Operation(summary = "测试受服务开关控制的方法")
    @GetMapping("/controlled-method")
    @ServiceSwitchControl("测试受控方法")
    public Map<String, Object> testControlledMethod() {
        log.info("执行受服务开关控制的方法");

        Map<String, Object> result = new HashMap<>();
        result.put("message", "方法执行成功");
        result.put("timestamp", LocalDateTime.now());
        result.put("serviceStatus", serviceSwitchManager.getCurrentServiceStatus().getDescription());

        return result;
    }

    @Operation(summary = "测试不受服务开关控制的方法")
    @GetMapping("/uncontrolled-method")
    public Map<String, Object> testUncontrolledMethod() {
        log.info("执行不受服务开关控制的方法");

        Map<String, Object> result = new HashMap<>();
        result.put("message", "方法执行成功（不受服务开关控制）");
        result.put("timestamp", LocalDateTime.now());
        result.put("serviceStatus", serviceSwitchManager.getCurrentServiceStatus().getDescription());

        return result;
    }

    @Operation(summary = "获取当前服务开关状态")
    @GetMapping("/status")
    public Map<String, Object> getServiceStatus() {
        Map<String, Object> result = new HashMap<>();
        result.put("currentServiceStatus", serviceSwitchManager.getCurrentServiceStatus());
        result.put("currentSchedulerStatus", serviceSwitchManager.getCurrentSchedulerStatus());
        result.put("isServiceAvailable", serviceSwitchManager.isServiceAvailable());
        result.put("isServiceDisabled", serviceSwitchManager.isServiceDisabled());
        result.put("isSchedulerAvailable", serviceSwitchManager.isSchedulerAvailable());
        result.put("isSchedulerDisabled", serviceSwitchManager.isSchedulerDisabled());
        result.put("timestamp", LocalDateTime.now());

        return result;
    }

    @Operation(summary = "测试返回不同类型的受控方法")
    @GetMapping("/controlled-method-string")
    @ServiceSwitchControl("测试返回字符串的受控方法")
    public String testControlledMethodString() {
        log.info("执行返回字符串的受控方法");
        return "方法执行成功，返回字符串";
    }

    @Operation(summary = "测试返回布尔值的受控方法")
    @GetMapping("/controlled-method-boolean")
    @ServiceSwitchControl("测试返回布尔值的受控方法")
    public Boolean testControlledMethodBoolean() {
        log.info("执行返回布尔值的受控方法");
        return true;
    }

    @Operation(summary = "测试无返回值的受控方法")
    @GetMapping("/controlled-method-void")
    @ServiceSwitchControl("测试无返回值的受控方法")
    public void testControlledMethodVoid() {
        log.info("执行无返回值的受控方法");
    }

    // ==================== 定时任务开关测试接口 ====================

    @Operation(summary = "测试受定时任务开关控制的方法")
    @GetMapping("/scheduler-controlled-method")
    @SchedulerSwitchControl(value = "测试受定时任务开关控制的方法", scheduled = true)
    public Map<String, Object> testSchedulerControlledMethod() {
        log.info("执行受定时任务开关控制的方法");

        Map<String, Object> result = new HashMap<>();
        result.put("message", "定时任务方法执行成功");
        result.put("timestamp", LocalDateTime.now());
        result.put("schedulerStatus", serviceSwitchManager.getCurrentSchedulerStatus().getDescription());

        return result;
    }

    @Operation(summary = "测试返回字符串的受定时任务开关控制方法")
    @GetMapping("/scheduler-controlled-method-string")
    @SchedulerSwitchControl("测试返回字符串的受定时任务开关控制方法")
    public String testSchedulerControlledMethodString() {
        log.info("执行返回字符串的受定时任务开关控制方法");
        return "定时任务方法执行成功，返回字符串";
    }

    @Operation(summary = "测试返回布尔值的受定时任务开关控制方法")
    @GetMapping("/scheduler-controlled-method-boolean")
    @SchedulerSwitchControl("测试返回布尔值的受定时任务开关控制方法")
    public Boolean testSchedulerControlledMethodBoolean() {
        log.info("执行返回布尔值的受定时任务开关控制方法");
        return true;
    }

    @Operation(summary = "测试无返回值的受定时任务开关控制方法")
    @GetMapping("/scheduler-controlled-method-void")
    @SchedulerSwitchControl("测试无返回值的受定时任务开关控制方法")
    public void testSchedulerControlledMethodVoid() {
        log.info("执行无返回值的受定时任务开关控制方法");
    }

    @Operation(summary = "测试不受定时任务开关控制的方法")
    @GetMapping("/scheduler-uncontrolled-method")
    public Map<String, Object> testSchedulerUncontrolledMethod() {
        log.info("执行不受定时任务开关控制的方法");

        Map<String, Object> result = new HashMap<>();
        result.put("message", "方法执行成功（不受定时任务开关控制）");
        result.put("timestamp", LocalDateTime.now());
        result.put("schedulerStatus", serviceSwitchManager.getCurrentSchedulerStatus().getDescription());

        return result;
    }
}
