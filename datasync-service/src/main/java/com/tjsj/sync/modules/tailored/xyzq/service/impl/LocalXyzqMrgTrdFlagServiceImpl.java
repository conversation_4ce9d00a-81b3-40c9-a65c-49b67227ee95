package com.tjsj.sync.modules.tailored.xyzq.service.impl;

import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.sync.modules.tailored.xyzq.mapper.LocalXyzqMrgTrdFlagMapper;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqMrgTrdFlagDO;
import com.tjsj.sync.modules.tailored.xyzq.service.LocalXyzqMrgTrdFlagService;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 20:46
 * @description
 */

@Service
public class LocalXyzqMrgTrdFlagServiceImpl extends ServiceImpl<LocalXyzqMrgTrdFlagMapper, LocalXyzqMrgTrdFlagDO>
		implements LocalXyzqMrgTrdFlagService {

	@Override
	public void insertBatchSomeColumn(List<LocalXyzqMrgTrdFlagDO> localXyzqMrgTrdFlagIncreList) {

		this.baseMapper.insertBatch(localXyzqMrgTrdFlagIncreList);
	}

}
