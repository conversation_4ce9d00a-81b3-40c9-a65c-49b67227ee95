package com.tjsj.sync.modules.sync.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * SeataConfigValidator
 *
 * <AUTHOR>
 * @date 2025/08/03
 * @version 1.0.0
 * @description Seata配置验证器 - 用于调试配置加载问题
 */
@Component
@Slf4j
public class SeataConfigValidator {

    @Value("${seata.enabled:false}")
    private String seataEnabled;

    @Value("${seata.application-id:NOT_SET}")
    private String applicationId;

    @Value("${seata.tx-service-group:NOT_SET}")
    private String txServiceGroup;

    @Resource
    private Environment environment;

    /**
     * 应用启动完成后验证Seata配置
     */
    @EventListener(ApplicationReadyEvent.class)
    public void validateSeataConfig(ApplicationReadyEvent event) {
        log.info("🔍 ==================== Seata配置验证开始 ====================");

        // 检查活跃的profile
        String[] activeProfiles = environment.getActiveProfiles();
        log.info("📋 当前活跃的Profile: {}", String.join(", ", activeProfiles));
        
        // 检查Seata相关配置
        log.info("🔧 seata.enabled = {}", seataEnabled);
        log.info("📋 seata.application-id = {}", applicationId);
        log.info("🔗 seata.tx-service-group = {}", txServiceGroup);
        
        // 检查是否包含seata profile
        boolean hasSeataProfile = false;
        for (String profile : activeProfiles) {
            if ("seata".equals(profile)) {
                hasSeataProfile = true;
                break;
            }
        }
        
        if (!hasSeataProfile) {
            log.warn("⚠️ 未发现 'seata' profile，application-seata.yml 可能未被加载");
            log.warn("💡 请检查 application.yml 中的 spring.profiles.active 配置");
        }
        
        // 检查Seata是否启用
        if ("true".equals(seataEnabled)) {
            log.info("✅ Seata分布式事务已启用");
            log.info("🎯 配置模式: Spring Boot YAML配置");
            log.info("🌐 分布式事务功能可用，应该能看到SeataConfig的初始化日志");
        } else if ("false".equals(seataEnabled)) {
            log.warn("⚠️ Seata已禁用 (seata.enabled=false)");
            log.warn("💡 如需启用分布式事务，请设置 seata.enabled=true");
        } else {
            log.error("❌ Seata配置未找到");
            log.error("💡 请检查 application.yml 中的 seata 配置");
        }

        log.info("🔍 ==================== Seata配置验证完成 ====================");
    }

}
