package com.tjsj.sync.modules.sync.utils.constants;

import org.springframework.stereotype.Component;

/**
 * SyncConsts
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/11/14
 * @description 同步常量
 */
@Component
public class SyncConsts {

    /**
     * 同步数据库日志类型-自动同步
     */
    public static final String AUTO_SYNC = "auto_sync";

    /**
     * 同步数据库类型-自动同步测试
     */
    public static final String AUTO_SYNC_TEST = "auto_sync_test";

    /**
     * 同步数据库日志类型-手动同步
     */
    public static final String MANUAL_SYNC = "manual_sync";


    /**
     * 自动同步-从云数据库取出每批量大小
     */
    public final static Integer AUTO_PULL_BATCH_SIZE = 10000;

    /**
     * 自动同步-数据同步时插入数据量每批量大小
     */
    public final static Integer AUTO_INSERT_BATCH_SIZE = 10000;

    /**
     * 更新时间-表字段
     */
    public static final String UPDATE_TIME_FIELD = "update_time";


    /**
     * 财务相关表-库名
     */
    public static final String SCHEMA_PLEDGEDATA = "pledgedata";

    /**
     * 财务相关表-表名前缀
     */
    public static final String FINANCE_TABLE_PREFIX = "t_financial";



}
