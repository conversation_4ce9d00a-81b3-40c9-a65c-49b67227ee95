package com.tjsj.sync.modules.sync.common.config;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.transaction.ChainedTransactionManager;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * TransactionManagerConfig
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/02/09
 * @description 交易经理配置
 */
//@Configuration
//@EnableTransactionManagement
//public class TransactionManagerConfig {
//
//    /**
//     * 创建 `source` 数据源 Bean
//     */
//    @Bean(name = "sourceDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource.source")
//    public DataSource sourceDataSource() {
//        return new DynamicRoutingDataSource();
//    }
//
//    /**
//     * 创建 `target` 数据源 Bean
//     */
//    @Bean(name = "targetDataSource")
//    @ConfigurationProperties(prefix = "spring.datasource.dynamic.datasource.target")
//    public DataSource targetDataSource() {
//        return new DynamicRoutingDataSource();
//    }
//
//    /**
//     * 配置 `source` 数据源的事务管理器
//     */
//    @Bean(name = "sourceTransactionManager")
//    @ConditionalOnBean(name = "sourceDataSource")
//    public DataSourceTransactionManager sourceTransactionManager(
//            @Qualifier("sourceDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    /**
//     * 配置 `target` 数据源的事务管理器
//     */
//    @Bean(name = "targetTransactionManager")
//    @ConditionalOnBean(name = "targetDataSource")
//    public DataSourceTransactionManager targetTransactionManager(
//            @Qualifier("targetDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    /**
//     * 配置 `ChainedTransactionManager` 以支持多数据源事务
//     */
//    @Bean(name = "chainedTransactionManager")
//    public PlatformTransactionManager chainedTransactionManager(
//            @Qualifier("sourceTransactionManager") DataSourceTransactionManager sourceTransactionManager,
//            @Qualifier("targetTransactionManager") DataSourceTransactionManager targetTransactionManager) {
//        return new ChainedTransactionManager(sourceTransactionManager, targetTransactionManager);
//    }
//}
