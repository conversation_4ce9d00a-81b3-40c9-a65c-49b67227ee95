package com.tjsj.sync.modules.tailored.xyzq.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqZslDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * CloudXyzqZslMapper
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description
 * @date 2025/5/19 21:48
 */
@Mapper
@DS(DataSourceNames.SOURCE_DB)
public interface CloudXyzqZslMapper extends BaseMapper<CloudXyzqZslDO> {
}