package com.tjsj.sync.modules.sync.controller;

import cn.hutool.core.thread.ThreadUtil;
import com.tjsj.common.annotation.PassLogInfo;
import com.tjsj.common.annotation.SchedulerSwitchControl;
import com.tjsj.common.annotation.ServiceSwitchControl;
import com.tjsj.common.annotation.authorize.PassToken;
import com.tjsj.common.annotation.lock.SingleInstanceLock;
import com.tjsj.sync.modules.business.service.BusinessSyncService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * BusinessSyncController
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/4/8 13:36
 * @description 业务数据同步
 */
@Tag(name = "BusinessSyncController", description = "业务数据同步")
@RestController
@RequestMapping("businessSync")
@RequiredArgsConstructor
@Slf4j
public class BusinessSyncController {

    private final BusinessSyncService businessSyncService;


    /**
     * @description 业务数据同步
     */
    @Operation(summary = "同步修复数据")
    @GetMapping("/syncFixData")
    @Scheduled(cron = "${tarkin.scheduler.cron.sync-fix-data:0 */2 * * * ?}")
    // 默认持有锁一小时，防止多次同步
    @SingleInstanceLock(leaseTime = 60)
    @PassToken
    @PassLogInfo
    @ServiceSwitchControl
    @SchedulerSwitchControl(scheduled = true)
    public void syncFixData() {

        businessSyncService.syncFixData();
    }


}
