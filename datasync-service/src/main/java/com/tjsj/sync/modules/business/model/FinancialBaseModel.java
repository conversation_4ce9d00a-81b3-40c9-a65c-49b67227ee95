package com.tjsj.sync.modules.business.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.Alias;

import java.time.LocalDateTime;

/**
 * FinancialBaseModel
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/1/5 12:32
 * @description 财务数据表标准模型
 */
@Data
@Accessors(chain = true)
@Alias(value = "FinancialBaseModel")
@Schema(description = "财务数据表标准模型")
@FieldNameConstants
public class FinancialBaseModel {

    /**
     * 主键ID
     */
    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    /**
     * 股票代码
     */
    @Schema(description = "股票代码")
    @TableField(value = "StockId")
    private String stockId;

    /**
     * 日期
     */
    @Schema(description = "日期")
    @TableField(value = "Date")
    private String date;

    /**
     * 数据排序
     */
    @Schema(description = "数据排序")
    @TableField(value = "Number")
    private Integer number;

    /**
     * 财务名称
     */
    @Schema(description = "财务名称")
    @TableField(value = "Name")
    private String name;

    /**
     * 财务对应值
     */
    @Schema(description = "财务对应值")
    @TableField(value = "Value")
    private String value;

    /**
     * 字段名称
     */
    @Schema(description = "字段名称")
    @TableField(value = "FiledName")
    private String filedName;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(value = "hash")
    private String hash;
}
