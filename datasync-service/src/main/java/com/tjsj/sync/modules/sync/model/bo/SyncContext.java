package com.tjsj.sync.modules.sync.model.bo;


import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.utils.SyncConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * SyncContext
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步上下文
 * <p>封装同步过程中的所有状态信息，便于在各个组件间传递</p>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncContext {

    /** 任务ID */
    private Integer taskId;

    /** 同步表配置 */
    private SyncTableConfigDO syncTableConfig;

    /** 是否插入数据 */
    private Boolean ifInsert;

    /** 同步类型 */
    private SyncTypeEnum syncTypeEnum;

    /** 同步历史记录 */
    private DatabaseSyncHistoryDO syncHistory;

    /** 同步开始时间 */
    private long startTime;

    /** 同步数据总数 */
    private int totalDataNum;

    /** 错误信息 */
    private String errorMessage;

    /** 同步配置缓存 */
    private SyncConfig syncConfig;

    // ═══════════════════════════════════════════════════════════════
    // 🔧 辅助方法
    // ═══════════════════════════════════════════════════════════════

    /**
     * 获取表名
     */
    public String getFullTableName() {
        if (syncTableConfig == null) {
            return "unknown";
        }
        return syncTableConfig.getSchemaName() + "." + syncTableConfig.getTableName();
    }


    /**
     * 获取任务类型
     */
    public SyncTaskTypeEnum getTaskType() {
        return syncTableConfig != null ? syncTableConfig.getTaskType() : null;
    }

    /**
     * 是否为全量同步
     */
    public boolean isFullUpdate() {
        return syncTableConfig != null &&
                syncTableConfig.getIfFullUpdate().equals(CommonStatus.ENABLE) &&
                Boolean.TRUE.equals(ifInsert);
    }

    /**
     * 是否需要比较最大时间
     */
    public boolean needCompareMaxTime() {
        return syncTableConfig != null &&
                syncTableConfig.getIfCompareMaxTime() == CommonStatus.ENABLE;
    }

    /**
     * 是否为Quartz同步
     */
    public boolean isQuartzSync() {
        return Objects.equals(syncTypeEnum, SyncTypeEnum.QUARTZ);
    }

    /**
     * 累加同步数据数量
     */
    public void addDataNum(int count) {
        this.totalDataNum += count;
    }

    /**
     * 获取同步耗时（毫秒）
     */
    public long getDuration() {
        return System.currentTimeMillis() - startTime;
    }


    /**
     * 构建同步上下文
     *
     * @param taskId 任务id
     * @param syncTableConfig 同步表配置
     * @param ifInsert 是否插入数据
     * @param syncTypeEnum 同步类型枚举
     * @return {@link SyncContext }
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public static SyncContext build(Integer taskId, SyncTableConfigDO syncTableConfig,
                                    Boolean ifInsert, SyncTypeEnum syncTypeEnum) {
        return SyncContext.builder()
                .taskId(taskId)
                .syncTableConfig(syncTableConfig)
                .ifInsert(ifInsert)
                .syncTypeEnum(syncTypeEnum)
                .build();
    }

}
