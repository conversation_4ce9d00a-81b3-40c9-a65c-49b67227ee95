package com.tjsj.sync.modules.tailored.xyzq.service.impl;

import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqZslDO;
import com.tjsj.sync.modules.tailored.xyzq.mapper.LocalXyzqZslMapper;
import com.tjsj.sync.modules.tailored.xyzq.service.LocalXyzqZslService;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 21:49
 * @description
 */
@Service
public class LocalXyzqZslServiceImpl extends ServiceImpl<LocalXyzqZslMapper, LocalXyzqZslDO>
		implements LocalXyzqZslService {

	@Override
	public void insertBatchSomeColumn(List<LocalXyzqZslDO> list) {

		this.baseMapper.insertBatch(list);
	}

}
