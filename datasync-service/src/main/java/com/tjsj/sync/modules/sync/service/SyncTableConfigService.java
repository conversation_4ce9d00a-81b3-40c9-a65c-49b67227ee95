package com.tjsj.sync.modules.sync.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SyncTableConfigService
 *
 * <AUTHOR>
 * @date 2024/08/07
 * @description 针对表【database_sync_tables】的数据库操作Service
 */
public interface SyncTableConfigService extends IService<SyncTableConfigDO> {


    /**
     * 查找最近更新配置
     *
     * @param lastSyncTime 上次同步时间
     * @return {@link List }<{@link SyncTableConfigDO }>
     * <AUTHOR> Ye
     * @date 2024/12/17
     */
    List<SyncTableConfigDO> findRecentlyUpdatedConfig(LocalDateTime lastSyncTime);


    /**
     * 产生同时表配置
     *
     * @param sourceDbType 源数据库类型
     * @param targetDbType 目标数据库类型
     * @param sourceProfileType 源配置文件类型
     * @param targetProfileType 目标配置文件类型
     *
     * <AUTHOR> Ye
     * @date 2025/06/30
     */
    void generateSyncTableConfig(String sourceDbType, String targetDbType, String sourceProfileType,
                                 String targetProfileType);
}
