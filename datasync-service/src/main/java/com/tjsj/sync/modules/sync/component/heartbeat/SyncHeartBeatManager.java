package com.tjsj.sync.modules.sync.component.heartbeat;

import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * SyncHeartBeatManager
 *
 * <AUTHOR>
 * @date 2025/07/02
 * @description 同步心跳管理器 - 管理表同步过程中的心跳更新
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SyncHeartBeatManager {

    private final SyncTableConfigService syncTableConfigService;

    /**
     * 心跳更新间隔（毫秒）- 10秒
     */
    private static final long HEARTBEAT_INTERVAL_MS = 10 * 1000L;

    /**
     * 记录每个表的最后心跳更新时间
     */
    private final ConcurrentHashMap<Integer, AtomicLong> lastHeartBeatUpdateTime = new ConcurrentHashMap<>();

    /**
     * 更新同步心跳时间
     * 
     * @param syncTableConfigId 同步表配置ID
     */
    public void updateHeartBeat(Integer syncTableConfigId) {
        if (syncTableConfigId == null) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        AtomicLong lastUpdateTime = lastHeartBeatUpdateTime.computeIfAbsent(
                syncTableConfigId, k -> new AtomicLong(0));

        // 检查是否需要更新心跳（距离上次更新超过10秒）
        if (currentTime - lastUpdateTime.get() >= HEARTBEAT_INTERVAL_MS) {
            try {
                // 更新数据库中的心跳时间
                SyncTableConfigDO updateConfig = new SyncTableConfigDO();
                updateConfig.setId(syncTableConfigId);
                updateConfig.setLastSyncHeartBeatTime(LocalDateTime.now());
                
                syncTableConfigService.updateById(updateConfig);
                
                // 更新内存中的记录
                lastUpdateTime.set(currentTime);
                
                log.debug("💓 更新同步心跳 - 表配置ID: {}, 时间: {}", 
                        syncTableConfigId, updateConfig.getLastSyncHeartBeatTime());
                        
            } catch (Exception e) {
                log.error("❌ 更新同步心跳失败 - 表配置ID: {}, 错误: {}", 
                        syncTableConfigId, e.getMessage(), e);
            }
        }
    }

    /**
     * 清理心跳记录
     * 
     * @param syncTableConfigId 同步表配置ID
     */
    public void clearHeartBeat(Integer syncTableConfigId) {
        if (syncTableConfigId != null) {
            lastHeartBeatUpdateTime.remove(syncTableConfigId);
            log.debug("🧹 清理心跳记录 - 表配置ID: {}", syncTableConfigId);
        }
    }

    /**
     * 检查是否需要更新心跳
     * 
     * @param syncTableConfigId 同步表配置ID
     * @return true-需要更新，false-不需要更新
     */
    public boolean shouldUpdateHeartBeat(Integer syncTableConfigId) {
        if (syncTableConfigId == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        AtomicLong lastUpdateTime = lastHeartBeatUpdateTime.get(syncTableConfigId);
        
        if (lastUpdateTime == null) {
            return true;
        }
        
        return currentTime - lastUpdateTime.get() >= HEARTBEAT_INTERVAL_MS;
    }
}
