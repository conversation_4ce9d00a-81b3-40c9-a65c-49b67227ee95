package com.tjsj.sync.modules.tailored.xyzq.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.base.mapper.IBaseMapper;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqMrgTrdFlagDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * CloudXyzqRzrqFlagMapper
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 20:33
 * @description
 */
@Mapper
@DS(DataSourceNames.SOURCE_DB)
public interface CloudXyzqRzrqFlagMapper extends IBaseMapper<CloudXyzqMrgTrdFlagDO> {

	int deleteByPrimaryKey(Integer id);

	@Override
	int insert(CloudXyzqMrgTrdFlagDO record);

	int insertSelective(CloudXyzqMrgTrdFlagDO record);

	CloudXyzqMrgTrdFlagDO selectByPrimaryKey(Integer id);

	int updateByPrimaryKeySelective(CloudXyzqMrgTrdFlagDO record);

	int updateByPrimaryKey(CloudXyzqMrgTrdFlagDO record);
}