package com.tjsj.sync.modules.sync.controller;

import com.tjsj.sync.modules.sync.service.TransactionRecoveryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * TransactionMonitorController
 *
 * <AUTHOR>
 * @date 2025/08/03
 * @version 1.0.0
 * @description 事务监控控制器
 * 
 * <p>🎯 核心功能：</p>
 * <ul>
 *   <li><strong>事务状态查询</strong>：查看当前活跃的分布式事务</li>
 *   <li><strong>悬挂事务检测</strong>：手动触发悬挂事务检查</li>
 *   <li><strong>事务恢复管理</strong>：提供事务恢复相关操作</li>
 * </ul>
 */
@RestController
@RequestMapping("/transaction")
@Slf4j
@RequiredArgsConstructor
public class TransactionMonitorController {

    @Autowired(required = false)
    private TransactionRecoveryService transactionRecoveryService;

    /**
     * 获取活跃事务信息
     * 
     * @return 活跃事务信息
     */
    @GetMapping("/active")
    public Map<String, Object> getActiveTransactions() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (transactionRecoveryService == null) {
                result.put("status", "UNAVAILABLE");
                result.put("message", "事务恢复服务未启用");
                return result;
            }

            int count = transactionRecoveryService.getActiveTransactionCount();
            String info = transactionRecoveryService.getActiveTransactionsInfo();

            result.put("status", "SUCCESS");
            result.put("active_count", count);
            result.put("transactions_info", info);
            result.put("message", count > 0 ? "发现活跃事务" : "当前无活跃事务");

            log.debug("🔍 查询活跃事务 - 数量: {}", count);

        } catch (Exception e) {
            log.error("❌ 查询活跃事务失败", e);
            result.put("status", "ERROR");
            result.put("message", "查询失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 手动触发悬挂事务检查
     * 
     * @return 检查结果
     */
    @PostMapping("/check-pending")
    public Map<String, Object> checkPendingTransactions() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (transactionRecoveryService == null) {
                result.put("status", "UNAVAILABLE");
                result.put("message", "事务恢复服务未启用");
                return result;
            }

            log.info("🔍 手动触发悬挂事务检查");
            transactionRecoveryService.checkPendingTransactions();

            result.put("status", "SUCCESS");
            result.put("message", "悬挂事务检查已完成");
            result.put("active_count", transactionRecoveryService.getActiveTransactionCount());

            log.info("✅ 手动悬挂事务检查完成");

        } catch (Exception e) {
            log.error("❌ 手动悬挂事务检查失败", e);
            result.put("status", "ERROR");
            result.put("message", "检查失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 清理所有活跃事务记录
     * 
     * @return 清理结果
     */
    @PostMapping("/clear-active")
    public Map<String, Object> clearActiveTransactions() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (transactionRecoveryService == null) {
                result.put("status", "UNAVAILABLE");
                result.put("message", "事务恢复服务未启用");
                return result;
            }

            int beforeCount = transactionRecoveryService.getActiveTransactionCount();
            
            log.info("🧹 手动清理活跃事务记录");
            transactionRecoveryService.clearActiveTransactions();

            result.put("status", "SUCCESS");
            result.put("message", "活跃事务记录已清理");
            result.put("cleared_count", beforeCount);

            log.info("✅ 手动清理活跃事务记录完成，清理数量: {}", beforeCount);

        } catch (Exception e) {
            log.error("❌ 清理活跃事务记录失败", e);
            result.put("status", "ERROR");
            result.put("message", "清理失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取事务监控统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getTransactionStats() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (transactionRecoveryService == null) {
                result.put("status", "UNAVAILABLE");
                result.put("message", "事务恢复服务未启用");
                return result;
            }

            int activeCount = transactionRecoveryService.getActiveTransactionCount();
            
            result.put("status", "SUCCESS");
            result.put("active_transaction_count", activeCount);
            result.put("recovery_service_enabled", true);
            result.put("monitoring_active", true);

        } catch (Exception e) {
            log.error("❌ 获取事务统计信息失败", e);
            result.put("status", "ERROR");
            result.put("message", "获取统计信息失败: " + e.getMessage());
        }

        return result;
    }
}
