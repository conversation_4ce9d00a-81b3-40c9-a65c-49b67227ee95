package com.tjsj.sync.modules.sync.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.annotation.ReviewDate;
import com.tjsj.common.annotation.ServiceSwitchControl;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.common.enums.TaskExecStatus;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.common.utils.data.HashUtil;
import com.tjsj.common.utils.string.StrUtils;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.log.service.DatabaseSyncHistoryService;
import com.tjsj.modules.log.service.LocalDatabaseSyncHistoryService;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.modules.sync.model.entity.SyncDataTaskDO;
import com.tjsj.sync.modules.sync.component.handler.SyncExceptionHandler;
import com.tjsj.sync.modules.sync.component.preparer.SyncPreparer;
import com.tjsj.sync.modules.sync.component.processor.SyncPostProcessor;
import com.tjsj.sync.modules.sync.component.synchronizer.DataSynchronizer;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.modules.sync.service.CloudDatabaseSyncManualService;
import com.tjsj.modules.sync.service.LocalSyncDataTaskService;
import com.tjsj.modules.sync.service.SyncDataTaskService;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.sync.component.checker.SyncPreChecker;
import com.tjsj.sync.modules.sync.component.heartbeat.SyncHeartBeatManager;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import com.tjsj.modules.sync.model.LocalDatabaseSyncManual;
import com.tjsj.sync.modules.sync.service.DataSyncService;
import com.tjsj.sync.modules.sync.service.LocalDatabaseSyncManualService;
import com.tjsj.sync.modules.sync.utils.MySqlKeyword;
import com.tjsj.sync.modules.sync.utils.SyncUtil;
import com.tjsj.sync.modules.sync.utils.constants.SyncConsts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * DataSyncServiceImpl
 *
 * <AUTHOR> Ye
 * @date 2024/08/03
 * @description 数据同步服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Validated
public class DataSyncServiceImpl implements DataSyncService {

    @Resource
    private TarkinConfig tarkinConfig;

    @Resource
    private CloudDataMapper cloudDataMapper;

    @Resource
    private LocalDataMapper localDataMapper;

    @Resource
    private SyncTableConfigService syncTableConfigService;

    @Resource
    private CloudDatabaseSyncManualService cloudDatabaseSyncManualService;

    @Resource
    private LocalDatabaseSyncManualService localDatabaseSyncManualService;

    @Resource
    private DatabaseSyncHistoryService databaseSyncHistoryService;
    @Resource
    private LocalDatabaseSyncHistoryService localDatabaseSyncHistoryService;

    @Resource
    private SyncDataTaskService syncDataTaskService;

    @Resource
    private LocalSyncDataTaskService localSyncDataTaskService;

    @Resource
    private SyncPreChecker syncPreChecker;

    @Resource
    private SyncHeartBeatManager syncHeartBeatManager;

    @Resource
    private SyncPreparer syncPreparer;
    @Resource
    private SyncPostProcessor postProcessor;
    @Resource
    private SyncExceptionHandler exceptionHandler;
    @Resource
    private DataSynchronizer synchronizer;


    @Override
    public void syncCloudToLocal(Boolean ifInsert, List<Integer> configIds) {

        // 先取出需要同步的所有表
        List<SyncTableConfigDO> syncTableConfigList =
                syncTableConfigService.list(Wrappers.<SyncTableConfigDO>lambdaQuery()
                        .eq(SyncTableConfigDO::getEnableStatus, CommonStatus.ENABLE)
                        .eq(SyncTableConfigDO::getDeleteStatus, CommonStatus.ENABLE)
                        .eq(SyncTableConfigDO::getProjectId, tarkinConfig.getProjectId())
                        .eq(SyncTableConfigDO::getDbType, tarkinConfig.getDbType())
                        .eq(SyncTableConfigDO::getProfileType, tarkinConfig.getProfile())
                        .orderByAsc(SyncTableConfigDO::getSyncOrder));
        // 如果传入了配置id列表，则只同步指定的表
        if (CollUtil.isNotEmpty(configIds)) {
            syncTableConfigList = syncTableConfigList.stream()
                    .filter(config -> configIds.contains(config.getId()))
                    .collect(Collectors.toList());
        }

        syncTableConfigList = SyncUtil.filterSyncTableConfigList(syncTableConfigList);

        SyncDataTaskDO syncDataTaskRecord = syncDataTaskService.generateSyncTask(syncTableConfigList.size());
        localSyncDataTaskService.save(syncDataTaskRecord);
        Integer taskId = syncDataTaskRecord.getTaskId();

        try {

            // 循环处理每个表
            for (SyncTableConfigDO syncTableConfig : syncTableConfigList) {
                log.debug("开始同步表 {}", syncTableConfig.getSchemaName() + "." + syncTableConfig.getTableName());
                // 同步单个表数据
                this.syncSingleTableDataNew(taskId, syncTableConfig, ifInsert, null);
//                this.syncSingleTableData(taskId, syncTableConfig, ifInsert, null);
            }
        } catch (Exception e) {
            log.error("❌❌❌ 同步任务执行失败", e);
            syncDataTaskRecord.setTaskEndTime(LocalDateTime.now());
            syncDataTaskService.updateById(syncDataTaskRecord);
            return;
        }

        syncDataTaskRecord.setTaskEndTime(LocalDateTime.now()).setTaskStatus(CommonStatus.ENABLE);
        localSyncDataTaskService.updateById(syncDataTaskRecord);
        syncDataTaskService.updateById(syncDataTaskRecord);


        // 手动清除缓存或临时对象
        System.gc();
    }


    @Override
    public void filterTableColumns(@NotNull SyncTableConfigDO tableConfig, List<Map<String, Object>> dataList,
                                   DatabaseSyncHistoryDO syncHistoryRecord) {

        // 将所有表字段转换为小写
        String dataSchemaName = tableConfig.getDataSchemaName();
        String dataTableName = tableConfig.getDataTableName();
        Set<String> cloudTableColumns = new HashSet<>(cloudDataMapper.listTableColumns(dataSchemaName, dataTableName));
        cloudTableColumns = cloudTableColumns.stream().map(String::toLowerCase).collect(Collectors.toSet());

        String schemaName = tableConfig.getSchemaName();
        String tableName = tableConfig.getTableName();
        Set<String> localTableColumns = new HashSet<>(localDataMapper.listTableColumns(schemaName, tableName));
        localTableColumns = localTableColumns.stream().map(String::toLowerCase).collect(Collectors.toSet());

        // 计算出数据源表存在，但是目标表不存在的字段
        cloudTableColumns.removeAll(localTableColumns);
        if (CollUtil.isEmpty(cloudTableColumns)) {
            return;
        }

        // 将字段列表拼接成逗号分隔的字符串
        String columnsString = CollUtil.join(cloudTableColumns, ",");
        // 拼接最终 remark 字符串
        String remark = StrUtil.format("数据源表存在，但是目标表不存在的字段：{}", columnsString);
        if (syncHistoryRecord != null) {
            syncHistoryRecord.setRemark(remark);
        }

        // 过滤 dataList，将不存在于目标表的字段删除
        Set<String> finalCloudTableColumns = cloudTableColumns;
        dataList.forEach(data -> finalCloudTableColumns.forEach(data::remove));

    }

    /**
     * 过滤数据列
     *
     * @param tableConfig    同步表配置
     * @param insertDataList 需要插入的数据列表
     * <AUTHOR> Ye
     * @date 2024/10/16
     */
    @Override
    public void filterDataColumns(@NotNull SyncTableConfigDO
                                          tableConfig, List<Map<String, Object>> insertDataList) {

        // TODO: 250724 处理数据的createTime或者updateTime字段值为0000-00-00 00:00:00的情况，不过暂时不用处理

        String includeColumn = tableConfig.getIncludeColumn();
        String excludeColumn = tableConfig.getExcludeColumn();

        // 保留指定列
        if (StrUtil.isNotEmpty(includeColumn)) {
            List<String> includeColumnList = Arrays.stream(includeColumn.split(",")).toList();
            insertDataList.forEach(data -> {
                data.keySet().retainAll(includeColumnList);
            });
        }

        // 排除指定列
        if (StrUtil.isNotEmpty(excludeColumn)) {
            List<String> excludeColumnList = Arrays.stream(excludeColumn.split(",")).toList();
            insertDataList.forEach(data -> {
                excludeColumnList.forEach(data::remove);
            });
        }

    }


    @Override
    @CheckCount(count = 1)
    @ReviewDate(reviewDates = {"2024-11-26 23:24"})
    @ServiceSwitchControl("手动数据同步")
    public void syncManual() {

        // 取出所有同步任务，数据库配置和环境配置需要模糊匹配
        List<DataSyncManualDO> syncManualRecordList =
                cloudDatabaseSyncManualService.list(Wrappers.<DataSyncManualDO>lambdaQuery()
                        .eq(DataSyncManualDO::getTaskStatus, CommonStatus.ENABLE)
                        .eq(DataSyncManualDO::getDeleteStatus, CommonStatus.ENABLE)
                        .like(StrUtil.isNotBlank(tarkinConfig.getProjectId()), DataSyncManualDO::getProjectId,
                                tarkinConfig.getProjectId())
                        .like(DataSyncManualDO::getDbType, tarkinConfig.getDbType())
                        .like(DataSyncManualDO::getProfileType, tarkinConfig.getProfile()));
        if (CollUtil.isEmpty(syncManualRecordList)) {
            return;
        }


        SyncDataTaskDO syncDataTaskRecord = syncDataTaskService.generateSyncTask(syncManualRecordList.size());
        Integer taskId = syncDataTaskRecord.getTaskId();

        try {
            for (DataSyncManualDO syncTableConfig : syncManualRecordList) {

                this.manualSyncSingleTableData(taskId, syncTableConfig);
            }

        } catch (Exception e) {
            log.error("手动同步任务失败，任务ID：{}，异常信息：{}", taskId, e.getMessage());
            syncDataTaskRecord.setTaskEndTime(LocalDateTime.now());
            syncDataTaskService.updateById(syncDataTaskRecord);
            return;
        }

        syncDataTaskRecord.setTaskEndTime(LocalDateTime.now()).setTaskStatus(CommonStatus.ENABLE);
        syncDataTaskService.updateById(syncDataTaskRecord);

    }

    @Override
    public void transferLocalSyncManualToCloud() {

        // 取出落地数据库中所有待执行的手动同步任务
        List<DataSyncManualDO> localDatabaseSyncManualList =
                localDatabaseSyncManualService.list(Wrappers.<LocalDatabaseSyncManual>lambdaQuery()
                                .eq(LocalDatabaseSyncManual::getTaskStatus, TaskExecStatus.NOT_EXECUTED))
                        .stream()
                        .map(localDatabaseSyncManual ->
                                BeanUtil.copyProperties(localDatabaseSyncManual, DataSyncManualDO.class))
                        .toList();
        //log.error("localDatabaseSyncManual size:{}", localDatabaseSyncManualList.size());
        if (localDatabaseSyncManualList.isEmpty()) {
            localDatabaseSyncManualList = createLocalSyncManualList();
        }


        localDatabaseSyncManualList.forEach(localDatabaseSyncManual -> {
            // 判断是否已经存在相同的同步任务
            DataSyncManualDO existRecord = cloudDatabaseSyncManualService.getOne(localDatabaseSyncManual);

            Optional.ofNullable(existRecord)
                    .ifPresentOrElse(cloudDatabaseSyncManual -> {
                        // 更新手动同步任务
                        cloudDatabaseSyncManual.setTaskStatus(TaskExecStatus.NOT_EXECUTED);
                        cloudDatabaseSyncManualService.updateById(cloudDatabaseSyncManual);
                    }, () -> {
                        // 插入一条新的手动同步任务
                        localDatabaseSyncManual.setId(null);
                        cloudDatabaseSyncManualService.save(localDatabaseSyncManual);
                    });
        });


    }

    @Override
    public void manualSyncSingleTableData(Integer taskId, DataSyncManualDO syncTableConfig) {

        // 创建同步历史日志
        DatabaseSyncHistoryDO syncHistory = syncPreparer.createSyncHistoryRecord(taskId, syncTableConfig, null, null);
        long startTime = System.currentTimeMillis();
        Integer thisTableSyncTotalDataNum = null;

        // 执行sql语句
        if (StrUtil.isNotEmpty(syncTableConfig.getExecuteSql())) {
            this.executeSql(syncTableConfig);
        } else {
            thisTableSyncTotalDataNum = 0;
            try {

                // 删除数据库中指定表中数据
                this.deleteTargetTableData(syncTableConfig);

                // 每批最大记录数
                int batchSize = 20000;
                boolean hasMoreData = true;
                // 起始索引
                int offset = 0;

                while (hasMoreData) {
                    List<Map<String, Object>> insertData = this.queryDataFromSource(syncTableConfig, offset,
                            batchSize);

                    if (insertData.isEmpty()) {
                        hasMoreData = false;
                    } else {
                        // 处理关键字
                        List<Map<String, Object>> dataList = new ArrayList<>();
                        for (Map<String, Object> record : insertData) {
                            dataList.add(MySqlKeyword.processKeywords(record));
                        }

                        // 对插入的数据进行后续处理
                        dataList = this.manualSyncProcessInsertData(syncTableConfig, dataList, syncHistory);

                        // 插入目标数据库
                        this.insertDataToTarget(syncTableConfig, dataList);
                        thisTableSyncTotalDataNum += dataList.size();
                        // 更新起始索引
                        offset += batchSize;
                    }
                }
            } catch (Exception e) {
                String remark = syncHistory.getRemark() == null ? "" : syncHistory.getRemark() +
                        ";";
                syncHistory.setTaskExecuteStatus(TaskExecuteStatusEnum.FAILURE)
                        .setRemark(remark + e.getMessage());

            }
        }

        // 更新同步任务历史
        this.saveSyncHistoryRecord(syncHistory, thisTableSyncTotalDataNum, startTime);

        // 更新任务状态
        cloudDatabaseSyncManualService.updateById(syncTableConfig.setTaskStatus(TaskExecStatus.EXECUTED)
                .setUpdateTime(null));
    }

    @Override
    public Boolean compareTableMaxTime(SyncTableConfigDO syncTableConfig) {
        SyncTaskTypeEnum syncTaskType = syncTableConfig.getTaskType();
        LocalDateTime localTableMaxTime = localDataMapper.getTableMaxUpdateTime(syncTableConfig);
        LocalDateTime cloudTableMaxTime = cloudDataMapper.getTableMaxUpdateTime(syncTableConfig);

        // 取出源数据库和目标数据库的最大更新时间
        // 判断源数据库的最大更新时间是否大于目标数据库的最大更新时间
        LocalDateTime sourceMaxTime = syncTaskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL) ?
                cloudTableMaxTime : localTableMaxTime;
        LocalDateTime targetMaxTime = syncTaskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL) ?
                localTableMaxTime : cloudTableMaxTime;

        if (null != sourceMaxTime && null != targetMaxTime) {
            return sourceMaxTime.isAfter(targetMaxTime);
        } else {
            return true;
        }

    }

    @Override
    public Boolean checkDataHashValue(List<Map<String, Object>> insertDataList, SyncTableConfigDO syncTableConfig,
                                      SyncTypeEnum syncTypeEnum) {

        // 如果是quartz同步，则不比较hash值，直接返回true
        if (!Objects.equals(syncTypeEnum, SyncTypeEnum.QUARTZ)) {
            return true;
        }

        Optional<String> optionalHash =
                Optional.ofNullable(syncTableConfigService.getById(syncTableConfig.getId()))
                        .map(SyncTableConfigDO::getQuartzLastSyncDataHash);

        String oldDataHashValue = optionalHash.orElse(null);
        // 生成新的数据哈希值
        String newDataHashValue = HashUtil.generateHash(insertDataList);

        syncTableConfig.setQuartzLastSyncDataHash(newDataHashValue);

        // 如果新数据的哈希值与旧数据的哈希值相同，则不进行同步
        return !Objects.equals(oldDataHashValue, newDataHashValue);
    }

    /**
     * 创造本地同步手动列表
     *
     * @return {@link List }<{@link DataSyncManualDO }>
     * <AUTHOR> Ye
     * @date 2024/12/13
     */
    private List<DataSyncManualDO> createLocalSyncManualList() {
        List<DataSyncManualDO> localDatabaseSyncManualList = new ArrayList<>();

//		// 创建手动同步同步程序任务
//		CloudDataSyncManualDO cloudDataSyncManualDO =
//				new CloudDataSyncManualDO().setDbType(EnumUtil.getBy(DbTypeEnum::getCode, dbType))
//						.setDeleteTableName("tj_middle_ground.t_log_auto")
//						.setInsertTableName("tj_middle_ground.t_log_auto")
//						// 替换占位符
//						.setInsertTableCondition(String.format("env = '%s' and date = CURRENT_DATE", env))
//						.setIfIdSync(CommonStatus.DISABLE)
//						.setIfTableDelete(CommonStatus.DISABLE)
//						.setTaskType(SyncTaskTypeEnum.LOCAL_TO_CLOUD)
//						.setTaskStatus(TaskExecStatus.NOT_EXECUTED);
//		localDatabaseSyncManualList.add(cloudDataSyncManualDO);

        return localDatabaseSyncManualList;

    }


    /**
     * 保存同步历史
     *
     * @param databaseSyncHistory 数据库同步历史记录
     * @param totalDataNum        全部数据数量
     * @param startTime           开始时间
     * <AUTHOR> Ye
     * @date 2024/08/07
     */
    private void saveSyncHistoryRecord(@NotNull DatabaseSyncHistoryDO databaseSyncHistory, Integer totalDataNum,
                                       long startTime) {

        long thisTableSyncEndTime = System.currentTimeMillis();

        // 设置同步数据历史记录的属性
        databaseSyncHistory.setTimeDuration((int) (thisTableSyncEndTime - startTime))
                .setDataNum(totalDataNum)
                .setEndTime(LocalDateTime.now());

        // 设置同步成功失败状态
        if (databaseSyncHistory.getTaskExecuteStatus() == null) {
            databaseSyncHistory.setTaskExecuteStatus(TaskExecuteStatusEnum.SUCCESS);
        }

        databaseSyncHistoryService.save(databaseSyncHistory);
        localDatabaseSyncHistoryService.save(databaseSyncHistory);
    }

    /**
     * 向目标插入数据
     *
     * @param syncInfo            同步信息
     * @param processedInsertData 已处理插入数据
     * <AUTHOR> Ye
     * @date 2024/08/01
     */
    private void insertDataToTarget(DataSyncManualDO
                                            syncInfo, List<Map<String, Object>> processedInsertData) {
        if (syncInfo.getTaskType().equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            localDataMapper.batchInsertTableDataByCondition(syncInfo, processedInsertData);
        } else if (syncInfo.getTaskType().equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            cloudDataMapper.batchInsertTableDataByCondition(syncInfo, processedInsertData);
        }
    }

    /**
     * 从源查询数据
     *
     * @param syncInfo  同步信息
     * @param offset    抵消
     * @param batchSize 批量大小
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR> Ye
     * @date 2024/08/01
     */
    private List<Map<String, Object>> queryDataFromSource(DataSyncManualDO syncInfo, int offset,
                                                          int batchSize) {
        List<Map<String, Object>> insertData = new ArrayList<>();
        // 按条件取出云端数据库指定表数据
        if (syncInfo.getTaskType().equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            insertData = cloudDataMapper
                    .selectTableDataByCondition(syncInfo, offset, batchSize);
        } else if (syncInfo.getTaskType().equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            insertData = localDataMapper
                    .selectTableDataByCondition(syncInfo, offset, batchSize);
        }
        return insertData;
    }


    /**
     * 执行sql
     *
     * @param syncTableConfig 同步表配置
     * <AUTHOR> Ye
     * @date 2025/02/05
     */
    private void executeSql(DataSyncManualDO syncTableConfig) {

        SyncTaskTypeEnum taskType = syncTableConfig.getTaskType();

        if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            localDataMapper.executeSqlToLocal(syncTableConfig.getExecuteSql());
        } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            cloudDataMapper.executeSqlToCloud(syncTableConfig.getExecuteSql());
        }

    }

    /**
     * 手动同步处理插入数据
     *
     * @param tableConfig       同步信息
     * @param dataList          已处理云数据
     * @param syncHistoryRecord 同步历史记录
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR> Ye
     * @date 2024/08/07
     */
    private List<Map<String, Object>> manualSyncProcessInsertData(DataSyncManualDO tableConfig,
                                                                  List<Map<String, Object>> dataList,
                                                                  DatabaseSyncHistoryDO syncHistoryRecord) {
        // 是否同步id
        if (tableConfig.getIfIdSync().equals(CommonStatus.DISABLE)) {
            dataList = dataList.stream()
                    .peek(record -> record.put("id", null))
                    .collect(Collectors.toList());
        }

        // 如果updateTime字段为null，置为当前时间
        dataList = dataList.stream()
                .peek(record -> {
                    if (record.containsKey(SyncConsts.UPDATE_TIME_FIELD) &&
                            record.get(SyncConsts.UPDATE_TIME_FIELD) == null) {
                        record.put(SyncConsts.UPDATE_TIME_FIELD, LocalDateTime.now());
                    }
                }).collect(Collectors.toList());

        // 过滤调用数据源表中存在，但是目标表不存在的字段
        SyncTableConfigDO syncTableConfig = new SyncTableConfigDO()
                .setDataSchemaName(tableConfig.getInsertTableName().split("\\.")[0])
                .setDataTableName(tableConfig.getInsertTableName().split("\\.")[1])
                .setSchemaName(tableConfig.getDeleteTableName().split("\\.")[0])
                .setTableName(tableConfig.getDeleteTableName().split("\\.")[1]);
        this.filterTableColumns(syncTableConfig, dataList, syncHistoryRecord);
        return dataList;
    }


    /**
     * 删除目标表数据
     *
     * @param syncTableConfig 同步信息
     * <AUTHOR> Ye
     * @date 2024/07/30
     */
    private void deleteTargetTableData(DataSyncManualDO syncTableConfig) {

        // 是否删除目标表数据
        if (syncTableConfig.getIfTableDelete().equals(CommonStatus.ENABLE)) {

            SyncTaskTypeEnum taskType = syncTableConfig.getTaskType();
            String deleteTableCondition = syncTableConfig.getDeleteTableCondition();
            LocalDateTime deleteStartUpdateTime = syncTableConfig.getDeleteStartUpdateTime();
            Integer deleteStartId = syncTableConfig.getDeleteStartId();
            String deleteTableName = syncTableConfig.getDeleteTableName();

            if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
                // truncate被插入数据的表
                if (StrUtil.isEmpty(deleteTableCondition) && (null == deleteStartUpdateTime)
                        && (null == deleteStartId)) {
                    cloudDataMapper.truncateTableData(deleteTableName);
                } else {
                    cloudDataMapper.deleteTableDataByCondition(deleteTableName, deleteTableCondition, deleteStartId,
                            deleteStartUpdateTime);
                }

            } else if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
                // truncate被插入数据的表
                if (StrUtils.nullOrEmpty(deleteTableCondition) && (null == deleteStartUpdateTime)
                        && (null == deleteStartId)) {
                    localDataMapper.truncateTableData(deleteTableName);
                } else {
                    localDataMapper.deleteTableDataByCondition(deleteTableName, deleteTableCondition, deleteStartId,
                            deleteStartUpdateTime);
                }
            }

        }

    }

    /**
     * 同步单个表数据
     *
     * @param taskId          任务ID
     * @param syncTableConfig 同步表配置
     * @param ifInsert        是否插入
     * @param syncTypeEnum    同步类型
     */
    @Override
    public void syncSingleTableDataNew(Integer taskId, SyncTableConfigDO syncTableConfig, Boolean ifInsert,
                                       SyncTypeEnum syncTypeEnum) {

        // 创建同步上下文
        SyncContext context = SyncContext.build(taskId, syncTableConfig, ifInsert, syncTypeEnum);

        log.debug("🚀 开始同步表 {}", context.getFullTableName());

        try {
            // 1. 前置检查 - 验证同步条件
            if (!syncPreChecker.checkSyncable(context)) {
                log.debug("⏭️ 表 {} 跳过同步", context.getFullTableName());
                return;
            }

            // 2. 同步准备 - 初始化同步环境
            syncPreparer.prepare(context);

            // 3. 执行同步 - 核心数据同步逻辑
//            synchronizer.synchronizeTableData(context);
            synchronizer.synchronizeTableDataNew(context);

        } catch (Exception e) {
            log.error("❌❌❌ 同步表 {} 数据失败", context.getFullTableName(), e);

            exceptionHandler.handle(context, e);
        }

        // 4. 后置处理 - 无论成功失败都要执行，保存历史记录
        postProcessor.process(context);

        // 5. 清理心跳记录
        syncHeartBeatManager.clearHeartBeat(context.getSyncTableConfig().getId());

        log.debug("✅ 表 {} 同步处理完成，共处理 {} 条数据",
                context.getFullTableName(), context.getTotalDataNum());

    }


}
