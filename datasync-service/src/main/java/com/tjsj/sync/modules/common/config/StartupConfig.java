package com.tjsj.sync.modules.common.config;

import com.tjsj.modules.manage.service.HeartBeatTestService;
import com.tjsj.sync.modules.sync.service.DataSyncService;
import com.tjsj.sync.modules.sync.utils.quartz.DynamicTaskScheduler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * StartupConfig
 *
 * <AUTHOR> Ye
 * @version 2.0.0
 * @date 2025/08/03
 * @description 应用启动初始化配置类 🚀
 *
 * <p>🎯 核心功能：</p>
 * <ul>
 *   <li><strong>数据库心跳检测</strong>：启动时检测云数据库连接状态，异常时自动执行恢复同步</li>
 *   <li><strong>Quartz任务管理</strong>：初始化和管理定时同步任务，确保调度器正常工作</li>
 *   <li><strong>性能监控</strong>：记录各初始化步骤的执行耗时，便于性能分析</li>
 *   <li><strong>异常处理</strong>：提供友好的错误提示和恢复建议</li>
 * </ul>
 *
 * <p>🔄 执行时机：</p>
 * <p>使用 {@link ApplicationReadyEvent} 确保在所有Bean初始化完成后执行，
 * 避免依赖注入问题，不受方法注解限制。</p>
 *
 * <p>📊 执行流程：</p>
 * <ol>
 *   <li>数据库心跳测试 → 连接异常时自动执行手动同步恢复</li>
 *   <li>Quartz定时任务管理 → 初始化调度器和同步任务</li>
 *   <li>性能统计 → 记录总耗时和各步骤耗时</li>
 * </ol>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class StartupConfig {

    private final HeartBeatTestService heartBeatTestService;
    private final DataSyncService dataSyncService;
    @Resource
    private DynamicTaskScheduler dynamicTaskScheduler;

    /**
     * 项目启动完成后执行初始化任务
     * 使用ApplicationReadyEvent确保在所有Bean初始化完成后执行
     * 不受原方法注解的影响，直接调用底层服务
     *
     * @param event 应用就绪事件
     */
    @EventListener(ApplicationReadyEvent.class)
    public void executeStartupHeartBeat(ApplicationReadyEvent event) {
        long totalStartTime = System.currentTimeMillis();

        log.info("🚀 ==================== 应用启动后初始化开始 ====================");
        log.info("📅 启动时间: {}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 1. 执行数据库心跳测试
        executeHeartbeatTest();

        // 2. 管理Quartz定时任务
        manageQuartzJobs();

        long totalDuration = System.currentTimeMillis() - totalStartTime;
        log.info("⏱️ 初始化总耗时: {}ms", totalDuration);
        log.info("✅ ==================== 应用启动后初始化完成 ====================");
    }

    /**
     * 执行数据库心跳测试
     */
    private void executeHeartbeatTest() {
        log.info("💓 开始执行数据库心跳测试...");
        long startTime = System.currentTimeMillis();

        try {
            // 执行与 testCloudDatabaseHeartBeat() 方法相同的逻辑
            // 但不受其注解限制
            testCloudDatabaseHeartBeatOnStartup();

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ 数据库心跳测试成功完成 (耗时: {}ms)", duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ 数据库心跳测试失败 (耗时: {}ms)", duration, e);
            log.warn("⚠️ 心跳测试失败不影响应用启动，请检查数据库连接配置");
        }
    }

    /**
     * 管理Quartz定时任务
     */
    private void manageQuartzJobs() {
        log.info("⚙️ 开始管理Quartz定时任务...");
        long startTime = System.currentTimeMillis();

        try {
            dynamicTaskScheduler.manageQuartzJob();

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ Quartz任务管理成功完成 (耗时: {}ms)", duration);

        } catch (SchedulerException e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ Quartz任务管理失败 (耗时: {}ms)", duration, e);
            log.warn("⚠️ Quartz任务管理失败可能影响定时同步功能，请检查调度器配置");

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ Quartz任务管理发生未知异常 (耗时: {}ms)", duration, e);
        }
    }

    /**
     * 启动时心跳测试方法
     * 复制 DatabaseSyncController.testCloudDatabaseHeartBeat() 的逻辑
     * 但不受注解控制
     */
    private void testCloudDatabaseHeartBeatOnStartup() {
        log.debug("🔍 开始检测云数据库连接状态...");

        // 云数据库心跳测试
        if (!heartBeatTestService.testHeartBeat()) {
            log.warn("💔 云数据库心跳测试失败，开始执行手动同步恢复...");

            try {
                dataSyncService.syncManual();
                heartBeatTestService.updateHeartBeatStatus();
                log.info("🔄 手动同步完成，心跳状态已更新");
            } catch (Exception e) {
                log.error("❌ 手动同步执行失败", e);
                throw e; // 重新抛出异常，让上层处理
            }
        } else {
            log.info("💚 云数据库心跳测试成功，连接状态正常");
        }

        // 注意：原方法中的本地数据库心跳测试部分已被注释，这里保持一致
        // 如果需要启用本地数据库心跳测试，可以取消下面的注释
        /*
        log.debug("🔍 开始检测本地数据库连接状态...");
        if (!localHeartBeatTestService.testHeartBeat()) {
            log.warn("💔 本地数据库心跳测试失败，开始执行手动同步恢复...");
            dataSyncService.syncManual();
            localHeartBeatTestService.updateHeartBeatStatus();
            log.info("🔄 本地数据库手动同步完成，心跳状态已更新");
        } else {
            log.info("💚 本地数据库心跳测试成功，连接状态正常");
        }
        */
    }
}
