package com.tjsj.sync.modules.sync.utils;

import cn.hutool.core.util.EnumUtil;
import com.tjsj.common.annotation.sync.CronSyncConfig;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.utils.quartz.QuartzSyncTask;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.jetbrains.annotations.NotNull;
import org.quartz.JobBuilder;
import org.quartz.JobDataMap;
import org.quartz.JobDetail;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * SyncUtil
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/3 23:26
 * @description 同步工具类
 */
@Schema(description = "同步工具类")
@Slf4j
public class SyncUtil {

    /**
     * 获取固定的全量更新表列表
     * <p>为什么这些表需要全量更新？因为这些表中的数据更新逻辑不是增量更新，而是直接覆盖源数据，所以需要全量更新。</p>
     *
     * @return {@link List }<{@link String }>
     * <AUTHOR> Ye
     * @date 2024/12/03
     */
    public static List<SyncTableConfigDO> getFixedFullUpdateTableList() {
        List<SyncTableConfigDO> fixedFullUpdateTableList = new ArrayList<>();

        fixedFullUpdateTableList.add(new SyncTableConfigDO().setSchemaName("tj_middle_ground").setTableName(
                "t_sec_peer_info"));

        fixedFullUpdateTableList.add(new SyncTableConfigDO().setSchemaName("datagrade").setTableName(
                "t_user_level_strategy"));

        fixedFullUpdateTableList.add(new SyncTableConfigDO().setSchemaName("tj_middle_ground").setTableName(
                "t_stock_chart"));

        fixedFullUpdateTableList.add(new SyncTableConfigDO().setSchemaName("labels").setTableName(
                "label_last_new"));

        return fixedFullUpdateTableList;
    }


    /**
     * 过滤同步表配置列表
     *
     * @param syncTableConfigList 同步表配置列表
     * <AUTHOR> Ye
     * @date 2024/12/04
     */
    public static @NotNull List<SyncTableConfigDO> filterSyncTableConfigList(
            List<SyncTableConfigDO> syncTableConfigList) {

        // 暂时只开放云到本地的同步
//        syncTableConfigList = syncTableConfigList.stream()
//                .filter(syncTableConfigDO -> syncTableConfigDO.getTaskType().equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL))
//                .toList();

        // 将某些表强制转换成全量更新
        List<SyncTableConfigDO> fixedFullUpdateTableList = getFixedFullUpdateTableList();
        syncTableConfigList.forEach(syncTableConfigDO -> {
            fixedFullUpdateTableList.stream()
                    .filter(fixedFullUpdateTable ->
                            fixedFullUpdateTable.getSchemaName().equals(syncTableConfigDO.getSchemaName())
                                    && fixedFullUpdateTable.getTableName().equals(syncTableConfigDO.getTableName()))
                    .findFirst()
                    .ifPresent(fixedFullUpdateTable -> {
                        syncTableConfigDO.setIfFullUpdate(CommonStatus.ENABLE);
                    });
        });


        return syncTableConfigList;
    }


    public static String generateSyncTableHash(SyncTableConfigDO config) {
        try {
            Class<?> clazz = config.getClass();
            Field[] fields = clazz.getDeclaredFields();
            StringJoiner joiner = new StringJoiner("|"); // 分隔符避免值碰撞

            for (Field field : fields) {
                if (field.isAnnotationPresent(CronSyncConfig.class)) {
                    field.setAccessible(true);
                    Object value = field.get(config);
                    joiner.add(value != null ? value.toString() : "null");
                }
            }

            return DigestUtils.md5Hex(joiner.toString());
        } catch (IllegalAccessException e) {
            throw new RuntimeException("生成同步配置哈希失败", e);
        }
    }


    /**
     * 从 JobDataMap 构建 SyncTableConfigDO 对象
     *
     * @param jobDataMap Quartz 任务数据
     * @return SyncTableConfigDO 配置信息对象
     */
    public static SyncTableConfigDO buildSyncTableConfigFromJobDataMap(JobDataMap jobDataMap) {

        return new SyncTableConfigDO()
                .setId(jobDataMap.getInt(SyncTableConfigDO.Fields.id))
                .setTableName(jobDataMap.getString(SyncTableConfigDO.Fields.tableName))
                .setSchemaName(jobDataMap.getString(SyncTableConfigDO.Fields.schemaName))
                .setDataSchemaName(jobDataMap.getString(SyncTableConfigDO.Fields.dataSchemaName))
                .setDataTableName(jobDataMap.getString(SyncTableConfigDO.Fields.dataTableName))
                .setIfFullUpdate(EnumUtil.getBy(CommonStatus::getCode,
                        jobDataMap.getInt(SyncTableConfigDO.Fields.ifFullUpdate)))
                .setReadSize(jobDataMap.get(SyncTableConfigDO.Fields.readSize) == null ?
                        null : (Integer) jobDataMap.get(SyncTableConfigDO.Fields.readSize))
                .setBatchSize(jobDataMap.get(SyncTableConfigDO.Fields.batchSize) == null ?
                        null : (Integer) jobDataMap.get(SyncTableConfigDO.Fields.batchSize))
                .setTaskType(EnumUtil.getBy(SyncTaskTypeEnum::getCode,
                        jobDataMap.getInt(SyncTableConfigDO.Fields.taskType)))
                .setIncludeColumn(jobDataMap.getString(SyncTableConfigDO.Fields.includeColumn))
                .setExcludeColumn(jobDataMap.getString(SyncTableConfigDO.Fields.excludeColumn))
                .setProjectId(jobDataMap.getString(SyncTableConfigDO.Fields.projectId))
                .setDbType(EnumUtil.getBy(EnvironmentTypeEnum::getCode, jobDataMap.getString(SyncTableConfigDO.Fields.dbType)))
                .setProfileType(jobDataMap.getString(SyncTableConfigDO.Fields.profileType))
                .setTaskType(EnumUtil.getBy(SyncTaskTypeEnum::getCode,
                        jobDataMap.getInt(SyncTableConfigDO.Fields.taskType)))
                .setCrontab(jobDataMap.getString(SyncTableConfigDO.Fields.crontab))
                .setSyncOrder(jobDataMap.get(SyncTableConfigDO.Fields.syncOrder) == null ?
                        null : (Integer) jobDataMap.get(SyncTableConfigDO.Fields.syncOrder))
                .setIfCompareMaxTime(EnumUtil.getBy(CommonStatus::getCode,
                        jobDataMap.getInt(SyncTableConfigDO.Fields.ifCompareMaxTime)))
                .setTableGroupByColumn(jobDataMap.getString(SyncTableConfigDO.Fields.tableGroupByColumn));

    }


    /**
     * 从 SyncTableConfigDO 对象构建 JobDataMap
     *
     * @param config 配置信息对象
     * @param jobKey 工作键
     * @return JobDetail 任务详情
     * <AUTHOR> Ye
     * @date 2025/05/26
     */
    public static JobDetail buildJobDetailFromSyncTableConfig(SyncTableConfigDO config, String jobKey) {

        return JobBuilder.newJob(QuartzSyncTask.class)
                .withIdentity(jobKey)
                .usingJobData(SyncTableConfigDO.Fields.id, config.getId())
                .usingJobData(SyncTableConfigDO.Fields.tableName, config.getTableName())
                .usingJobData(SyncTableConfigDO.Fields.schemaName, config.getSchemaName())
                .usingJobData(SyncTableConfigDO.Fields.dataSchemaName, config.getDataSchemaName())
                .usingJobData(SyncTableConfigDO.Fields.dataTableName, config.getDataTableName())
                .usingJobData(SyncTableConfigDO.Fields.ifFullUpdate, config.getIfFullUpdate().getCode())
                .usingJobData(SyncTableConfigDO.Fields.readSize, config.getReadSize())
                .usingJobData(SyncTableConfigDO.Fields.batchSize, config.getBatchSize())
                .usingJobData(SyncTableConfigDO.Fields.taskType, config.getTaskType().getCode())
                .usingJobData(SyncTableConfigDO.Fields.includeColumn, config.getIncludeColumn())
                .usingJobData(SyncTableConfigDO.Fields.excludeColumn, config.getExcludeColumn())
                .usingJobData(SyncTableConfigDO.Fields.projectId, config.getProjectId())
                .usingJobData(SyncTableConfigDO.Fields.dbType, config.getDbType().getCode())
                .usingJobData(SyncTableConfigDO.Fields.profileType, config.getProfileType())
                .usingJobData(SyncTableConfigDO.Fields.crontab, config.getCrontab())
                .usingJobData(SyncTableConfigDO.Fields.syncOrder, config.getSyncOrder())
                .usingJobData(SyncTableConfigDO.Fields.ifCompareMaxTime, config.getIfCompareMaxTime().getCode())
                .usingJobData(SyncTableConfigDO.Fields.tableGroupByColumn, config.getTableGroupByColumn())
                .build();


    }


}
