package com.tjsj.sync.modules.sync.model.bo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * SyncResult
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步结果
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncResult {


    /**
     * 同步的总数据量
     */
    private int totalDataNum;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 处理的批次数
     */
    private int batchCount;


}
