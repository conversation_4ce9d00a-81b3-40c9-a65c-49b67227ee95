package com.tjsj.sync.modules.business.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * InfoSchemaColumn
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description 信息模式列信息表
 * @date 2024/10/12 19:08
 */
@Schema
@Data
@Accessors(chain = true)
@TableName(value = "information_schema.`COLUMNS`")
public class InfoSchemaColumn implements Serializable {

	@TableField(value = "TABLE_CATALOG")
	@Schema(description = "")
	private String tableCatalog;

	@TableField(value = "TABLE_SCHEMA")
	@Schema(description = "")
	private String tableSchema;

	@TableField(value = "`TABLE_NAME`")
	@Schema(description = "")
	private String tableName;

	@TableField(value = "`COLUMN_NAME`")
	@Schema(description = "")
	private String columnName;

	@TableField(value = "ORDINAL_POSITION")
	@Schema(description = "")
	private Long ordinalPosition;

	@TableField(value = "COLUMN_DEFAULT")
	@Schema(description = "")
	private String columnDefault;

	@TableField(value = "IS_NULLABLE")
	@Schema(description = "")
	private String isNullable;

	@TableField(value = "DATA_TYPE")
	@Schema(description = "")
	private String dataType;

	@TableField(value = "CHARACTER_MAXIMUM_LENGTH")
	@Schema(description = "")
	private Long characterMaximumLength;

	@TableField(value = "CHARACTER_OCTET_LENGTH")
	@Schema(description = "")
	private Long characterOctetLength;

	@TableField(value = "NUMERIC_PRECISION")
	@Schema(description = "")
	private Long numericPrecision;

	@TableField(value = "NUMERIC_SCALE")
	@Schema(description = "")
	private Long numericScale;

	@TableField(value = "DATETIME_PRECISION")
	@Schema(description = "")
	private Long datetimePrecision;

	@TableField(value = "`CHARACTER_SET_NAME`")
	@Schema(description = "")
	private String characterSetName;

	@TableField(value = "`COLLATION_NAME`")
	@Schema(description = "")
	private String collationName;

	@TableField(value = "COLUMN_TYPE")
	@Schema(description = "")
	private String columnType;

	@TableField(value = "COLUMN_KEY")
	@Schema(description = "")
	private String columnKey;

	@TableField(value = "EXTRA")
	@Schema(description = "")
	private String extra;

	@TableField(value = "`PRIVILEGES`")
	@Schema(description = "")
	private String privileges;

	@TableField(value = "COLUMN_COMMENT")
	@Schema(description = "")
	private String columnComment;

	@TableField(value = "GENERATION_EXPRESSION")
	@Schema(description = "")
	private String generationExpression;

	@Serial
	private static final long serialVersionUID = 1L;
}