package com.tjsj.sync.modules.tailored.xyzq.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tjsj.common.enums.base.CommonStatus;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CloudXyzqBdzqDO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description 兴业证标的证券数据结构
 * @date 2025/5/19 21:14
 */
@Schema(description = "兴业证标的证券数据结构")
@Data
@Accessors(chain = true)
@TableName(value = "credit.t_rzrq_xyzq_bdzq")
public class CloudXyzqBdzqDO implements Serializable {

	@TableId(value = "id", type = IdType.AUTO)
	@Schema(description = "")
	private Integer id;

	/**
	 * 证券代码
	 */
	@TableField(value = "sec_code")
	@Schema(description = "证券代码")
	private String secCode;

	/**
	 * 证券名称
	 */
	@TableField(value = "sec_name")
	@Schema(description = "证券名称")
	private String secName;

	/**
	 * 证券市场
	 */
	@TableField(value = "market")
	@Schema(description = "证券市场")
	private String market;

	/**
	 * 日期
	 */
	@TableField(value = "sse_date")
	@Schema(description = "日期")
	private LocalDate sseDate;

	/**
	 * 融资标的
	 */
	@TableField(value = "rzbd")
	@Schema(description = "融资标的")
	private Integer rzbd;

	/**
	 * 融资保证金比例
	 */
	@TableField(value = "rz_rate")
	@Schema(description = "融资保证金比例")
	private BigDecimal rzRate;

	/**
	 * 融券标的
	 */
	@TableField(value = "rqbd")
	@Schema(description = "融券标的")
	private Integer rqbd;

	/**
	 * 融券保证金比例
	 */
	@TableField(value = "rq_rate")
	@Schema(description = "融券保证金比例")
	private BigDecimal rqRate;

	/**
	 * 备注
	 */
	@TableField(value = "mark")
	@Schema(description = "备注")
	private String mark;

	/**
	 * 日期
	 */
	@TableField(value = "`date`")
	@Schema(description = "日期")
	private LocalDate date;

	/**
	 * 数据源
	 */
	@TableField(value = "`source`")
	@Schema(description = "数据源")
	private String source;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time")
	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time")
	@Schema(description = "更新时间")
	private LocalDateTime updateTime;

	/**
	 * 启用状态
	 */
	@TableField(value = "enable_status")
	@Schema(description = "启用状态")
	private CommonStatus enableStatus;

	/**
	 * 是否已检查market字段
	 */
	@TableField(value = "if_market_checked")
	@Schema(description = "是否已检查market字段")
	private CommonStatus ifMarketChecked;

	@Serial
	private static final long serialVersionUID = 1L;
}