package com.tjsj.sync.modules.sync.component.checker;

import cn.hutool.core.collection.CollUtil;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.common.utils.data.HashUtil;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * HashChecker
 *
 * <AUTHOR> Ye
 * @date 2025/08/01
 * @version 1.0.0
 * @description 哈希检查器
 * <p>负责检查数据哈希值变化，避免重复同步相同数据</p>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class HashChecker {

    private final SyncTableConfigService syncTableConfigService;

    /**
     * 检查数据是否发生变化
     * <p>通过比较数据哈希值来判断数据是否发生变化，避免重复同步</p>
     *
     * @param context     同步上下文
     * @param dataList    待检查的数据列表
     * @param totalDataNum     总数据量
     * @return true-数据发生变化需要同步，false-数据未变化可跳过同步
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public boolean checkDataChange(SyncContext context, List<Map<String, Object>> dataList,
                                   int totalDataNum) {
        // 1. 前置检查
        if (!shouldCheckHash(context)) {
            log.debug("⏭️ 跳过哈希检查 - 表: {}, 同步类型: {}",
                    context.getFullTableName(), context.getSyncTypeEnum());
            return true;
        }

        if (CollUtil.isEmpty(dataList)) {
            log.debug("📭 数据列表为空，跳过哈希检查 - 表: {}", context.getFullTableName());
            return false;
        }

        if (totalDataNum != dataList.size()) {
            log.debug("总数据量与当前批次数据量不一致，跳过哈希检查 - 表: {}, 总数据量: {}, 批次数据量: {}",
                    context.getFullTableName(), totalDataNum, dataList.size());
            return true;
        }

        // 2. 获取表配置
        SyncTableConfigDO tableConfig = context.getSyncTableConfig();
        if (tableConfig == null) {
            log.warn("⚠️ 表配置为空，跳过哈希检查 - 表: {}", context.getFullTableName());
            return true;
        }

        // 3. 执行哈希检查
        return performHashCheck(context, dataList, tableConfig);
    }

    /**
     * 检查是否需要进行哈希检查
     * <p>只有Quartz定时任务才需要进行哈希检查</p>
     */
    private boolean shouldCheckHash(SyncContext context) {
        return Objects.equals(context.getSyncTypeEnum(), SyncTypeEnum.QUARTZ);
    }

    /**
     * 执行哈希检查
     */
    private boolean performHashCheck(SyncContext context, List<Map<String, Object>> dataList,
                                     SyncTableConfigDO tableConfig) {

        try {
            // 1. 获取旧的哈希值
            String oldHashValue = getOldHashValue(tableConfig);

            // 2. 生成新的哈希值
            String newHashValue = generateNewHashValue(dataList);

            // 3. 更新表配置中的哈希值
            updateHashValue(tableConfig, newHashValue);

            // 4. 比较哈希值
            boolean hasChanged = !Objects.equals(oldHashValue, newHashValue);

            log.debug("🔍 哈希检查结果 - 表: {}, 旧哈希: {}, 新哈希: {}, 是否变化: {}",
                    context.getFullTableName(),
                    maskHashValue(oldHashValue),
                    maskHashValue(newHashValue),
                    hasChanged);

            return hasChanged;

        } catch (Exception e) {
            log.error("❌ 哈希检查失败 - 表: {}, 错误: {}", context.getFullTableName(), e.getMessage(), e);
            // 发生异常时，默认认为数据有变化，继续同步
            return true;
        }
    }

    /**
     * 获取旧的哈希值
     *
     * @description 从表配置中获取旧的哈希值，即上次表同步的哈希值
     * @param tableConfig 表配置
     * @return {@link String }
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    private String getOldHashValue(SyncTableConfigDO tableConfig) {
        return Optional.ofNullable(syncTableConfigService.getById(tableConfig.getId()))
                .map(SyncTableConfigDO::getQuartzLastSyncDataHash)
                .orElse(null);
    }

    /**
     * 生成新的哈希值
     */
    private String generateNewHashValue(List<Map<String, Object>> dataList) {
        return HashUtil.generateHash(dataList);
    }

    /**
     * 更新表配置中的哈希值
     */
    private void updateHashValue(SyncTableConfigDO tableConfig, String newHashValue) {
        tableConfig.setQuartzLastSyncDataHash(newHashValue);
    }

    /**
     * 掩码哈希值用于日志显示
     * <p>只显示前8位和后8位，中间用*代替</p>
     */
    private String maskHashValue(String hashValue) {
        if (hashValue == null) {
            return "null";
        }
        if (hashValue.length() <= 16) {
            return hashValue;
        }
        return hashValue.substring(0, 8) + "****" + hashValue.substring(hashValue.length() - 8);
    }

    /**
     * 获取哈希统计信息
     *
     * @param dataList 数据列表
     * @return 哈希统计信息
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public HashStatistics getHashStatistics(List<Map<String, Object>> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return new HashStatistics(0, 0, null);
        }

        String hashValue = HashUtil.generateHash(dataList);
        int totalRecords = dataList.size();
        int totalFields = dataList.get(0).size();

        return new HashStatistics(totalRecords, totalFields, hashValue);
    }

    /**
     * 哈希统计信息
     */
    public static class HashStatistics {
        private final int totalRecords;
        private final int totalFields;
        private final String hashValue;

        public HashStatistics(int totalRecords, int totalFields, String hashValue) {
            this.totalRecords = totalRecords;
            this.totalFields = totalFields;
            this.hashValue = hashValue;
        }

        public int getTotalRecords() {
            return totalRecords;
        }

        public int getTotalFields() {
            return totalFields;
        }

        public String getHashValue() {
            return hashValue;
        }

        public String getMaskedHashValue() {
            if (hashValue == null) return "null";
            if (hashValue.length() <= 16) return hashValue;
            return hashValue.substring(0, 8) + "****" + hashValue.substring(hashValue.length() - 8);
        }

        @Override
        public String toString() {
            return String.format("HashStatistics{记录数: %d, 字段数: %d, 哈希值: %s}",
                    totalRecords, totalFields, getMaskedHashValue());
        }
    }
}
