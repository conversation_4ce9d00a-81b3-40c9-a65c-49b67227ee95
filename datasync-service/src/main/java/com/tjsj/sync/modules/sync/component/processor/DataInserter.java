package com.tjsj.sync.modules.sync.component.processor;


import cn.hutool.core.collection.CollUtil;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import com.tjsj.sync.modules.sync.model.bo.SyncBatchConfig;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * DataInserter
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 数据·插入器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DataInserter {


    private final CloudDataMapper cloudDataMapper;
    private final LocalDataMapper localDataMapper;


    /**
     * 批量插入数据
     */
    public int insertData(SyncContext context, List<Map<String, Object>> dataList, SyncBatchConfig batchConfig) {

        if (!context.getIfInsert() || CollUtil.isEmpty(dataList)) {
            return 0;
        }

        log.debug("📤 开始批量插入数据 - 表: {}, 数据量: {} 条",
                context.getFullTableName(), dataList.size());

        int totalInserted = 0;
        int batchSize = batchConfig.getInsertSize();

        // 分批插入
        for (int i = 0; i < dataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, dataList.size());
            List<Map<String, Object>> batchData = new ArrayList<>(dataList.subList(i, endIndex));

            // 使用重试机制插入
            boolean success = retryInsertBatch(context, batchData, batchConfig);

            if (success) {
                totalInserted += batchData.size();
                log.debug("📤 批次插入成功 - 表: {}, 批次: {}, 数量: {} 条",
                        context.getFullTableName(), i / batchSize + 1, batchData.size());
            } else {
                log.error("❌ 批次插入失败 - 表: {}, 批次: {}",
                        context.getFullTableName(), i / batchSize + 1);
                break;
            }
        }

        log.debug("📤 批量插入完成 - 表: {}, 总插入: {} 条",
                context.getFullTableName(), totalInserted);

        return totalInserted;
    }

    /**
     * 重试插入批次数据
     * <p>执行批次数据插入，支持重试机制，并确保重试次数始终被记录到历史记录中</p>
     *
     * @param context 同步上下文
     * @param batchData 批次数据
     * @param batchConfig 批次配置
     * @return true-插入成功，false-插入失败
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    private boolean retryInsertBatch(SyncContext context, List<Map<String, Object>> batchData,
                                     SyncBatchConfig batchConfig) {

        int retryCount = 0;
        int maxRetryTimes = batchConfig.getMaxRetryTimes();
        long retryDelayMillis = batchConfig.getRetryDelayMillis();
        boolean insertSuccess = false;
        Exception lastException = null;

        log.debug("🔄 开始批次插入重试 - 表: {}, 数据量: {}, 最大重试次数: {}",
                context.getFullTableName(), batchData.size(), maxRetryTimes);

        try {
            while (retryCount < maxRetryTimes) {
                try {
                    // 批量插入数据
                    insertBatch(context, batchData);

                    insertSuccess = true;
                    log.debug("✅ 批次插入成功 - 表: {}, 重试次数: {}",
                            context.getFullTableName(), retryCount);
                    break;

                } catch (Exception e) {
                    lastException = e;
                    retryCount++;

                    log.warn("⚠️ 批次插入重试 - 表: {}, 重试次数: {}/{}, 错误: {}",
                            context.getFullTableName(), retryCount, maxRetryTimes, e.getMessage());

                    // 如果还有重试机会，则等待后重试
                    if (retryCount < maxRetryTimes) {
                        try {
                            log.debug("⏳ 等待重试 - 表: {}, 延迟: {}ms",
                                    context.getFullTableName(), retryDelayMillis);
                            Thread.sleep(retryDelayMillis);
                        } catch (InterruptedException ie) {
                            log.warn("🚫 重试等待被中断 - 表: {}", context.getFullTableName());
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        } finally {
            // 确保重试次数始终被记录到历史记录中
            recordRetryCount(context, retryCount, insertSuccess, lastException);
        }

        return insertSuccess;
    }

    /**
     * 记录重试次数到历史记录
     * <p>无论插入成功还是失败，都要记录重试次数</p>
     *
     * @param context 同步上下文
     * @param retryCount 重试次数
     * @param insertSuccess 是否插入成功
     * @param lastException 最后一次异常
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    private void recordRetryCount(SyncContext context, int retryCount, boolean insertSuccess, Exception lastException) {
        if (context.getSyncHistory() == null) {
            return;
        }

        try {
            // 记录重试次数
            context.getSyncHistory().setRetryCount(retryCount);

            // 记录详细信息到日志
            if (insertSuccess) {
                if (retryCount == 0) {
                    log.debug("📊 重试统计 - 表: {}, 首次成功，无重试", context.getFullTableName());
                } else {
                    log.info("📊 重试统计 - 表: {}, 重试 {} 次后成功", context.getFullTableName(), retryCount);
                }
            } else {
                log.error("📊 重试统计 - 表: {}, 重试 {} 次后仍失败, 最后错误: {}",
                        context.getFullTableName(), retryCount,
                        lastException != null ? lastException.getMessage() : "未知错误");
            }

        } catch (Exception e) {
            log.error("❌ 记录重试次数失败 - 表: {}, 错误: {}", context.getFullTableName(), e.getMessage(), e);
        }
    }

    /**
     * 插入单个批次
     *
     * @description 源数据库向目标数据库插入数据/目标数据库向源数据库插入数据
     */
    private void insertBatch(SyncContext context, List<Map<String, Object>> batchData) {
        SyncTableConfigDO config = context.getSyncTableConfig();
        SyncTaskTypeEnum taskType = config.getTaskType();

        if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            localDataMapper.batchInsertTableData(config, batchData);
        } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            cloudDataMapper.batchInsertTableData(config, batchData);
        }
    }

}
