package com.tjsj.sync.modules.sync.component.synchronizer;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.common.utils.data.HashUtil;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.sync.modules.sync.component.preparer.SyncPreparer;
import com.tjsj.sync.modules.sync.component.strategy.BatchSyncStrategy;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.bo.SyncPrepareResult;
import com.tjsj.sync.modules.sync.model.bo.SyncResult;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.sync.utils.MySqlKeyword;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * DataSynchronizer
 *
 * <AUTHOR> Ye
 * @date 2025/08/01
 * @version 1.0.0
 * @description 数据同步器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DataSynchronizer {

    @Resource
    private SyncPreparer syncDataPreparer;
    @Resource
    private BatchSyncStrategy batchSyncStrategy;

    /**
     * 同步表数据 - 重构版本
     *
     * <p>该方法是从原始的 batchInsertTableData 方法重构而来，
     * 保持完全相同的功能和行为，但与 SyncContext 集成。</p>
     *
     * @param context 同步上下文
     * @throws RuntimeException 如果同步过程中发生错误
     */
    public void synchronizeTableDataNew(SyncContext context) {

        // 1. 数据准备阶段
        SyncPrepareResult prepareResult = syncDataPreparer.prepareData(context);

        // 2. 执行数据同步
        SyncResult result = batchSyncStrategy.executeSyncData(prepareResult, context);

        // 3. 更新同步结果
        context.setTotalDataNum(result.getTotalDataNum());
    }

}
