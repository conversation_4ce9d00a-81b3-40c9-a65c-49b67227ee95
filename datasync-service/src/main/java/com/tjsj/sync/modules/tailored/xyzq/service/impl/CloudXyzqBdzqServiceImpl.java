package com.tjsj.sync.modules.tailored.xyzq.service.impl;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqBdzqDO;
import com.tjsj.sync.modules.tailored.xyzq.mapper.CloudXyzqBdzqMapper;
import com.tjsj.sync.modules.tailored.xyzq.service.CloudXyzqBdzqService;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 21:14
 * @description
 */

@Service
public class CloudXyzqBdzqServiceImpl extends ServiceImpl<CloudXyzqBdzqMapper, CloudXyzqBdzqDO>
		implements CloudXyzqBdzqService {

}
