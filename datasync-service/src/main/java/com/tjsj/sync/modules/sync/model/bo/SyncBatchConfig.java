package com.tjsj.sync.modules.sync.model.bo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SyncBatchConfig
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步批次配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncBatchConfig {

    /**
     * 读取批次大小
     */
    @Schema(description = "读取批次大小")
    private int readSize;

    /**
     * 插入批次大小
     */
    @Schema(description = "插入批次大小")
    private int insertSize;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数")
    private int maxRetryTimes;

    /**
     * 重试延迟时间(毫秒)
     */
    @Schema(description = "重试延迟时间(毫秒)")
    private long retryDelayMillis;


}
