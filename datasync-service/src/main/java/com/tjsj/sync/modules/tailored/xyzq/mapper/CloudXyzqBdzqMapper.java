package com.tjsj.sync.modules.tailored.xyzq.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqBdzqDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 21:14
 * @description
 */
@Mapper
@DS(DataSourceNames.SOURCE_DB)
public interface CloudXyzqBdzqMapper extends BaseMapper<CloudXyzqBdzqDO> {
}