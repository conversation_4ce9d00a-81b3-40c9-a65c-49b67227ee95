package com.tjsj.sync.modules.sync.controller;

import com.tjsj.common.annotation.PassLogInfo;
import com.tjsj.common.annotation.SchedulerSwitchControl;
import com.tjsj.common.annotation.ServiceSwitchControl;
import com.tjsj.common.annotation.UpdateAnnotation;
import com.tjsj.common.annotation.lock.SingleInstanceLock;
import com.tjsj.modules.manage.service.HeartBeatTestService;
import com.tjsj.modules.manage.service.LocalHeartBeatTestService;
import com.tjsj.sync.modules.sync.service.DataSyncService;
import com.tjsj.sync.modules.sync.utils.quartz.DynamicTaskScheduler;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * DatabaseSyncController
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2024/07/10
 * @description 数据库同步
 */
@Tag(name = "DatabaseSyncController", description = "数据库同步")
@RestController
@RequestMapping("database-sync")
@RequiredArgsConstructor
@Slf4j
//@ConditionalOnProperty(prefix = "scheduling", name = "enabled", havingValue = "true")
public class DatabaseSyncController {

    private final HeartBeatTestService heartBeatTestService;


    private final DataSyncService dataSyncService;


    /**
     * 定时任务-同步阿里云数据库数据到本地数据库
     *
     * 🎯 注解说明：
     * - @Scheduled: 定时任务核心注解，每60秒执行一次
     * - @UpdateAnnotation: 提供日志记录和跳过token认证
     * - @SingleInstanceLock: 分布式锁，防止多实例重复执行
     * - @SchedulerSwitchControl: 定时任务开关控制，支持动态启停
     */
    @Operation(summary = "定时任务-同步阿里云数据库数据到本地数据库")
    @Scheduled(cron = "${tarkin.scheduler.cron.sync-cloud-to-local}")
    @Async(value = "asyncExecutor")
    @UpdateAnnotation
    @SingleInstanceLock()
    @ServiceSwitchControl
    @SchedulerSwitchControl(value = "同步阿里云数据库数据到本地数据库", scheduled = true)
    public void syncCloudToLocalScheduler() {
        dataSyncService.syncCloudToLocal(true, null);
    }

    /**
     * Http接口-同步阿里云数据库数据到本地数据库
     *
     * 🎯 注解说明：
     * - @GetMapping: HTTP GET接口
     * - @UpdateAnnotation: 提供日志记录和跳过token认证
     * - @SingleInstanceLock: 分布式锁，防止并发执行
     * - @ServiceSwitchControl: 服务开关控制，支持动态启停
     *
     */
    @Operation(summary = "Http接口-同步阿里云数据库数据到本地数据库")
    @GetMapping("/syncCloudToLocal")
    @Async(value = "asyncExecutor")
    @UpdateAnnotation
    @SingleInstanceLock()
    @ServiceSwitchControl
    public void syncCloudToLocal() {
        dataSyncService.syncCloudToLocal(true, null);
    }


    /**
     * 同步阿里云数据库数据到本地数据库测试多线程
     *
     * 🎯 功能说明：
     * - 支持指定配置ID列表进行部分同步
     * - 为空则同步所有配置
     * - 主要用于测试和调试场景
     *
     * @param configIds 同步配置ID列表，为空则同步所有配置
     * @return 测试同步操作的结果信息
     */
    @Operation(summary = "同步阿里云数据库数据到本地数据库测试多线程",
            parameters = {@Parameter(name = "configIds", description = "同步配置ID列表，为空则同步所有配置")})
    @GetMapping("syncCloudToLocalTest")
    @UpdateAnnotation
    @SingleInstanceLock()
    @ServiceSwitchControl
    public String syncCloudToLocalTest(@RequestParam(required = false) List<Integer> configIds) {
        dataSyncService.syncCloudToLocal(true, configIds);
        String configInfo = configIds != null && !configIds.isEmpty()
            ? "指定配置ID: " + configIds
            : "所有配置";
        return "测试同步任务已启动 (" + configInfo + ")，请查看日志获取详细执行情况 🧪";
    }

    /**
     * 手动同步数据
     *
     * 🎯 功能说明：
     * - 手动触发数据同步操作
     * - 通常用于紧急数据修复或补偿同步
     *
     * @return 手动同步操作的结果信息
     */
    @Operation(summary = "手动同步数据")
    @GetMapping("/syncManual")
    @UpdateAnnotation
    @SingleInstanceLock()
    @ServiceSwitchControl
    public String syncManual() {
        dataSyncService.syncManual();
        return "手动同步任务已启动，请查看日志获取详细执行情况 🔧";
    }

    @Operation(summary = "数据库心跳测试")
    @GetMapping("/testCloudDatabaseHeartBeat")
    // 定时心跳测试，默认每5秒执行一次
    @Scheduled(cron = "${tarkin.scheduler.cron.heartbeat-test:0/5 * * * * ?}")
    @PassLogInfo
    @SingleInstanceLock()
    @SchedulerSwitchControl(scheduled = true, enabled = false)
    public void testCloudDatabaseHeartBeat() {

        //云数据库心跳测试
        if (!heartBeatTestService.testHeartBeat()) {
            dataSyncService.syncManual();
            heartBeatTestService.updateHeartBeatStatus();
        }

        //本地数据库心跳测试
//        if (!localHeartBeatTestService.testHeartBeat()) {
////            dataSyncService.transferLocalSyncManualToCloud();
//            dataSyncService.syncManual();
//            localHeartBeatTestService.updateHeartBeatStatus();
//        }

    }


}
