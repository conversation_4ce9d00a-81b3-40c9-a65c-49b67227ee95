package com.tjsj.sync.modules.sync.component.strategy;


import cn.hutool.core.thread.ThreadUtil;
import com.tjsj.sync.modules.sync.component.processor.DataExtractor;
import com.tjsj.sync.modules.sync.component.processor.DataInserter;
import com.tjsj.sync.modules.sync.component.processor.DataPipeline;
import com.tjsj.sync.modules.sync.component.checker.HashChecker;
import com.tjsj.sync.modules.sync.model.bo.SyncBatchConfig;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.bo.SyncPrepareResult;
import com.tjsj.sync.modules.sync.model.bo.SyncResult;
import com.tjsj.sync.modules.sync.service.DistributedSyncService;
import com.tjsj.sync.modules.sync.config.SeataConnectionMonitor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * BatchSyncStrategy
 *
 * <AUTHOR> Ye
 * @date 2025/08/01
 * @version 1.0.0
 * @description 批量同步策略
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class BatchSyncStrategy {

    private final DataExtractor dataExtractor;
    private final DataPipeline dataPipeline;
    private final HashChecker hashChecker;
    private final DataInserter dataInserter;

    // 分布式事务服务（可选，仅在启用Seata时注入）
    @Autowired(required = false)
    private DistributedSyncService distributedSyncService;

    // Seata连接监控器（可选，仅在启用Seata时注入）
    @Autowired(required = false)
    private SeataConnectionMonitor seataConnectionMonitor;

    @Value("${seata.enabled:false}")
    private boolean seataEnabled;

    @Value("${seata.degradation.auto-fallback-enabled:true}")
    private boolean autoFallbackEnabled;


    /**
     * 执行同步数据 - 支持分布式事务
     *
     * @param prepareResult 准备同步结果
     * @param context 同步上下文
     * @return 同步结果
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {

        String tableName = context.getFullTableName();

        // 检查是否启用Seata且服务可用
        if (seataEnabled && distributedSyncService != null) {

            // 如果有连接监控器且启用了自动降级，先检查连接状态
            if (seataConnectionMonitor != null && autoFallbackEnabled) {

                if (seataConnectionMonitor.isSeataServerAvailable()) {

                    log.info("🌐 使用Seata分布式事务执行同步 - 表: {}", tableName);
                    log.debug("🔍 Seata状态: {}", seataConnectionMonitor.getStatusDescription());
                    return distributedSyncService.executeDistributedSync(prepareResult, context);
                } else {

                    log.warn("⚠️ Seata Server不可用，直接使用本地事务 - 表: {}", tableName);
                    log.warn("🔍 Seata状态: {}", seataConnectionMonitor.getStatusDescription());
                    log.warn("🔧 降级配置: {}", seataConnectionMonitor.getDegradationConfig());
                    return executeLocalSync(prepareResult, context);
                }
            } else {

                // 没有连接监控器或禁用了自动降级，直接尝试分布式事务
                String mode = autoFallbackEnabled ? "带自动降级" : "无降级";
                log.info("🌐 使用Seata分布式事务执行同步（{}） - 表: {}", mode, tableName);
                return distributedSyncService.executeDistributedSync(prepareResult, context);
            }
        } else {

            log.info("📦 使用本地事务执行同步 - 表: {}", tableName);
            return executeLocalSync(prepareResult, context);
        }
    }

    /**
     * 本地事务同步（原有逻辑保留）
     */
    private SyncResult executeLocalSync(SyncPrepareResult prepareResult, SyncContext context) {
        log.debug("📦 执行批量同步策略 - 表: {}", context.getFullTableName());

        SyncBatchConfig batchConfig = prepareResult.getBatchConfig();
        LocalDateTime targetMaxTime = prepareResult.getTargetMaxTime();

        // 总插入数据量
        int totalDataNum = 0;
        // 批次计数
        int batchCount = 0;
        // 偏移量
        int offset = 0;

        List<Map<String, Object>> dataList;
        do {
            // 1. 数据提取
            dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchConfig.getReadSize());

            log.debug("📥 本批次提取数据: {} 条 - 表: {}, 偏移: {}",
                    dataList.size(), context.getFullTableName(), offset);

            // 2. 数据处理流水线
            dataList = dataPipeline.process(context, dataList);

            // 3. 哈希检查
            if (!hashChecker.checkDataChange(context, dataList, totalDataNum)) {
                if (dataList.size() != batchConfig.getReadSize()) {
                    log.debug("🔍 数据哈希值未变化且已到最后一批，结束同步 - 表: {}", context.getFullTableName());
                    break;
                }
            }

            // 4. 数据插入
            if (!dataList.isEmpty()) {
                int insertedCount = dataInserter.insertData(context, dataList, batchConfig);
                totalDataNum += insertedCount;
                batchCount++;

                log.debug("📤 本批次插入数据: {} 条 - 表: {}", insertedCount, context.getFullTableName());
            }

            offset += batchConfig.getReadSize();

        } while (dataList.size() == batchConfig.getReadSize());

        // TODO:
//        log.warn("睡眠中++++++++++++++++++++++++++");
//        ThreadUtil.sleep(100000, TimeUnit.MINUTES);

        return SyncResult.builder()
                .totalDataNum(totalDataNum)
                .batchCount(batchCount)
                .success(true)
                .build();


    }


}
