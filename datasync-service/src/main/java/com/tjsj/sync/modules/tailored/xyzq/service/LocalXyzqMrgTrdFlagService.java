package com.tjsj.sync.modules.tailored.xyzq.service;

import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqMrgTrdFlagDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 20:46
 * @description
 */

public interface LocalXyzqMrgTrdFlagService extends IService<LocalXyzqMrgTrdFlagDO> {


	/**
	 * 批量插入
	 *
	 * @param localXyzqMrgTrdFlagIncreList 本地xyzq先生坚固判断参数增量列表
	 * <AUTHOR> Ye
	 * @date 2025/05/20
	 */
	void insertBatchSomeColumn(List<LocalXyzqMrgTrdFlagDO> localXyzqMrgTrdFlagIncreList);

}
