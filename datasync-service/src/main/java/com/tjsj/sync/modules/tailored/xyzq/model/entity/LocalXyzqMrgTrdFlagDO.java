package com.tjsj.sync.modules.tailored.xyzq.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 20:46
 * @description 兴业证券官网-数据表爬虫采集标志
 */
@Schema(description = "兴业证券官网-数据表爬虫采集标志")
@Data
@Accessors(chain = true)
@TableName(value = "credit.t_xyzq_rzrq_flag")
public class LocalXyzqMrgTrdFlagDO implements Serializable {

	@TableId(value = "id", type = IdType.AUTO)
	@Schema(description = "")
	private Integer id;

	/**
	 * 日期
	 */
	@TableField(value = "`date`")
	@Schema(description = "日期")
	private LocalDate date;

	/**
	 * 表名
	 */
	@TableField(value = "`table_name`")
	@Schema(description = "表名")
	private String tableName;

	/**
	 * 标志
	 */
	@TableField(value = "flag")
	@Schema(description = "标志")
	private Integer flag;

	/**
	 * 数据源
	 */
	@TableField(value = "`source`")
	@Schema(description = "数据源")
	private String source;

	/**
	 * 创建时间
	 */
	@TableField(value = "create_time")
	@Schema(description = "创建时间")
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	@TableField(value = "update_time")
	@Schema(description = "更新时间")
	private LocalDateTime updateTime;

	@Serial
	private static final long serialVersionUID = 1L;
}