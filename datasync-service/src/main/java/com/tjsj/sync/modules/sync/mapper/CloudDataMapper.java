package com.tjsj.sync.modules.sync.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.base.model.vo.TypeCountVO;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.business.model.FinancialBaseModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * CloudDataMapper
 *
 * <AUTHOR> Ye
 * @date 2024/08/01
 * @description 云数据映射器
 */
@Mapper
@DS(DataSourceNames.SOURCE_DB)
public interface CloudDataMapper {


	/**
	 * 根据数据库同步表查询数据
	 *
	 * @param tableConfig 表配置
	 * @param updateTime 更新时间
	 * @param offset 偏移量
	 * @param batchSize 批量大小
	 * @param quartzSyncType 是否是quartz同步
	 * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
	 * <AUTHOR> Ye
	 * @date 2024/08/01
	 */
	List<Map<String, Object>> selectTableDataByDataRange(@Param("tableConfig") SyncTableConfigDO tableConfig,
	                                                     @Param("updateTime") LocalDateTime updateTime,
	                                                     @Param("offset") int offset,
	                                                     @Param("batchSize") int batchSize,
	                                                     @Param("quartzSyncType") boolean quartzSyncType);

	/**
	 * 选择表数据通过数据范围测试
	 *
	 * @param tableConfig 表配置
	 * @param offset      抵消
	 * @param batchSize   批量大小
	 * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
	 * <AUTHOR> Ye
	 * @date 2024/10/10
	 */
	List<Map<String, Object>> selectTableDataByDataRangeTest(@Param("table") SyncTableConfigDO tableConfig,
	                                                         @Param("offset") int offset,
	                                                         @Param("batchSize") int batchSize);

	/**
	 * 根据数据库同步表查询数据
	 *
	 * @param tableConfig 同步表配置
	 * @param id          主键id
	 * @return List<Map < String, Object>>
	 * <AUTHOR> Ye
	 * @date 2024/08/07
	 */
	List<Map<String, Object>> selectTableDataById(@Param("table") SyncTableConfigDO tableConfig, @Param("id") Long id);

	List<Map<String, Object>> selectTableDataByCondition(@Param("syncInfo") DataSyncManualDO syncInfo, @Param(
			"offset") int offset, @Param("batchSize") int batchSize);

	/**
	 * 执行sql到云端
	 *
	 * @param executeSql 执行sql
	 * <AUTHOR> Ye
	 * @date 2024/07/30
	 */
	void executeSqlToCloud(@Param("executeSql") String executeSql);

	/**
	 * 批量插入表数据通过条件
	 *
	 * @param syncInfo  同步信息
	 * @param cloudData 云数据
	 * <AUTHOR> Ye
	 * @date 2024/07/30
	 */
	void batchInsertTableDataByCondition(@Param("syncInfo") DataSyncManualDO syncInfo,
	                                     @Param("dataList") List<Map<String, Object>> cloudData);

	/**
	 * 截断表数据
	 *
	 * @param deleteTableName 同步信息
	 * <AUTHOR> Ye
	 * @date 2024/07/30
	 */
	void truncateTableData(@Param("deleteTableName") String deleteTableName);

	/**
	 * 删除表数据通过条件
	 *
	 * @param deleteTableName       删除表名称
	 * @param deleteTableCondition  删除表条件
	 * @param deleteStartId         删除开始id
	 * @param deleteStartUpdateTime 删除开始更新时间
	 * <AUTHOR> Ye
	 * @date 2024/07/30
	 */
	void deleteTableDataByCondition(@Param("deleteTableName") String deleteTableName,
	                                @Param("deleteTableCondition") String deleteTableCondition,
	                                @Param("deleteStartId") Integer deleteStartId,
	                                @Param("deleteStartUpdateTime") LocalDateTime deleteStartUpdateTime);


	/**
	 * 列表表的列
	 *
	 * @param schemaName 架构名称
	 * @param tableName  表名称
	 * @return {@link List }<{@link String }>
	 * <AUTHOR> Ye
	 * @date 2024/10/10
	 */
	List<String> listTableColumns(@Param("schemaName") String schemaName, @Param("tableName") String tableName);

	/**
	 * 截断插入表数据
	 *
	 * @param tableConfig 表配置
	 * <AUTHOR> Ye
	 * @date 2024/10/16
	 */
	void truncateInsertTableData(@Param("tableConfig") SyncTableConfigDO tableConfig);

	/**
	 * 获取表最大值更新时间
	 *
	 * @param tableConfig 表配置
	 * @return {@link LocalDateTime }
	 * <AUTHOR> Ye
	 * @date 2024/10/16
	 */
	LocalDateTime getTableMaxUpdateTime(@Param("tableConfig") SyncTableConfigDO tableConfig);

	/**
	 * 批量插入表数据，对主键重复数据进行更新操作
	 *
	 * @param tableConfig 表配置
	 * @param dataList    数据列表
	 * <AUTHOR> Ye
	 * @date 2024/07/11
	 */
	void batchInsertTableData(@Param("tableConfig") SyncTableConfigDO tableConfig,
	                          @Param("dataList") List<Map<String, Object>> dataList);

	/**
	 * 获取表分组后的不同分组的数量
	 *
	 * @param dataSchemaName     数据来源的数据库名
	 * @param dataTableName      数据来源的表名
	 * @param tableGroupByColumn 分组字段
	 * @return {@link List }<{@link FinancialBaseModel }>
	 * <AUTHOR> Ye
	 * @date 2025/04/08
	 */
	List<TypeCountVO> getTableGroupByDataCount(@Param("dataSchemaName") String dataSchemaName,
	                                           @Param("dataTableName") String dataTableName,
	                                           @Param("tableGroupByColumn") String tableGroupByColumn);


	/**
	 * 获取财务数据表数据
	 *
	 * @param groupByTierOneColumnValue 一级表分组字段值
	 * @param groupByTierTwoColumnValue 二级表分组字段值
	 * @param dataSchemaName            架构名称
	 * @param dataTableName             表名称
	 * @param offset                    抵消
	 * @param pullBatchSize             拉批量大小
	 * @param tableGroupByColumnList    分组字段列表
	 * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
	 * <AUTHOR> Ye
	 * @date 2025/04/08
	 */
	List<Map<String, Object>> selectFinancialTableDataByOffset(@Param("groupByTierOneColumnValue") String groupByTierOneColumnValue,
	                                                           @Param("groupByTierTwoColumnValue") String groupByTierTwoColumnValue,
	                                                           @Param("dataSchemaName") String dataSchemaName,
	                                                           @Param("dataTableName") String dataTableName,
	                                                           @Param("offset") Integer offset,
	                                                           @Param("pullBatchSize") Integer pullBatchSize,
	                                                           @Param("tableGroupByColumnList") List<String> tableGroupByColumnList);


	/**
	 * 获取表分组通过数据统计层级二
	 *
	 * @param filteredGroupByColumnValue 过滤分组通过列值
	 * @param dataSchemaName             数据提要名称
	 * @param dataTableName              数据表名称
	 * @param tableGroupByColumnTierOne  一级表分组字段
	 * @param tableGroupByColumnTierTwo  二级分组字段
	 * @return {@link List }<{@link TypeCountVO }>
	 * <AUTHOR> Ye
	 * @date 2025/04/15
	 */
	List<TypeCountVO> getTableGroupByDataCountTierTwo(@Param("filteredGroupByColumnValue") String filteredGroupByColumnValue,
	                                                  @Param("dataSchemaName") String dataSchemaName,
	                                                  @Param("dataTableName") String dataTableName,
	                                                  @Param("tableGroupByColumnTierOne") String tableGroupByColumnTierOne,
	                                                  @Param("tableGroupByColumnTierTwo") String tableGroupByColumnTierTwo);

	/**
	 * 获取表字段名和值列表
	 *
	 * @param dataSchemaName 数据架构名称
	 * @param dataTableName  数据表名称
	 * @return {@link List }<{@link String }>
	 * <AUTHOR> Ye
	 * @date 2025/05/08
	 */
	List<String> getFieldNameValueList(@Param("dataSchemaName") String dataSchemaName,
	                                   @Param("dataTableName") String dataTableName);

	/**
	 * 测试删除分布锁
	 *
	 *
	 * @return int
	 * <AUTHOR> Ye
	 * @date 2025/07/02
	 */
	int testDeleteDistributedLock();
}

