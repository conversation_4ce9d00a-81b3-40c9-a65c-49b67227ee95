package com.tjsj.sync.modules.sync.component.preparer;


import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tjsj.common.config.SyncConfigProperties;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.common.utils.date.DateUtils;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import com.tjsj.sync.modules.sync.model.bo.SyncBatchConfig;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.bo.SyncPrepareResult;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * SyncPreparer
 *
 * <AUTHOR> Ye
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步准备器
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SyncPreparer {

    private final SyncTableConfigService syncTableConfigService;

    private final TarkinConfig tarkinConfig;

    private final SyncConfigProperties syncConfigProperties;

    private final LocalDataMapper localDataMapper;

    private final CloudDataMapper cloudDataMapper;

    /**
     * 同步单张表之前的准备工作
     *
     * @description 同步单张表之前的准备工作，包括更新同步状态、创建同步历史记录、设置开始时间
     * @param context 上下文
     *
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public void prepare(SyncContext context) {
        // 更新同步状态
        updateSyncStatus(context);
        // 创建同步历史记录
        createSyncHistory(context);
        // 设置开始时间
        context.setStartTime(System.currentTimeMillis());
    }

    /**
     * 更新同步状态
     *
     * @description 将同步配置表的同步状态设置为正在同步、上次同步开始时间设置为当前时间
     * @param context 上下文
     *
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public void updateSyncStatus(SyncContext context) {
        LocalDateTime now = LocalDateTime.now();
        syncTableConfigService.update(Wrappers.<SyncTableConfigDO>lambdaUpdate()
                .set(SyncTableConfigDO::getIfSyncing, CommonStatus.ENABLE)
                .set(SyncTableConfigDO::getLastSyncStartTime, now)
                .set(SyncTableConfigDO::getLastSyncHeartBeatTime, now) // 初始化心跳时间
                .eq(SyncTableConfigDO::getId, context.getSyncTableConfig().getId()));

        log.debug("🔄 更新同步状态 - 表: {}, 状态: 同步中, 初始化心跳时间: {}",
                context.getFullTableName(), now);
    }

    /**
     * 创建同步历史记录
     *
     * @description 创建同步历史记录，并将同步历史记录ID设置到上下文中
     * @param context 上下文
     *
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public void createSyncHistory(SyncContext context) {
        DatabaseSyncHistoryDO syncHistory = createSyncHistoryRecord(
                context.getTaskId(), null, context.getSyncTableConfig(), context.getSyncTypeEnum());
        context.setSyncHistory(syncHistory);
    }

    /**
     * 创建同步历史记录
     *
     * @description 创建同步历史记录
     * @param taskId 任务ID
     * @param syncManual 手动同步信息
     * @param tableConfig 同步配置信息
     * @param syncTypeEnum 同步类型
     * @return 同步历史记录
     *
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public DatabaseSyncHistoryDO createSyncHistoryRecord(Integer taskId, DataSyncManualDO syncManual,
                                                         SyncTableConfigDO tableConfig, SyncTypeEnum syncTypeEnum) {

        DatabaseSyncHistoryDO databaseSyncHistory = new DatabaseSyncHistoryDO()
                .setDate(DateUtils.format(new Date(), DateUtils.DATE_PATTERN))
                .setStartTime(LocalDateTime.now())
                .setProjectId(syncManual == null ? tableConfig.getProjectId() : syncManual.getProjectId())
                .setDbType(EnumUtil.getBy(EnvironmentTypeEnum::getCode, tarkinConfig.getDbType()))
                .setProfileType(tarkinConfig.getProfile())
                .setSyncType(syncTypeEnum != null ?
                        syncTypeEnum : (syncManual != null ? SyncTypeEnum.MANUAL : SyncTypeEnum.AUTO))
                .setTaskType(syncManual != null ? syncManual.getTaskType() : tableConfig.getTaskType())
                .setTaskId(taskId);

        if (syncManual != null) {
            // 设置插入表名
            String insertTableName = syncManual.getInsertTableName();
            if (StrUtil.isNotEmpty(insertTableName)) {
                // 取出数据库名和表名
                String[] split = insertTableName.split("\\.");
                String databaseName = split[0];
                String tableName = split[1];
                databaseSyncHistory.setDataSchemaName(databaseName)
                        .setDataTableName(tableName);
                if (StrUtil.isNotEmpty(syncManual.getDeleteTableName())) {
                    String[] deleteSplit = syncManual.getDeleteTableName().split("\\.");
                    String deleteDatabaseName = deleteSplit[0];
                    String deleteTableName = deleteSplit[1];
                    databaseSyncHistory.setSchemaName(deleteDatabaseName)
                            .setTableName(deleteTableName);
                }
            } else if (StrUtil.isNotEmpty(syncManual.getExecuteSql())) {
                // 将执行语句设置到同步历史日志中
                databaseSyncHistory.setExecuteSql(syncManual.getExecuteSql());
            }
        } else {
            databaseSyncHistory.setSchemaName(tableConfig.getSchemaName())
                    .setTableName(tableConfig.getTableName())
                    .setDataSchemaName(tableConfig.getDataSchemaName())
                    .setDataTableName(tableConfig.getDataTableName());
        }


        return databaseSyncHistory;
    }


    /**
     * 准备数据
     */
    public SyncPrepareResult prepareData(SyncContext context) {
        log.debug("🔧 准备同步数据 - 表: {}", context.getFullTableName());

        // 1. 处理全量更新
        boolean fullUpdateExecuted = handleFullUpdate(context);

        // 2. 获取目标表最大时间
        LocalDateTime targetMaxTime = getTargetTableMaxTime(context);

        // 3. 解析批次配置
        SyncBatchConfig batchConfig = resolveBatchConfig(context);

        return SyncPrepareResult.builder()
                .targetMaxTime(targetMaxTime)
                .batchConfig(batchConfig)
                .fullUpdateExecuted(fullUpdateExecuted)
                .build();
    }


    /**
     * 处理全量更新
     */
    private boolean handleFullUpdate(SyncContext context) {
        SyncTableConfigDO config = context.getSyncTableConfig();
        Boolean ifInsert = context.getIfInsert();

        if (config.getIfFullUpdate().equals(CommonStatus.ENABLE) && Boolean.TRUE.equals(ifInsert)) {
            truncateTargetTable(context);
            log.debug("🗑️ 执行全量更新，清空目标表 - 表: {}", context.getFullTableName());
            return true;
        }

        return false;
    }

    /**
     * 清空目标表
     */
    private void truncateTargetTable(SyncContext context) {
        SyncTableConfigDO config = context.getSyncTableConfig();
        SyncTaskTypeEnum taskType = config.getTaskType();

        if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            localDataMapper.truncateInsertTableData(config);
        } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            cloudDataMapper.truncateInsertTableData(config);
        }
    }

    /**
     * 获取目标表最大更新时间
     */
    private LocalDateTime getTargetTableMaxTime(SyncContext context) {
        SyncTableConfigDO config = context.getSyncTableConfig();
        SyncTaskTypeEnum taskType = config.getTaskType();

        if (config.getTargetMaxTime() != null) {
            if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
                return config.getTargetMaxTime();
            } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
                return config.getSourceMaxTime();
            }
        } else {
            if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
                return localDataMapper.getTableMaxUpdateTime(config);
            } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
                return cloudDataMapper.getTableMaxUpdateTime(config);
            }
        }


        return null;
    }

    /**
     * 解析批次配置
     */
    private SyncBatchConfig resolveBatchConfig(SyncContext context) {
        SyncTableConfigDO config = context.getSyncTableConfig();

        return SyncBatchConfig.builder()
                .readSize(
                        config.getReadSize() != null ? config.getReadSize() : syncConfigProperties.getAutoPullBatchSize())
                .insertSize(
                        config.getBatchSize() != null ? config.getBatchSize() : syncConfigProperties.getAutoInsertBatchSize())
                .maxRetryTimes(syncConfigProperties.getAutoMaxRetryTimes())
                .retryDelayMillis(syncConfigProperties.getAutoRetryDelayMillis())
                .build();
    }

}
