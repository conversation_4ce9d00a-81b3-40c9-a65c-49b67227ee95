package com.tjsj.sync.modules.sync.component.checker;


import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * SyncPreChecker
 *
 * <AUTHOR> Ye
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步预检查器
 * <p>负责在数据同步前进行各种必要的检查，确保同步条件满足</p>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SyncPreChecker {

    private final SyncTableConfigService syncTableConfigService;

    private final TimeComparisonService timeComparisonService;


    /**
     * 判断表是否可以进行同步
     *
     * @param context 同步上下文
     * @return true：可以同步，false：不可以同步
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public boolean checkSyncable(SyncContext context) {
        return checkTableSyncable(context.getSyncTableConfig().getId()) && checkTimeComparison(context);
    }

    /**
     * 判断表是否需要进行时间比较
     *
     * @param context 同步上下文
     * @return true：需要进行时间比较，false：不需要进行时间比较
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    private boolean checkTimeComparison(SyncContext context) {
        SyncTableConfigDO config = context.getSyncTableConfig();
        if (config.getIfCompareMaxTime() == CommonStatus.ENABLE) {
            Boolean ifContinueSync = timeComparisonService.compareTableMaxTime(config);
            if (!ifContinueSync) {
                log.info("表 {} 的不需要进行同步，跳过", context.getFullTableName());
                return false;
            }
        }
        return true;
    }

    /**
     * 判断表是否可以进行同步
     *
     * @param syncTableConfigId  同步表配置ID
     * @return true：可以同步，false：不可以同步
     */
    public boolean checkTableSyncable(Integer syncTableConfigId) {
        SyncTableConfigDO syncTableConfigDO = syncTableConfigService.getById(syncTableConfigId);
        CommonStatus ifSyncing = syncTableConfigDO.getIfSyncing();
        LocalDateTime lastSyncHeartBeatTime = syncTableConfigDO.getLastSyncHeartBeatTime();

        // 如果表配置当前未在同步，则可以进行同步
        if (ifSyncing == CommonStatus.DISABLE) {
            log.debug("表 {} 未在同步中，允许同步",
                    syncTableConfigDO.getSchemaName() + "." + syncTableConfigDO.getTableName());
            return true;
        }

        // 如果表正在同步，检查心跳时间
        if (lastSyncHeartBeatTime == null) {
            // 如果没有心跳时间记录，使用原来的逻辑（10小时超时）
            LocalDateTime lastSyncStartTime = syncTableConfigDO.getLastSyncStartTime();
            boolean tableSyncable = lastSyncStartTime != null &&
                    lastSyncStartTime.isBefore(LocalDateTime.now().minusHours(10));
            if (!tableSyncable) {
                log.debug("表 {} 正在同步且无心跳记录，最后同步时间未超过10小时，不允许同步",
                        syncTableConfigDO.getSchemaName() + "." + syncTableConfigDO.getTableName());
                return false;
            }
            log.debug("表 {} 正在同步但无心跳记录，最后同步时间超过10小时，允许同步",
                    syncTableConfigDO.getSchemaName() + "." + syncTableConfigDO.getTableName());
            return true;
        }

        // 检查心跳时间是否超过20秒
        LocalDateTime heartBeatThreshold = LocalDateTime.now().minusSeconds(20);
        if (lastSyncHeartBeatTime.isBefore(heartBeatThreshold)) {
            log.debug("表 {} 心跳超时（超过20秒），同步服务可能已死，允许同步。最后心跳时间: {}",
                    syncTableConfigDO.getSchemaName() + "." + syncTableConfigDO.getTableName(),
                    lastSyncHeartBeatTime);
            return true;
        }

        log.debug("表 {} 正在同步且心跳正常（{}），不允许同步",
                syncTableConfigDO.getSchemaName() + "." + syncTableConfigDO.getTableName(),
                lastSyncHeartBeatTime);
        return false;
    }


}
