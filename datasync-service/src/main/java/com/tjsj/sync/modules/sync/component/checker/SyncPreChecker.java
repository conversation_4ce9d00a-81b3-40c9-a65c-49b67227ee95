package com.tjsj.sync.modules.sync.component.checker;


import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * SyncPreChecker
 *
 * <AUTHOR> Ye
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步预检查器
 * <p>负责在数据同步前进行各种必要的检查，确保同步条件满足</p>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SyncPreChecker {

    private final SyncTableConfigService syncTableConfigService;

    private final TimeComparisonService timeComparisonService;


    /**
     * 判断表是否可以进行同步
     *
     * @param context 同步上下文
     * @return true：可以同步，false：不可以同步
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public boolean checkSyncable(SyncContext context) {
        return checkTableSyncable(context.getSyncTableConfig().getId()) && checkTimeComparison(context);
    }

    /**
     * 判断表是否需要进行时间比较
     *
     * @param context 同步上下文
     * @return true：需要进行时间比较，false：不需要进行时间比较
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    private boolean checkTimeComparison(SyncContext context) {
        SyncTableConfigDO config = context.getSyncTableConfig();
        if (config.getIfCompareMaxTime() == CommonStatus.ENABLE) {
            Boolean ifContinueSync = timeComparisonService.compareTableMaxTime(config);
            if (!ifContinueSync) {
                log.info("表 {} 的不需要进行同步，跳过", context.getFullTableName());
                return false;
            }
        }
        return true;
    }

    /**
     * 判断表是否可以进行同步
     *
     * @param syncTableConfigId  同步表配置ID
     * @return true：可以同步，false：不可以同步
     */
    public boolean checkTableSyncable(Integer syncTableConfigId) {
        SyncTableConfigDO syncTableConfigDO = syncTableConfigService.getById(syncTableConfigId);
        CommonStatus ifSyncing = syncTableConfigDO.getIfSyncing();
        LocalDateTime lastSyncStartTime = syncTableConfigDO.getLastSyncStartTime();
        // 如果1.表配置当前未在同步，或者2.最后一次同步时间超过10小时，则可以进行同步
        boolean tableSyncable = ifSyncing == CommonStatus.DISABLE ||
                (lastSyncStartTime != null && lastSyncStartTime.isBefore(LocalDateTime.now().minusHours(10)));
        if (!tableSyncable) {
            log.debug("表 {} 的配置不允许同步，跳过",
                    syncTableConfigDO.getSchemaName() + "." + syncTableConfigDO.getTableName());
            return false;
        }
        return true;
    }


}
