package com.tjsj.sync.modules.sync.component.processor;


import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * DataExtractor
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 数据提取器
 */
@Component
@Data
@Accessors
@Schema(description = "数据提取器")
@Slf4j
public class DataExtractor {

    private final CloudDataMapper cloudDataMapper;
    private final LocalDataMapper localDataMapper;


    /**
     * 从源数据库中提取数据
     *
     * @description
     * @param context 上下文
     * @param updateTime 更新时间
     * @param offset 偏移量
     * @param batchSize 批量大小
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public List<Map<String, Object>> extractData(SyncContext context,
                                                 LocalDateTime updateTime,
                                                 int offset,
                                                 int batchSize) {

        SyncTableConfigDO config = context.getSyncTableConfig();
        SyncTaskTypeEnum taskType = config.getTaskType();
        SyncTypeEnum syncType = context.getSyncTypeEnum();
        boolean quartzSyncType = Objects.equals(syncType, SyncTypeEnum.QUARTZ);

        log.debug("📥 提取数据 - 表: {}, 偏移: {}, 批次: {}", context.getFullTableName(), offset, batchSize);

        List<Map<String, Object>> dataList;

        if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            dataList = cloudDataMapper.selectTableDataByDataRange(config, updateTime, offset, batchSize,
                    quartzSyncType);
        } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            dataList = localDataMapper.selectTableDataByDataRange(config, updateTime, offset, batchSize,
                    quartzSyncType);
        } else {
            dataList = new ArrayList<>();
        }

        log.debug("📥 数据提取完成 - 表: {}, 提取数量: {} 条", context.getFullTableName(), dataList.size());

        return dataList;
    }

}
