package com.tjsj.sync.modules.tailored.xyzq.service.impl;

import org.springframework.stereotype.Service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqBdzqDO;
import com.tjsj.sync.modules.tailored.xyzq.mapper.LocalXyzqBdzqMapper;
import com.tjsj.sync.modules.tailored.xyzq.service.LocalXyzqBdzqService;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 21:17
 * @description
 */
@Service
public class LocalXyzqBdzqServiceImpl extends ServiceImpl<LocalXyzqBdzqMapper, LocalXyzqBdzqDO>
		implements LocalXyzqBdzqService {

	@Override
	public void insertBatchSomeColumn(List<LocalXyzqBdzqDO> list) {

		this.baseMapper.insertBatch(list);
	}

}
