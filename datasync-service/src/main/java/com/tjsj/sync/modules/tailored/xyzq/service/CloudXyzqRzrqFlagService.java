package com.tjsj.sync.modules.tailored.xyzq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqMrgTrdFlagDO;

/**
 * XyzqRzrqFlagService
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description
 * @date 2025/5/19 20:33
 */
public interface CloudXyzqRzrqFlagService extends IService<CloudXyzqMrgTrdFlagDO> {


	int deleteByPrimaryKey(Integer id);

	int insert(CloudXyzqMrgTrdFlagDO record);

	int insertSelective(CloudXyzqMrgTrdFlagDO record);

	CloudXyzqMrgTrdFlagDO selectByPrimaryKey(Integer id);

	int updateByPrimaryKeySelective(CloudXyzqMrgTrdFlagDO record);

	int updateByPrimaryKey(CloudXyzqMrgTrdFlagDO record);

	/**
	 * 同时表数据
	 *
	 * <AUTHOR> Ye
	 * @date 2025/05/19
	 */
	void syncOfficialWebsiteData();


}
