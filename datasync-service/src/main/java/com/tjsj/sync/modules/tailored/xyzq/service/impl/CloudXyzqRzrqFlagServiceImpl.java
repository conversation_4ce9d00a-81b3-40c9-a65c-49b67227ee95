package com.tjsj.sync.modules.tailored.xyzq.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.*;
import com.tjsj.sync.modules.tailored.xyzq.service.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.tjsj.sync.modules.tailored.xyzq.mapper.CloudXyzqRzrqFlagMapper;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * CloudXyzqRzrqFlagServiceImpl
 *
 * <AUTHOR> <PERSON>
 * @version 1.0.0
 * @description
 * @date 2025/5/19 20:33
 */
@Service
public class CloudXyzqRzrqFlagServiceImpl extends ServiceImpl<CloudXyzqRzrqFlagMapper, CloudXyzqMrgTrdFlagDO>
		implements CloudXyzqRzrqFlagService {
	@Resource
	private CloudXyzqZslService cloudXyzqZslService;
	@Resource
	private LocalXyzqZslService localXyzqZslService;
	@Resource
	private CloudXyzqBdzqService cloudXyzqBdzqService;
	@Resource
	private LocalXyzqBdzqService localXyzqBdzqService;
	@Resource
	private LocalXyzqMrgTrdFlagService localXyzqMrgTrdFlagService;

	@Resource
	private CloudXyzqRzrqFlagMapper cloudXyzqRzrqFlagMapper;

	@Override
	public int deleteByPrimaryKey(Integer id) {
		return cloudXyzqRzrqFlagMapper.deleteByPrimaryKey(id);
	}

	@Override
	public int insert(CloudXyzqMrgTrdFlagDO record) {
		return cloudXyzqRzrqFlagMapper.insert(record);
	}

	@Override
	public int insertSelective(CloudXyzqMrgTrdFlagDO record) {
		return cloudXyzqRzrqFlagMapper.insertSelective(record);
	}

	@Override
	public CloudXyzqMrgTrdFlagDO selectByPrimaryKey(Integer id) {
		return cloudXyzqRzrqFlagMapper.selectByPrimaryKey(id);
	}

	@Override
	public int updateByPrimaryKeySelective(CloudXyzqMrgTrdFlagDO record) {
		return cloudXyzqRzrqFlagMapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public int updateByPrimaryKey(CloudXyzqMrgTrdFlagDO record) {
		return cloudXyzqRzrqFlagMapper.updateByPrimaryKey(record);
	}

	@Override
	public void syncOfficialWebsiteData() {



	}


}
