package com.tjsj.sync.modules.tailored.xyzq.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.base.mapper.IBaseMapper;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqZslDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 21:49
 * @description
 */
@Mapper
@DS(DataSourceNames.TARGET_DB)
public interface LocalXyzqZslMapper extends IBaseMapper<LocalXyzqZslDO> {

    /**
     * 插入批量一些列
     *
     * @param entityList 实体名单
     *
     * <AUTHOR>
     * @date 2025/07/01
     */
    void insertBatch(List<LocalXyzqZslDO> entityList);
}