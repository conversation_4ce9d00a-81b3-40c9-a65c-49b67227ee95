package com.tjsj.sync.modules.tailored.xyzq.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.base.mapper.IBaseMapper;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqMrgTrdFlagDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * LocalXyzqMrgTrdFlagMapper
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @description 本地数据库-兴业证券官网-数据表爬虫采集标志
 * @date 2025/5/19 20:46
 */
@Mapper
@DS(DataSourceNames.TARGET_DB)
public interface LocalXyzqMrgTrdFlagMapper extends IBaseMapper<LocalXyzqMrgTrdFlagDO> {

    /**
     * 插入批量一些列
     *
     * @param entityList 实体名单
     *
     * <AUTHOR> Ye
     * @date 2025/07/01
     */
    void insertBatch(List<LocalXyzqMrgTrdFlagDO> entityList);
}