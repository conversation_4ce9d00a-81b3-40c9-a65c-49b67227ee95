package com.tjsj.sync.modules.sync.service;

import cn.hutool.core.thread.ThreadUtil;
import com.tjsj.sync.modules.sync.component.processor.DataExtractor;
import com.tjsj.sync.modules.sync.component.processor.DataInserter;
import com.tjsj.sync.modules.sync.component.processor.DataPipeline;
import com.tjsj.sync.modules.sync.component.checker.HashChecker;
import com.tjsj.sync.modules.sync.component.heartbeat.SyncHeartBeatManager;
import com.tjsj.sync.modules.sync.config.SeataConnectionMonitor;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.bo.SyncPrepareResult;
import com.tjsj.sync.modules.sync.model.bo.SyncResult;
import com.tjsj.sync.modules.sync.model.bo.SyncBatchConfig;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.GlobalTransaction;
import io.seata.tm.api.GlobalTransactionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * DistributedSyncService
 *
 * <AUTHOR> Ye
 * @date 2025/08/03
 * @version 1.0.0
 * @description 分布式事务同步服务
 *
 * <p>🌐 核心功能：</p>
 * <ul>
 *   <li><strong>分布式事务管理</strong>：使用Seata AT模式确保跨数据源事务一致性</li>
 *   <li><strong>批量数据同步</strong>：支持大批量数据的分布式事务处理</li>
 *   <li><strong>自动回滚机制</strong>：任何异常都会触发全局事务回滚</li>
 *   <li><strong>数据源自动切换</strong>：与Dynamic-Datasource完美集成</li>
 * </ul>
 *
 * <p>🔧 事务特性：</p>
 * <ul>
 *   <li>事务超时: 5分钟（可配置）</li>
 *   <li>回滚条件: 任何Exception</li>
 *   <li>事务模式: AT模式（自动补偿）</li>
 *   <li>数据源: 支持多数据源事务</li>
 * </ul>
 */
@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "seata.enabled", havingValue = "true", matchIfMissing = false)
public class DistributedSyncService {

    private final DataExtractor dataExtractor;
    private final DataPipeline dataPipeline;
    private final HashChecker hashChecker;
    private final DataInserter dataInserter;
    private final SeataConnectionMonitor seataConnectionMonitor;
    private final TransactionRecoveryService transactionRecoveryService;
    private final SyncHeartBeatManager syncHeartBeatManager;

    // 从配置文件读取降级配置
    @Value("${seata.degradation.auto-fallback-enabled:true}")
    private boolean autoFallbackEnabled;

    /**
     * 执行分布式事务同步（带故障恢复机制）
     *
     * <p>🌐 分布式事务说明：</p>
     * <ul>
     *   <li>优先使用分布式事务，确保跨数据源ACID特性</li>
     *   <li>当Seata Server不可用时，自动降级为本地事务</li>
     *   <li>提供完整的异常处理和状态监控</li>
     *   <li>实现故障恢复机制，处理服务异常停止情况</li>
     *   <li>保证业务连续性，避免因Seata故障导致业务中断</li>
     * </ul>
     *
     * @param prepareResult 同步准备结果
     * @param context 同步上下文
     * @return 同步结果
     */
    public SyncResult executeDistributedSync(SyncPrepareResult prepareResult, SyncContext context) {

        String tableName = context.getFullTableName();

        // 检查是否启用自动降级
        if (!autoFallbackEnabled) {
            log.debug("🔧 自动降级已禁用，直接执行分布式事务 - 表: {}", tableName);
//            return executeDistributedSyncInternal(prepareResult, context);
            return executeDistributedSyncWithRecovery(prepareResult, context);
        }

        // 检查Seata Server连接状态
        if (!seataConnectionMonitor.isSeataServerAvailable()) {
            log.warn("⚠️ Seata Server不可用，自动降级为本地事务模式 - 表: {}", tableName);
            log.warn("🔄 降级原因: {}", seataConnectionMonitor.getStatusDescription());
            log.warn("🔧 降级配置: {}", seataConnectionMonitor.getDegradationConfig());
            return executeLocalSyncFallback(prepareResult, context);
        }

        // 尝试执行分布式事务
        try {
            return executeDistributedSyncWithRecovery(prepareResult, context);
        } catch (Exception e) {
            // 检查是否启用自动降级且为Seata连接相关异常
            if (autoFallbackEnabled && isSeataConnectionException(e)) {
                log.warn("⚠️ 分布式事务执行失败，Seata连接异常，降级为本地事务 - 表: {}", tableName);
                log.warn("🔄 异常信息: {}", e.getMessage());
                log.warn("🔧 降级配置: {}", seataConnectionMonitor.getDegradationConfig());

                // 强制刷新连接状态
                seataConnectionMonitor.forceRefreshStatus();

                // 降级执行
                return executeLocalSyncFallback(prepareResult, context);
            } else {
                // 自动降级禁用或非连接异常，直接抛出
                if (!autoFallbackEnabled) {
                    log.error("❌ 分布式事务失败，自动降级已禁用 - 表: {}", tableName);
                }
                throw e;
            }
        }
    }

    /**
     * 带故障恢复的分布式事务执行方法
     *
     * <p>🛡️ 故障恢复机制：</p>
     * <ul>
     *   <li>手动管理全局事务，确保异常时能正确回滚</li>
     *   <li>添加JVM关闭钩子，处理服务异常停止</li>
     *   <li>实现事务状态检查和恢复</li>
     *   <li>提供详细的事务追踪日志</li>
     * </ul>
     */
    private SyncResult executeDistributedSyncWithRecovery(SyncPrepareResult prepareResult, SyncContext context) {

        String tableName = context.getFullTableName();
        GlobalTransaction globalTransaction = null;
        String xid = null;

        try {
            // 手动开启全局事务
            globalTransaction = GlobalTransactionContext.getCurrentOrCreate();
            globalTransaction.begin(300000, "batch-data-sync-transaction");
            xid = globalTransaction.getXid();

            log.info("🌐 ==================== 分布式事务同步开始 ====================");
            log.info("🆔 全局事务ID: {}", xid);
            log.info("📋 同步表: {}", tableName);
            log.info("🔄 事务模式: Seata AT模式（手动管理+故障恢复）");

            // 注册关闭钩子，处理服务异常停止
            registerShutdownHook(xid, tableName);

            // 注册事务开始
            transactionRecoveryService.registerTransactionStart(xid, tableName);

            long startTime = System.currentTimeMillis();

            // 执行同步逻辑
            SyncResult result = executeSyncLogic(prepareResult, context, xid);

            // 手动提交全局事务
            globalTransaction.commit();

            // 注册事务成功完成
            transactionRecoveryService.registerTransactionComplete(xid, true);

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ ==================== 分布式事务同步成功 ====================");
            log.info("🆔 全局事务ID: {}", xid);
            log.info("📋 同步表: {}", tableName);
            log.info("📊 同步数据量: {} 条", result.getTotalDataNum());
            log.info("📦 处理批次: {} 个", result.getBatchCount());
            log.info("⏱️ 总耗时: {}ms", duration);
            log.info("🎉 全局事务已手动提交");

            return result;

        } catch (Exception e) {
            long startTime = System.currentTimeMillis();
            long duration = System.currentTimeMillis() - startTime;

            log.error("❌ ==================== 分布式事务同步失败 ====================");
            log.error("🆔 全局事务ID: {}", xid);
            log.error("📋 同步表: {}", tableName);
            log.error("💥 失败原因: {}", e.getMessage());
            log.error("⏱️ 失败耗时: {}ms", duration);

            // 手动回滚全局事务
            boolean rollbackSuccess = false;
            if (globalTransaction != null) {
                try {
                    globalTransaction.rollback();
                    rollbackSuccess = true;
                    log.error("🔄 全局事务已手动回滚");
                } catch (Exception rollbackException) {
                    log.error("❌ 全局事务回滚失败: {}", rollbackException.getMessage(), rollbackException);
                }
            }

            // 注册事务失败完成
            transactionRecoveryService.registerTransactionComplete(xid, false);

            log.error("❌ ========================================================");
            throw new RuntimeException("分布式事务同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 内部分布式事务执行方法（注解方式，保留作为备用）
     */
    @GlobalTransactional(
            name = "batch-data-sync-transaction",
            rollbackFor = Exception.class,
            timeoutMills = 300000  // 5分钟超时
    )
    private SyncResult executeDistributedSyncInternal(SyncPrepareResult prepareResult, SyncContext context) {

        String xid = RootContext.getXID();
        String tableName = context.getFullTableName();

        log.info("🌐 ==================== 分布式事务同步开始 ====================");
        log.info("🆔 全局事务ID: {}", xid);
        log.info("📋 同步表: {}", tableName);
        log.info("🔄 事务模式: Seata AT模式");

        long startTime = System.currentTimeMillis();

        try {
            // 执行同步逻辑
            SyncResult result = executeSyncLogic(prepareResult, context, xid);

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ ==================== 分布式事务同步成功 ====================");
            log.info("🆔 全局事务ID: {}", xid);
            log.info("📋 同步表: {}", tableName);
            log.info("📊 同步数据量: {} 条", result.getTotalDataNum());
            log.info("📦 处理批次: {} 个", result.getBatchCount());
            log.info("⏱️ 总耗时: {}ms", duration);
            log.info("🎉 事务将自动提交");

            // TODO:
            ThreadUtil.sleep(100000, TimeUnit.MINUTES);
            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ ==================== 分布式事务同步失败 ====================");
            log.error("🆔 全局事务ID: {}", xid);
            log.error("📋 同步表: {}", tableName);
            log.error("💥 失败原因: {}", e.getMessage());
            log.error("⏱️ 失败耗时: {}ms", duration);
            log.error("🔄 将触发全局事务回滚");
            log.error("❌ ========================================================");

            throw new RuntimeException("分布式事务同步失败: " + e.getMessage(), e);
        }


    }

    /**
     * 执行具体的同步逻辑
     *
     * <p>🔄 处理流程：</p>
     * <ol>
     *   <li>数据提取 - 从source数据库读取（自动切换数据源）</li>
     *   <li>数据处理 - 通过数据处理流水线</li>
     *   <li>哈希检查 - 验证数据是否需要同步</li>
     *   <li>数据插入 - 写入target数据库（自动切换数据源）</li>
     * </ol>
     */
    private SyncResult executeSyncLogic(SyncPrepareResult prepareResult, SyncContext context, String xid) {

        SyncBatchConfig batchConfig = prepareResult.getBatchConfig();
        LocalDateTime targetMaxTime = prepareResult.getTargetMaxTime();
        String tableName = context.getFullTableName();

        int totalDataNum = 0;
        int batchCount = 0;
        int offset = 0;

        List<Map<String, Object>> dataList;

        log.info("🔄 开始批量数据处理 - XID: {}, 表: {}", xid, tableName);

        do {
            String batchId = String.format("batch-%d-%d", System.currentTimeMillis() % 10000, batchCount);

            try {
                log.debug("📦 处理批次 {} - XID: {}, 表: {}, 偏移: {}", batchId, xid, tableName, offset);

                // 0. 更新同步心跳
                syncHeartBeatManager.updateHeartBeat(context.getSyncTableConfig().getId());

                // 1. 数据提取 - 从source数据库读取
                dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchConfig.getReadSize());

                if (dataList.isEmpty()) {
                    log.debug("📭 批次 {} 无数据，结束同步 - XID: {}", batchId, xid);
                    break;
                }

                log.debug("📥 批次 {} 提取数据: {} 条 - XID: {}", batchId, dataList.size(), xid);

                // 2. 数据处理流水线
                dataList = dataPipeline.process(context, dataList);

                // 3. 哈希检查
                if (!hashChecker.checkDataChange(context, dataList, totalDataNum)) {
                    if (dataList.size() != batchConfig.getReadSize()) {
                        log.debug("🔍 批次 {} 数据哈希值未变化且已到最后一批，结束同步 - XID: {}", batchId, xid);
                        break;
                    }
                }

                // 4. 数据插入 - 写入target数据库
                if (!dataList.isEmpty()) {
                    int insertedCount = dataInserter.insertData(context, dataList, batchConfig);
                    totalDataNum += insertedCount;
                    batchCount++;

                    log.debug("📤 批次 {} 插入数据: {} 条 - XID: {}", batchId, insertedCount, xid);
                }

                offset += batchConfig.getReadSize();

            } catch (Exception e) {
                log.error("❌ 批次 {} 处理失败 - XID: {}, 表: {}", batchId, xid, tableName, e);
                throw new RuntimeException("批次 " + batchId + " 处理失败: " + e.getMessage(), e);
            }

        } while (dataList.size() == batchConfig.getReadSize());

        log.info("🎉 批量数据处理完成 - XID: {}, 表: {}, 总批次: {}, 总数据量: {}",
                xid, tableName, batchCount, totalDataNum);

        return SyncResult.builder()
                .totalDataNum(totalDataNum)
                .batchCount(batchCount)
                .success(true)
                .build();
    }

    /**
     * 本地事务降级执行
     * 当Seata Server不可用时的备用方案
     */
    private SyncResult executeLocalSyncFallback(SyncPrepareResult prepareResult, SyncContext context) {

        String tableName = context.getFullTableName();
        log.info("📦 ==================== 本地事务降级同步开始 ====================");
        log.info("📋 同步表: {}", tableName);
        log.info("🔄 执行模式: 本地事务模式（Seata降级）");

        long startTime = System.currentTimeMillis();

        try {
            // 执行本地事务同步逻辑（与原BatchSyncStrategy相同）
            SyncResult result = executeSyncLogic(prepareResult, context, "LOCAL-FALLBACK");

            long duration = System.currentTimeMillis() - startTime;
            log.info("✅ ==================== 本地事务降级同步成功 ====================");
            log.info("📋 同步表: {}", tableName);
            log.info("📊 同步数据量: {} 条", result.getTotalDataNum());
            log.info("📦 处理批次: {} 个", result.getBatchCount());
            log.info("⏱️ 总耗时: {}ms", duration);
            log.info("💡 注意: 此次同步使用本地事务，无跨数据源ACID保证");

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("❌ ==================== 本地事务降级同步失败 ====================");
            log.error("📋 同步表: {}", tableName);
            log.error("💥 失败原因: {}", e.getMessage());
            log.error("⏱️ 失败耗时: {}ms", duration);
            log.error("❌ ========================================================");

            throw new RuntimeException("本地事务降级同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查是否为Seata连接相关异常
     */
    private boolean isSeataConnectionException(Exception e) {
        if (e == null) {
            return false;
        }

        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        // 检查异常消息中是否包含Seata连接相关关键字
        return message.contains("can not connect to services-server") ||
                message.contains("can not register RM") ||
                message.contains("Connection refused") ||
                message.contains("connect failed") ||
                e.getClass().getSimpleName().contains("FrameworkException");
    }

    /**
     * 注册JVM关闭钩子，处理服务异常停止
     *
     * <p>🛡️ 故障恢复说明：</p>
     * <ul>
     *   <li>当JVM异常关闭时，尝试回滚未完成的全局事务</li>
     *   <li>记录事务状态，便于后续恢复</li>
     *   <li>提供优雅关闭机制</li>
     * </ul>
     */
    private void registerShutdownHook(String xid, String tableName) {
        Thread shutdownHook = new Thread(() -> {
            try {
                log.warn("🚨 ==================== JVM关闭检测 ====================");
                log.warn("🆔 检测到未完成的全局事务: {}", xid);
                log.warn("📋 涉及表: {}", tableName);
                log.warn("🔄 尝试回滚全局事务...");

                // 尝试获取全局事务并回滚
                GlobalTransaction globalTransaction = GlobalTransactionContext.reload(xid);
                if (globalTransaction != null) {
                    globalTransaction.rollback();
                    log.warn("✅ 全局事务回滚成功 - XID: {}", xid);
                } else {
                    log.warn("⚠️ 无法获取全局事务实例 - XID: {}", xid);
                }

                log.warn("🚨 ==================== JVM关闭处理完成 ====================");

            } catch (Exception e) {
                log.error("❌ JVM关闭时回滚全局事务失败 - XID: {}, 错误: {}", xid, e.getMessage(), e);
            }
        }, "SeataShutdownHook-" + xid);

        // 注册关闭钩子
        Runtime.getRuntime().addShutdownHook(shutdownHook);

        log.debug("🛡️ 已注册JVM关闭钩子 - XID: {}", xid);
    }

    /**
     * 检查并恢复悬挂的事务
     *
     * <p>🔍 恢复机制：</p>
     * <ul>
     *   <li>检查是否有未完成的全局事务</li>
     *   <li>尝试恢复或回滚悬挂事务</li>
     *   <li>记录恢复过程和结果</li>
     * </ul>
     */
    public void checkAndRecoverPendingTransactions() {
        try {
            log.info("🔍 开始检查悬挂事务...");

            // 这里可以实现具体的悬挂事务检查逻辑
            // 例如：查询数据库中的事务状态表，检查未完成的事务

            log.info("✅ 悬挂事务检查完成");

        } catch (Exception e) {
            log.error("❌ 悬挂事务检查失败", e);
        }
    }
}
