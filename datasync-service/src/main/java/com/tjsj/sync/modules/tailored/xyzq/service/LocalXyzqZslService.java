package com.tjsj.sync.modules.tailored.xyzq.service;

import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqZslDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/19 21:49
 * @description
 */

public interface LocalXyzqZslService extends IService<LocalXyzqZslDO> {


	/**
	 * 插入批量数据
	 *
	 * @param list 列表
	 * <AUTHOR>
	 * @date 2025/05/20
	 */
	void insertBatchSomeColumn(List<LocalXyzqZslDO> list);

}
