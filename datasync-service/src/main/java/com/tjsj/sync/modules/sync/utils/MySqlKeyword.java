package com.tjsj.sync.modules.sync.utils;

import java.util.*;

/**
 * MySqlKeyword
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/10/08
 * @description MySql关键字
 */
public class MySqlKeyword {

    /**
     * MySQL关键字集合
     *
     * @description 该集合包含了MySQL所有的关键字，包括保留字、数据类型、函数、运算符等。
     * @date 2024/10/08
     */
    private static final Set<String> KEYWORDS = new HashSet<>(Arrays.asList(
            "ACCESSIBLE", "ADD", "ALL", "ALTER", "ANALYZE", "AND", "AS", "ASC", "ASENSITIVE",
            "BEFORE", "BETWEEN", "BIGINT", "BINARY", "BLOB", "BOTH", "BY", "CALL", "CASCADE",
            "CASE", "CHANGE", "CHAR", "CHARACTER", "CHECK", "COLLAT<PERSON>", "COLUMN", "CONDITION",
            "CONSTRAINT", "CONTINUE", "CONVERT", "CREATE", "CROSS", "CUBE", "CUME_DIST", "CURRENT_DATE",
            "CURRENT_TIME", "CURRENT_TIMESTAMP", "CURRENT_USER", "CURSOR", "DATABASE", "DATABASES",
            "DAY_HOUR", "DAY_MICROSECOND", "DAY_MINUTE", "DAY_SECOND", "DEC", "DECIMAL", "DECLARE",
            "DEFAULT", "DELAYED", "DELETE", "DENSE_RANK", "DESC", "DESCRIBE", "DETERMINISTIC",
            "DISTINCT", "DISTINCTROW", "DIV", "DOUBLE", "DROP", "DUAL", "EACH", "ELSE", "ELSEIF",
            "EMPTY", "ENCLOSED", "ESCAPED", "EXCEPT", "EXISTS", "EXIT", "EXPLAIN", "FALSE", "FETCH",
            "FIRST_VALUE", "FLOAT", "FLOAT4", "FLOAT8", "FOR", "FORCE", "FOREIGN", "FROM", "FULLTEXT",
            "FUNCTION", "GENERATED", "GET", "GRANT", "GROUP", "GROUPING", "GROUPS", "HAVING", "HIGH_PRIORITY",
            "HOUR_MICROSECOND", "HOUR_MINUTE", "HOUR_SECOND", "IF", "IGNORE", "IN", "INDEX", "INFILE",
            "INNER", "INOUT", "INSENSITIVE", "INSERT", "INT", "INT1", "INT2", "INT3", "INT4", "INT8", "INTEGER",
            "INTERVAL", "INTO", "IO_AFTER_GTIDS", "IO_BEFORE_GTIDS", "IS", "ITERATE", "JOIN", "JSON_TABLE",
            "KEY", "KEYS", "KILL", "LAG", "LAST_VALUE", "LATERAL", "LEAD", "LEADING", "LEAVE", "LEFT",
            "LIKE", "LIMIT", "LINEAR", "LINES", "LOAD", "LOCALTIME", "LOCALTIMESTAMP", "LOCK", "LONG",
            "LONGBLOB", "LONGTEXT", "LOOP", "LOW_PRIORITY", "MASTER_BIND", "MASTER_SSL_VERIFY_SERVER_CERT",
            "MATCH", "MAXVALUE", "MEDIUMBLOB", "MEDIUMINT", "MEDIUMTEXT", "MIDDLEINT", "MINUTE_MICROSECOND",
            "MINUTE_SECOND", "MOD", "MODIFIES", "NATURAL", "NOT", "NO_WRITE_TO_BINLOG", "NTH_VALUE", "NTILE",
            "NULL", "NUMERIC", "OF", "ON", "OPTIMIZE", "OPTIMIZER_COSTS", "OPTION", "OPTIONALLY", "OR",
            "ORDER", "OUT", "OUTER", "OUTFILE", "OVER", "PARTITION", "PERCENT_RANK", "PRECISION",
            "PRIMARY", "PROCEDURE", "PURGE", "RANGE", "RANK", "READ", "READS", "READ_WRITE", "REAL",
            "RECURSIVE", "REFERENCES", "REGEXP", "RELEASE", "RENAME", "REPEAT", "REPLACE", "REQUIRE",
            "RESIGNAL", "RESTRICT", "RETURN", "REVOKE", "RIGHT", "RLIKE", "ROW", "ROWS", "ROW_NUMBER",
            "SCHEMA", "SCHEMAS", "SECOND_MICROSECOND", "SELECT", "SENSITIVE", "SEPARATOR", "SET", "SHOW",
            "SIGNAL", "SMALLINT", "SPATIAL", "SPECIFIC", "SQL", "SQLEXCEPTION", "SQLSTATE", "SQLWARNING",
            "SQL_BIG_RESULT", "SQL_CALC_FOUND_ROWS", "SQL_SMALL_RESULT", "SSL", "STARTING", "STORED",
            "STRAIGHT_JOIN", "SYSTEM", "TABLE", "TERMINATED", "THEN", "TINYBLOB", "TINYINT", "TINYTEXT",
            "TO", "TRAILING", "TRIGGER", "TRUE", "UNDO", "UNION", "UNIQUE", "UNLOCK", "UNSIGNED", "UPDATE",
            "USAGE", "USE", "USING", "UTC_DATE", "UTC_TIME", "UTC_TIMESTAMP", "VALUES", "VARBINARY", "VARCHAR",
            "VARCHARACTER", "VARYING", "VIRTUAL", "WHEN", "WHERE", "WHILE", "WINDOW", "WITH", "WRITE",
            "XOR", "YEAR_MONTH", "ZEROFILL"
    ));

    /**
     * 判断是否为关键字
     *
     * @param word 关键字 
     * @return boolean
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public static boolean isKeyword(String word) {
        return KEYWORDS.contains(word.toUpperCase());
    }

    /**
     * 处理数据记录中的MySQL关键字字段
     * <p>
     * 将字段名为MySQL关键字的字段用反引号(`)包裹，避免SQL语法错误。
     * 例如：字段名 "order" 会被转换为 "`order`"
     * </p>
     *
     * <h3>处理规则：</h3>
     * <ul>
     *   <li>如果字段名是MySQL关键字，则用反引号包裹：{@code order -> `order`}</li>
     *   <li>如果字段名不是关键字，则保持不变：{@code user_name -> user_name}</li>
     *   <li>字段值保持不变，只处理字段名</li>
     * </ul>
     *
     * <h3>使用示例：</h3>
     * <pre>{@code
     * Map<String, Object> originalRecord = Map.of(
     *     "id", 1,
     *     "order", "A001",        // MySQL关键字
     *     "user_name", "张三"      // 非关键字
     * );
     *
     * Map<String, Object> processedRecord = MySqlKeyword.processKeywords(originalRecord);
     * // 结果：{"id": 1, "`order`": "A001", "user_name": "张三"}
     * }</pre>
     *
     * @param record 待处理的数据记录，包含字段名和字段值的映射
     * @return 处理后的数据记录，关键字字段名已用反引号包裹
     * @throws NullPointerException 如果 record 为 null
     * <AUTHOR> Ye
     * @date 2024/10/11
     * @since 1.0.0
     */
    public static Map<String, Object> processKeywords(Map<String, Object> record) {
        // 1. 空值检查
        if (record == null) {
            throw new IllegalArgumentException("数据记录不能为null");
        }

        if (record.isEmpty()) {
            return new HashMap<>();
        }

        // 2. 创建新的结果映射，避免修改原始数据
        Map<String, Object> processedRecord = new HashMap<>(record.size());

        // 3. 遍历处理每个字段
        for (Map.Entry<String, Object> entry : record.entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();

            // 4. 处理字段名：如果是关键字则用反引号包裹
            String processedFieldName = isKeyword(fieldName) ? wrapWithBackticks(fieldName) : fieldName;

            // 5. 保存处理后的字段
            processedRecord.put(processedFieldName, fieldValue);
        }

        return processedRecord;
    }

    /**
     * 用反引号包裹字段名
     * <p>将字段名用MySQL反引号包裹，用于处理关键字字段</p>
     *
     * @param fieldName 字段名
     * @return 用反引号包裹的字段名
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    private static String wrapWithBackticks(String fieldName) {
        return "`" + fieldName + "`";
    }

}

