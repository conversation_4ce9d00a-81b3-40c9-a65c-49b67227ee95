package com.tjsj.sync.modules.business.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.sync.modules.business.model.InfoSchemaColumn;
import org.apache.ibatis.annotations.Mapper;

/**
 * InfoSchemaColumnMapper
 *
 * <AUTHOR>
 * @version 1.0.0
 * @description
 * @date 2024/10/12 19:08
 */
@DS(DataSourceNames.TARGET_DB)
@Mapper
public interface InfoSchemaColumnMapper extends BaseMapper<InfoSchemaColumn> {
}