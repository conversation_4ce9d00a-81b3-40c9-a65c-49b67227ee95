package com.tjsj.sync.modules.sync.model.bo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * SyncPrepareResult
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步表准备结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncPrepareResult {
    /**
     * 目标表最大更新时间
     */
    private LocalDateTime targetMaxTime;

    /**
     * 批次配置
     */
    private SyncBatchConfig batchConfig;

    /**
     * 是否已执行全量更新
     */
    private boolean fullUpdateExecuted;


}
