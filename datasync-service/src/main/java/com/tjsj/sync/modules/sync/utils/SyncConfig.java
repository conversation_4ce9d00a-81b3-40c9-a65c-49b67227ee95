package com.tjsj.sync.modules.sync.utils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SyncConfig
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步配置类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncConfig {

    private int batchReadSize;
    private int batchInsertSize;
    private int maxRetryTimes;


}
