package com.tjsj.sync.modules.sync.service.impl;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.sync.modules.sync.mapper.SyncTableConfigMapper;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * SyncTableConfigServiceImpl
 *
 * <AUTHOR>
 * @date 2024/08/07
 * @description 针对表【database_sync_tables】的数据库操作Service实现
 */
@Service
public class SyncTableConfigServiceImpl extends ServiceImpl<SyncTableConfigMapper, SyncTableConfigDO>
        implements SyncTableConfigService {

    @Resource
    private TarkinConfig tarkinConfig;

    @Override
    public List<SyncTableConfigDO> findRecentlyUpdatedConfig(LocalDateTime lastSyncTime) {

        return this.list(Wrappers.<SyncTableConfigDO>lambdaQuery()
                        .eq(SyncTableConfigDO::getProjectId, tarkinConfig.getProjectId())
                        .eq(SyncTableConfigDO::getDbType, tarkinConfig.getDbType())
                        .eq(SyncTableConfigDO::getProfileType, tarkinConfig.getProfile())
                        .isNotNull(SyncTableConfigDO::getCrontab)
                        .ne(SyncTableConfigDO::getCrontab, "")
                        .gt(lastSyncTime != null, SyncTableConfigDO::getUpdateTime, lastSyncTime))
                .stream()
                .filter(config -> StrUtil.isNotBlank(config.getCrontab()))
                .toList();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateSyncTableConfig(String sourceDbType, String targetDbType, String sourceProfileType,
                                        String targetProfileType) {

        List<SyncTableConfigDO> sourceTableConfigList = this.list(Wrappers.<SyncTableConfigDO>lambdaQuery()
                .eq(SyncTableConfigDO::getDbType, sourceDbType)
                .eq(SyncTableConfigDO::getProfileType, sourceProfileType)
                .eq(SyncTableConfigDO::getDeleteStatus, CommonStatus.ENABLE));

        sourceTableConfigList.forEach(sourceTableConfig -> {
            sourceTableConfig.setDbType(EnumUtil.getBy(EnvironmentTypeEnum::getCode, targetDbType))
                    .setProfileType(targetProfileType)
                    .setId(null)
                    .setCreateTime(null)
                    .setUpdateTime(null)
                    .setQuartzLastSyncDataHash(null)
                    .setLastSyncStartTime(null)
                    .setIfSyncing(CommonStatus.DISABLE);
        });

        this.saveBatch(sourceTableConfigList);

    }


}




