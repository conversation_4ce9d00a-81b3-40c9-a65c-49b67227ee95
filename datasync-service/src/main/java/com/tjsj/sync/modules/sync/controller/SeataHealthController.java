package com.tjsj.sync.modules.sync.controller;

import com.tjsj.sync.modules.sync.config.SeataConnectionMonitor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * SeataHealthController
 *
 * <AUTHOR>
 * @date 2025/08/03
 * @version 1.0.0
 * @description Seata健康检查控制器
 * 
 * <p>🎯 核心功能：</p>
 * <ul>
 *   <li><strong>健康检查</strong>：提供Seata连接状态检查接口</li>
 *   <li><strong>状态监控</strong>：实时查看Seata Server连接状态</li>
 *   <li><strong>手动刷新</strong>：支持手动刷新连接状态</li>
 * </ul>
 */
@RestController
@RequestMapping("/seata")
@Slf4j
@RequiredArgsConstructor
public class SeataHealthController {

    @Autowired(required = false)
    private SeataConnectionMonitor seataConnectionMonitor;

    @Value("${seata.enabled:false}")
    private boolean seataEnabled;

    /**
     * 获取Seata健康状态
     * 
     * @return Seata状态信息
     */
    @GetMapping("/health")
    public Map<String, Object> getSeataHealth() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!seataEnabled) {
                result.put("status", "DISABLED");
                result.put("message", "Seata未启用");
                result.put("enabled", false);
                return result;
            }

            if (seataConnectionMonitor == null) {
                result.put("status", "UNAVAILABLE");
                result.put("message", "Seata连接监控器未初始化");
                result.put("enabled", true);
                return result;
            }

            boolean isAvailable = seataConnectionMonitor.isSeataServerAvailable();
            String description = seataConnectionMonitor.getStatusDescription();

            result.put("status", isAvailable ? "UP" : "DOWN");
            result.put("message", description);
            result.put("enabled", true);
            result.put("available", isAvailable);
            result.put("cached_status", seataConnectionMonitor.getCachedStatus());
            result.put("auto_fallback_enabled", seataConnectionMonitor.isAutoFallbackEnabled());
            result.put("degradation_config", seataConnectionMonitor.getDegradationConfig());

            log.debug("🔍 Seata健康检查 - 状态: {}, 描述: {}",
                     isAvailable ? "UP" : "DOWN", description);

        } catch (Exception e) {
            log.error("❌ Seata健康检查异常", e);
            result.put("status", "ERROR");
            result.put("message", "健康检查异常: " + e.getMessage());
            result.put("enabled", seataEnabled);
        }

        return result;
    }

    /**
     * 强制刷新Seata连接状态
     * 
     * @return 刷新后的状态信息
     */
    @GetMapping("/refresh")
    public Map<String, Object> refreshSeataStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!seataEnabled) {
                result.put("status", "DISABLED");
                result.put("message", "Seata未启用，无需刷新");
                return result;
            }

            if (seataConnectionMonitor == null) {
                result.put("status", "UNAVAILABLE");
                result.put("message", "Seata连接监控器未初始化");
                return result;
            }

            log.info("🔄 手动刷新Seata连接状态");
            boolean isAvailable = seataConnectionMonitor.forceRefreshStatus();
            String description = seataConnectionMonitor.getStatusDescription();

            result.put("status", isAvailable ? "UP" : "DOWN");
            result.put("message", "状态已刷新 - " + description);
            result.put("available", isAvailable);
            result.put("refreshed", true);

            log.info("✅ Seata状态刷新完成 - 状态: {}", isAvailable ? "UP" : "DOWN");

        } catch (Exception e) {
            log.error("❌ Seata状态刷新异常", e);
            result.put("status", "ERROR");
            result.put("message", "状态刷新异常: " + e.getMessage());
            result.put("refreshed", false);
        }

        return result;
    }

    /**
     * 获取Seata配置信息
     * 
     * @return Seata配置信息
     */
    @GetMapping("/config")
    public Map<String, Object> getSeataConfig() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("enabled", seataEnabled);
        result.put("monitor_available", seataConnectionMonitor != null);

        if (seataConnectionMonitor != null) {
            result.put("status_description", seataConnectionMonitor.getStatusDescription());
            result.put("cached_status", seataConnectionMonitor.getCachedStatus());
            result.put("auto_fallback_enabled", seataConnectionMonitor.isAutoFallbackEnabled());
            result.put("degradation_config", seataConnectionMonitor.getDegradationConfig());
        }

        return result;
    }
}
