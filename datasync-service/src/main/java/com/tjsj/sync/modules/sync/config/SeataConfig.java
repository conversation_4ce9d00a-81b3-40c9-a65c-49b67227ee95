package com.tjsj.sync.modules.sync.config;

import io.seata.spring.annotation.GlobalTransactionScanner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * SeataConfig
 *
 * <AUTHOR>
 * @date 2025/08/03
 * @version 1.0.0
 * @description Seata分布式事务配置类 - File模式
 * 
 * <p>🎯 核心功能：</p>
 * <ul>
 *   <li><strong>File模式配置</strong>：基于文件的注册和配置中心，无需外部依赖</li>
 *   <li><strong>全局事务扫描</strong>：自动扫描@GlobalTransactional注解</li>
 *   <li><strong>条件化启用</strong>：通过seata.enabled配置控制是否启用</li>
 *   <li><strong>数据源代理</strong>：自动代理数据源，支持AT模式事务</li>
 * </ul>
 *
 */
@Configuration
@Slf4j
public class SeataConfig {

    /**
     * 应用标识，用于区分不同的应用
     */
    @Value("${seata.application-id:data-sync-service}")
    private String applicationId;

    /**
     * 事务服务组，用于事务路由
     */
    @Value("${seata.tx-service-group:data-sync-group}")
    private String txServiceGroup;

    /**
     * 是否启用Seata，默认false
     */
    @Value("${seata.enabled:false}")
    private boolean seataEnabled;

    // 构造函数中添加日志，验证配置类是否被加载
    public SeataConfig() {
        log.info("🔧 SeataConfig 配置类正在初始化...");
        log.info("🔧 注意：此时@Value注解的值还未注入，实际值将在Bean方法中显示");
    }

    /**
     * 初始化全局事务扫描器
     *
     * <p>🌐 功能说明：</p>
     * <ul>
     *   <li>扫描@GlobalTransactional注解的方法</li>
     *   <li>自动开启和管理全局事务</li>
     *   <li>处理分支事务的注册和提交/回滚</li>
     * </ul>
     */
    @Bean
    @Primary  // 优先使用我们的配置，覆盖自动配置
    @ConditionalOnProperty(name = "seata.enabled", havingValue = "true")
    public GlobalTransactionScanner globalTransactionScanner() {
        log.info("🌐 ==================== Seata配置初始化开始 ====================");
        log.info("🔧 seata.enabled = {}", seataEnabled);

        // 确保配置值不为空
        String appId = (applicationId != null && !applicationId.trim().isEmpty()) ? applicationId : "data-sync-service";
        String txGroup = (txServiceGroup != null && !txServiceGroup.trim().isEmpty()) ? txServiceGroup : "data-sync-group";

        log.info("📋 应用标识: {}", appId);
        log.info("🔗 事务服务组: {}", txGroup);
        log.info("📁 配置模式: File模式（无外部依赖）");

        try {
            GlobalTransactionScanner scanner = new GlobalTransactionScanner(appId, txGroup);

            log.info("✅ 全局事务扫描器创建成功");
            log.info("🔍 将自动扫描@GlobalTransactional注解");
            log.info("🌐 ==================== Seata配置初始化完成 ====================");

            return scanner;

        } catch (Exception e) {
            log.error("❌ ==================== Seata配置初始化失败 ====================");
            log.error("💥 初始化失败原因: {}", e.getMessage(), e);
            log.error("🔧 请检查以下配置:");
            log.error("   1. Seata Server是否启动 (端口8091)");
            log.error("   2. registry.conf和file.conf是否存在");
            log.error("   3. 数据源配置是否正确");
            log.error("   4. undo_log表是否已创建");
            throw new RuntimeException("Seata配置初始化失败", e);
        }
    }
}
