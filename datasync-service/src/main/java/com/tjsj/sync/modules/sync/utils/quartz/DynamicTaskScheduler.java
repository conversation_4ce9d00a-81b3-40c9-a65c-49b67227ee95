package com.tjsj.sync.modules.sync.utils.quartz;

import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.sync.utils.SyncUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * DynamicTaskScheduler
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2024/12/17 10:23
 * @description 动态任务调度器
 */
@Data
@Component
@Slf4j
public class DynamicTaskScheduler {

    /**
     * Quartz 核心调度器
     */
    @Resource
    private Scheduler scheduler;

    @Resource
    private SyncTableConfigService syncTableConfigService;

    /**
     * jobDetailLastSyncTime 同步任务详情的最后一次同步时间，此值与同步配置表中存在CronJob值的记录中的最大UpdateTime值保持一致
     */
    private LocalDateTime jobDetailLastSyncTime = null;

    public void updateLastSyncTime(LocalDateTime newJobDetailSyncTime) {


        if (newJobDetailSyncTime != null) {
            this.jobDetailLastSyncTime = newJobDetailSyncTime;
        }

    }

    /**
     * 管理Quartz任务：动态加载/更新/删除任务
     *
     * <AUTHOR> Ye
     * @date 2025/04/29
     */
    public void manageQuartzJob() throws SchedulerException {

        LocalDateTime jobDetailLastSyncTime = getJobDetailLastSyncTime();

        // 从数据库中查询启用状态的表任务，并且是UpdateTime字段值大于jobDetailLastSyncTime的任务
        List<SyncTableConfigDO> syncTableConfigList =
                syncTableConfigService.findRecentlyUpdatedConfig(jobDetailLastSyncTime);

        LocalDateTime newJobDetailUpdateTime = syncTableConfigList.stream()
                .map(SyncTableConfigDO::getUpdateTime)
                .max(LocalDateTime::compareTo)
                .orElse(jobDetailLastSyncTime);

        List<SyncTableConfigDO> enabledSyncTableConfigList = syncTableConfigList.stream()
                .filter(config -> config.getDeleteStatus() == CommonStatus.ENABLE && config.getEnableStatus() == CommonStatus.ENABLE)
                .toList();
        for (SyncTableConfigDO syncTableConfigDO : enabledSyncTableConfigList) {
            // 构建任务的唯一标识
            String jobKey = this.buildQuartzJobKey(syncTableConfigDO);
            JobKey existingJobKey = new JobKey(jobKey);


            if (scheduler.checkExists(existingJobKey)) {

                // 1.任务已存在，判断任务配置是否存在更新，如果是：则先删除任务，再重新添加
                boolean tableConfigUpdated = this.checkTableConfigUpdated(syncTableConfigDO, existingJobKey);

                if (tableConfigUpdated) {
                    scheduler.deleteJob(existingJobKey);
                    log.warn("删除Scheduler任务，JobKey: {}", jobKey);

                    this.addQuartzJob(syncTableConfigDO, jobKey);
                }


            } else {
                // 2.任务不存在，新增任务
                this.addQuartzJob(syncTableConfigDO, jobKey);
            }

        }

        // 删除定时任务
        // 过滤出未启用或已删除的任务
        List<SyncTableConfigDO> disabledSyncTableConfigList = syncTableConfigList.stream()
                .filter(config -> config.getDeleteStatus() == CommonStatus.DISABLE || config.getEnableStatus() == CommonStatus.DISABLE)
                .toList();
        this.deleteQuartzJob(disabledSyncTableConfigList);

        // 更新 lastSyncTime
        this.updateLastSyncTime(newJobDetailUpdateTime);

    }

    /**
     * 构建Quartz任务键
     *
     * @param syncTableConfigDO 同步任务配置
     * @return String
     * <AUTHOR> Ye
     * @date 2025/05/26
     */
    private String buildQuartzJobKey(SyncTableConfigDO syncTableConfigDO) {

        return syncTableConfigDO.getSchemaName() + "." + syncTableConfigDO.getTableName()
                + " - " + syncTableConfigDO.getDataSchemaName() + "." + syncTableConfigDO.getDataTableName();
    }

    /**
     * 检查任务配置是否存在更新
     *
     * @param newTableConfig 同步任务配置
     * @param existingJobKey    已存在的任务键
     * @return boolean
     * <AUTHOR> Ye
     * @date 2025/05/26
     */
    private boolean checkTableConfigUpdated(SyncTableConfigDO newTableConfig, JobKey existingJobKey) {

        try {
            JobDetail jobDetail = scheduler.getJobDetail(existingJobKey);
            if (null != jobDetail) {
                JobDataMap jobDataMap = jobDetail.getJobDataMap();
                SyncTableConfigDO oldTableConfig = SyncUtil.buildSyncTableConfigFromJobDataMap(jobDataMap);

                // 通过注解字段比对 hash
                String newHash = SyncUtil.generateSyncTableHash(newTableConfig);
                String oldHash = SyncUtil.generateSyncTableHash(oldTableConfig);

                return !newHash.equals(oldHash);

            } else {
                return true;
            }


        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 删除Quartz定时任务
     *
     * @param disabledSyncTableConfigList 已删除或禁用的同步任务列表
     * <AUTHOR> Ye
     * @date 2024/12/17
     */
    private void deleteQuartzJob(
            @NotNull List<SyncTableConfigDO> disabledSyncTableConfigList) throws SchedulerException {


        // 获取 Quartz 中所有已注册的任务，删除不再需要的任务
        Set<JobKey> registeredJobKeys = scheduler.getJobKeys(GroupMatcher.anyGroup());

        for (JobKey registeredJobKey : registeredJobKeys) {
            String jobKeyName = registeredJobKey.getName();

            // 判断当前已注册的任务是否在数据库中已删除或禁用
            // 如果已删除或禁用，则从 Quartz 中删除该任务
            boolean taskDeleteInDb = disabledSyncTableConfigList.stream()
                    .anyMatch(config -> (config.getSchemaName() + "." + config.getTableName()
                            + config.getDataSchemaName() + "." + config.getDataTableName()).equals(jobKeyName));

            if (taskDeleteInDb) {
                scheduler.deleteJob(registeredJobKey);
                log.warn("删除Quartz定时任务: {}", jobKeyName);
            }
        }

    }


    /**
     * 新增任务到 Quartz 调度器。
     * <p>
     * 此方法会将一个新的同步任务添加到 Quartz 调度器中。
     * 每个任务的唯一标识由 `schemaName` 和 `tableName`，以及 `dataSchemaName` 和 `dataTableName` 组合而成的 `jobKey` 定义。
     * 任务的调度时间由 `config` 中的 `crontab` 字段提供。
     *
     * @param config 同步任务的配置信息，包含任务的数据库名、表名和调度规则（cron表达式）。
     * @param jobKey 任务的唯一标识，通常由 `schemaName.tableName` 拼接而成。
     * <AUTHOR> Ye
     * @date 2025/04/29
     */
    private void addQuartzJob(@NotNull SyncTableConfigDO config, String jobKey) throws SchedulerException {

        // 1. 构建 JobDetail，用于定义任务的元数据。
        // - 指定任务执行类：SyncTask.class
        // - 任务的唯一标识：jobKey
        // - 传递任务所需的参数，例如 tableName 和 schemaName。


        JobDetail jobDetail = SyncUtil.buildJobDetailFromSyncTableConfig(config, jobKey);

        // 2. 构建 Trigger，用于定义任务的触发规则。
        // - Trigger 的唯一标识：Trigger-jobKey
        // - 调度规则：基于 cron 表达式的触发器。
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity("Trigger-" + jobKey)
                .withSchedule(CronScheduleBuilder.cronSchedule(config.getCrontab()))
                .build();

        // 3. 将任务和触发器注册到 Quartz 调度器中，等待调度器执行任务。
        scheduler.scheduleJob(jobDetail, trigger);

        log.warn("添加Quartz定时任务，JobKey: {}；Crontab: {}", jobKey, config.getCrontab());

    }


}
