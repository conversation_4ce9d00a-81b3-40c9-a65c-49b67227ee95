package com.tjsj.sync.modules.util.controller;


import com.tjsj.common.annotation.ServiceSwitchControl;
import com.tjsj.common.annotation.authorize.PassToken;
import com.tjsj.common.lock.mapper.LocalDistributedLockMapper;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * TestController
 *
 * <AUTHOR>
 * @date 2025/06/29
 * @version 1.0.0
 * @description 测试控制器
 */
@Tag(name = "TestController", description = "测试控制器")
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
@Slf4j
public class TestController {


    private final SyncTableConfigService syncTableConfigService;

    @Resource
    private LocalDataMapper localDataMapper;

    @Resource
    private CloudDataMapper cloudDataMapper;

    @Resource
    private LocalDistributedLockMapper localDistributedLockMapper;

    /**
     * @description 测试控制器
     */
    @Operation(summary = "manageQuartzJob")
    @GetMapping("/manageQuartzJob")
    @PassToken
    @ServiceSwitchControl
    public void manageQuartzJob() {
        log.warn("manageQuartzJob");
//        taskSyncJob.manageQuartzJob();
    }

    /**
     * @description 测试控制器
     */
    @Operation(summary = "生产同步表配置",
            parameters = {@Parameter(name = "sourceDbType", description = "源数据库类型"),
                    @Parameter(name = "targetDbType", description = "目标数据库类型"),
                    @Parameter(name = "sourceProfileType", description = "源环境配置类型"),
                    @Parameter(name = "targetProfileType", description = "目标环境配置类型")})
    @PostMapping("/generateSyncTableConfig")
    public void generateSyncTableConfig(@RequestParam("sourceDbType") String sourceDbType,
                                        @RequestParam("targetDbType") String targetDbType,
                                        @RequestParam("sourceProfileType") String sourceProfileType,
                                        @RequestParam("targetProfileType") String targetProfileType) {

        syncTableConfigService.generateSyncTableConfig(sourceDbType, targetDbType, sourceProfileType,
                targetProfileType);
    }


    /**
     * @description 测试控制器
     */
    @GetMapping("/test01")
    @PassToken
    public void test01() {

        int affectedRows = localDistributedLockMapper.testDeleteDistributedLock();
        log.warn("affectedRows: {}", affectedRows);
    }

    /**
     * @description 测试控制器
     */
    @GetMapping("/test02")
    @PassToken
    public void test02() {

        int affectedRows = cloudDataMapper.testDeleteDistributedLock();
        log.warn("affectedRows: {}", affectedRows);
    }


    /**
     * @description 测试控制器
     */
    @GetMapping("/test03")
    @PassToken
    public void test03() {

        int affectedRows = localDataMapper.testDeleteDistributedLock();
        log.warn("affectedRows: {}", affectedRows);
    }


    /**
     * @description 测试控制器
     */
    @GetMapping("/test04")
    @PassToken
    public void test04() {

        int affectedRows = localDataMapper.testDeleteDistributedLock();
        log.warn("affectedRows: {}", affectedRows);
        int affectedRows1 = cloudDataMapper.testDeleteDistributedLock();
        log.warn("affectedRows1: {}", affectedRows1);
    }


    /**
     * @description 测试控制器
     */
    @GetMapping("/test05")
    @PassToken
    public void test05() {

        int affectedRows = cloudDataMapper.testDeleteDistributedLock();
        log.warn("affectedRows: {}", affectedRows);
        int affectedRows1 = localDataMapper.testDeleteDistributedLock();
        log.warn("affectedRows1: {}", affectedRows1);
    }

    /**
     * @description 测试控制器
     */
    @GetMapping("/test06")
    @PassToken
    public void test06() {

        int affectedRows = cloudDataMapper.testDeleteDistributedLock();
        log.warn("affectedRows: {}", affectedRows);
        int affectedRows1 = localDistributedLockMapper.testDeleteDistributedLock();
        log.warn("affectedRows1: {}", affectedRows1);
    }

    /**
     * @description 测试控制器
     */
    @GetMapping("/test07")
    @PassToken
    public void test07() {

        int affectedRows = localDistributedLockMapper.testDeleteDistributedLock();
        log.warn("affectedRows: {}", affectedRows);
        int affectedRows1 = cloudDataMapper.testDeleteDistributedLock();
        log.warn("affectedRows1: {}", affectedRows1);

    }



}
