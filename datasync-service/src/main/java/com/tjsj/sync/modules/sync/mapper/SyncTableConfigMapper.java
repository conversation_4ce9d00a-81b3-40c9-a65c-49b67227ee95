package com.tjsj.sync.modules.sync.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * SyncTableConfigMapper
 *
 * <AUTHOR>
 * @date 2024/08/07
 * @description 针对表【database_sync_tables】的数据库操作Mapper
 */
@Mapper
@DS(DataSourceNames.SOURCE_DB)
public interface SyncTableConfigMapper extends BaseMapper<SyncTableConfigDO> {


}




