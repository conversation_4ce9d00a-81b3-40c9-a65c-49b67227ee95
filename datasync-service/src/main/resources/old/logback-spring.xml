<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- ========================================
         📋 数据同步服务日志配置文件
         🎯 功能：分离WARN和ERROR级别日志到不同文件
         📅 更新：2025-06-30
         ======================================== -->

    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <!-- ========================================
         🔧 全局配置参数
         ======================================== -->
    <property name="SERVICE_NAME" value="datasync-service"/>
    <property name="LOG_PATTERN" value="%date %level [%thread] %logger{10} [%file:%line] %msg%n"/>
    <property name="LOG_CHARSET" value="UTF-8"/>
    <property name="MAX_FILE_SIZE" value="10MB"/>
    <property name="MAX_HISTORY" value="30"/>
    <property name="TOTAL_SIZE_CAP" value="5GB"/>

    <!-- ========================================
         🔇 第三方库日志级别控制
         ======================================== -->
    <logger name="c.n.d.s.r.aws.ConfigClusterResolver" level="ERROR"/>
    <logger name="org.springframework.web" level="ERROR"/>
    <logger name="org.springboot.sample" level="ERROR"/>

    <!-- ========================================
         📁 通用Appender模板定义
         ======================================== -->

    <!-- 🔴 ERROR级别日志Appender模板 -->
    <appender name="ERROR_FILE_TEMPLATE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${LOG_CHARSET}</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 🟡 WARN级别日志Appender模板 -->
    <appender name="WARN_FILE_TEMPLATE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${LOG_CHARSET}</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- ========================================
         🛠️ 开发/测试环境配置
         ======================================== -->
    <springProfile name="dev,test,local">
        <!-- 🔴 ERROR日志文件 -->
        <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>./logs/${SERVICE_NAME}-dev_error.log</file>
            <encoder>
                <pattern>${LOG_PATTERN}</pattern>
                <charset>${LOG_CHARSET}</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>./logs/${SERVICE_NAME}-dev_error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>${MAX_HISTORY}</maxHistory>
                <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
            </rollingPolicy>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>

        <!-- 🟡 WARN日志文件 -->
        <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>./logs/${SERVICE_NAME}-dev_warn.log</file>
            <encoder>
                <pattern>${LOG_PATTERN}</pattern>
                <charset>${LOG_CHARSET}</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>./logs/${SERVICE_NAME}-dev_warn-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>${MAX_HISTORY}</maxHistory>
                <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
            </rollingPolicy>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>

        <!-- 📝 日志输出配置 -->
        <root level="WARN">
            <appender-ref ref="ERROR_FILE"/>
            <appender-ref ref="WARN_FILE"/>
        </root>
    </springProfile>

    <!-- ========================================
         🏭 生产环境配置
         ======================================== -->
    <springProfile name="prod">
        <!-- 🔴 ERROR日志文件 -->
        <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>./logs/${SERVICE_NAME}-prod_error.log</file>
            <encoder>
                <pattern>${LOG_PATTERN}</pattern>
                <charset>${LOG_CHARSET}</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>./logs/${SERVICE_NAME}-prod_error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>${MAX_HISTORY}</maxHistory>
                <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
            </rollingPolicy>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>

        <!-- 🟡 WARN日志文件 -->
        <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>./logs/${SERVICE_NAME}-prod_warn.log</file>
            <encoder>
                <pattern>${LOG_PATTERN}</pattern>
                <charset>${LOG_CHARSET}</charset>
            </encoder>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>./logs/${SERVICE_NAME}-prod_warn-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>${MAX_HISTORY}</maxHistory>
                <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
            </rollingPolicy>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>WARN</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
        </appender>

        <!-- 📝 日志输出配置 -->
        <root level="WARN">
            <appender-ref ref="ERROR_FILE"/>
            <appender-ref ref="WARN_FILE"/>
        </root>
    </springProfile>

    <!-- ========================================
         📊 配置文件结构说明
         ========================================

         📁 文件结构：
         ├── 🔧 全局配置参数
         ├── 🔇 第三方库日志级别控制
         ├── 🛠️ 开发/测试环境配置
         └── 🏭 生产环境配置

         📋 日志文件输出：
         ├── 🔴 ERROR: ${SERVICE_NAME}-{env}_error.log
         └── 🟡 WARN:  ${SERVICE_NAME}-{env}_warn.log

         ⚙️ 滚动策略：
         ├── 📏 单文件大小: ${MAX_FILE_SIZE}
         ├── 📅 保留天数: ${MAX_HISTORY}天
         └── 💾 总大小限制: ${TOTAL_SIZE_CAP}

         ======================================== -->

</configuration>

