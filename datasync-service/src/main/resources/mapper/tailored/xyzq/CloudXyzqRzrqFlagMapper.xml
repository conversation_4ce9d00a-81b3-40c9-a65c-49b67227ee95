<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.sync.modules.tailored.xyzq.mapper.CloudXyzqRzrqFlagMapper">
  <resultMap id="BaseResultMap" type="com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqMrgTrdFlagDO">
    <!--@mbg.generated-->
    <!--@Table credit.t_xyzq_rzrq_flag-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="flag" jdbcType="TINYINT" property="flag" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `date`, `table_name`, flag, `source`, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from credit.t_xyzq_rzrq_flag
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from credit.t_xyzq_rzrq_flag
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqMrgTrdFlagDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into credit.t_xyzq_rzrq_flag (`date`, `table_name`, flag, 
      `source`, create_time, update_time
      )
    values (#{date,jdbcType=DATE}, #{tableName,jdbcType=VARCHAR}, #{flag,jdbcType=TINYINT}, 
      #{source,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqMrgTrdFlagDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into credit.t_xyzq_rzrq_flag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="date != null">
        `date`,
      </if>
      <if test="tableName != null and tableName != ''">
        `table_name`,
      </if>
      <if test="flag != null">
        flag,
      </if>
      <if test="source != null and source != ''">
        `source`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="tableName != null and tableName != ''">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=TINYINT},
      </if>
      <if test="source != null and source != ''">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqMrgTrdFlagDO">
    <!--@mbg.generated-->
    update credit.t_xyzq_rzrq_flag
    <set>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="tableName != null and tableName != ''">
        `table_name` = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        flag = #{flag,jdbcType=TINYINT},
      </if>
      <if test="source != null and source != ''">
        `source` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tjsj.sync.modules.tailored.xyzq.model.entity.CloudXyzqMrgTrdFlagDO">
    <!--@mbg.generated-->
    update credit.t_xyzq_rzrq_flag
    set `date` = #{date,jdbcType=DATE},
      `table_name` = #{tableName,jdbcType=VARCHAR},
      flag = #{flag,jdbcType=TINYINT},
      `source` = #{source,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>