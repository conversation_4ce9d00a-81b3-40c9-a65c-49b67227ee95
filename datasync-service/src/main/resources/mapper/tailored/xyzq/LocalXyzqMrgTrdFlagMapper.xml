<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.sync.modules.tailored.xyzq.mapper.LocalXyzqMrgTrdFlagMapper">
  <resultMap id="BaseResultMap" type="com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqMrgTrdFlagDO">
    <!--@mbg.generated-->
    <!--@Table credit.t_xyzq_rzrq_flag-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="flag" jdbcType="TINYINT" property="flag" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `date`, `table_name`, flag, `source`, create_time, update_time
  </sql>

  <insert id="insertBatch" useGeneratedKeys="false">
      insert into credit.t_xyzq_rzrq_flag
          (id, date, table_name, flag, source, create_time, update_time)
      values
      <foreach collection="list" item="item" separator=",">
          (#{item.id}, #{item.date}, #{item.tableName}, #{item.flag}, #{item.source}, #{item.createTime},
           #{item.updateTime})
      </foreach>
  </insert>
</mapper>