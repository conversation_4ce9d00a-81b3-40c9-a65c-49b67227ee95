<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.sync.modules.tailored.xyzq.mapper.LocalXyzqBdzqMapper">
  <resultMap id="BaseResultMap" type="com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqBdzqDO">
    <!--@mbg.generated-->
    <!--@Table credit.t_rzrq_xyzq_bdzq-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sec_code" jdbcType="VARCHAR" property="secCode" />
    <result column="sec_name" jdbcType="VARCHAR" property="secName" />
    <result column="market" jdbcType="VARCHAR" property="market" />
    <result column="sse_date" jdbcType="DATE" property="sseDate" />
    <result column="rzbd" jdbcType="TINYINT" property="rzbd" />
    <result column="rz_rate" jdbcType="DECIMAL" property="rzRate" />
    <result column="rqbd" jdbcType="TINYINT" property="rqbd" />
    <result column="rq_rate" jdbcType="DECIMAL" property="rqRate" />
    <result column="mark" jdbcType="VARCHAR" property="mark" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="enable_status" jdbcType="BOOLEAN" property="enableStatus" />
    <result column="if_market_checked" jdbcType="BOOLEAN" property="ifMarketChecked" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sec_code, sec_name, market, sse_date, rzbd, rz_rate, rqbd, rq_rate, mark, `date`, 
    `source`, create_time, update_time, enable_status, if_market_checked
  </sql>

  <insert id="insertBatch" useGeneratedKeys="false">
      insert into credit.t_rzrq_xyzq_bdzq
      (id, sec_code, sec_name, market, sse_date, rzbd, rz_rate, rqbd, rq_rate, mark, date, source, create_time,
       update_time, enable_status, if_market_checked)
      values
      <foreach collection="list" item="item" separator=",">
          (#{item.id}, #{item.secCode}, #{item.secName}, #{item.market}, #{item.sseDate}, #{item.rzbd}, #{item.rzRate},
           #{item.rqbd}, #{item.rqRate}, #{item.mark}, #{item.date}, #{item.source}, #{item.createTime},
           #{item.updateTime},
           #{item.enableStatus}, #{item.ifMarketChecked})
      </foreach>
  </insert>
</mapper>