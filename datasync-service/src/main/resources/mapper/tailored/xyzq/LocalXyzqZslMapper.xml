<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.sync.modules.tailored.xyzq.mapper.LocalXyzqZslMapper">

    <insert id="insertBatch" useGeneratedKeys="false">
        insert into credit.t_rzrq_xyzq_zsl
        (id, sec_code, sec_name, market, sse_date, rate, `group`, status, date, source, create_time, update_time,
         enable_status, if_market_checked)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.secCode}, #{item.secName}, #{item.market}, #{item.sseDate}, #{item.rate}, #{item.group},
             #{item.status}, #{item.date}, #{item.source}, #{item.createTime}, #{item.updateTime}, #{item.enableStatus},
             #{item.ifMarketChecked})
        </foreach>
    </insert>
</mapper>