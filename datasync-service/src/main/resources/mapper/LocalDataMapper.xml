<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.sync.modules.sync.mapper.LocalDataMapper">

    <update id="truncateTableData">
        truncate table ${deleteTableName}
    </update>

    <delete id="deleteTableDataByCondition">

        delete
        from ${deleteTableName}
        <where>
            <if test="deleteTableCondition != null and deleteTableCondition != ''">
                ${deleteTableCondition}
            </if>
            <if test="deleteStartId">
                and id >= #{deleteStartId,jdbcType=INTEGER}
            </if>
            <if test="deleteStartUpdateTime != null">
                and update_time >= #{deleteStartUpdateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </delete>

    <update id="truncateInsertTableData">
        truncate table ${tableConfig.schemaName}.${tableConfig.tableName}
    </update>

    <select id="test" resultType="java.util.Map">
        select * from tj_middle_ground.t_stock_info
        where stock_id = '000001'
    </select>

    <select id="getTableDataMaxId" resultType="java.lang.Long">
        select max(id) as id from ${table.schemaName}.${table.tableName}
    </select>

    <insert id="insertTableData">
        INSERT INTO ${table.schemaName}.${table.tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <foreach collection="data" item="value" index="key">
                ${key},
            </foreach>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <foreach collection="data" item="value" index="key">
                #{data[${key}]},
            </foreach>
        </trim>
        ON DUPLICATE KEY UPDATE
        <foreach collection="data" item="value" index="key" separator=",">
            ${key} = #{data[${key}]}
        </foreach>
    </insert>

    <select id="getTableMaxUpdateTime" resultType="java.time.LocalDateTime">
        select max(update_time) as update_time
        from ${tableConfig.schemaName}.${tableConfig.tableName}
    </select>

    <insert id="batchInsertTableData" parameterType="map">

        INSERT INTO ${tableConfig.schemaName}.${tableConfig.tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <foreach collection="dataList[0].keySet()" item="key">
                ${key},
            </foreach>
        </trim>
        VALUES
        <foreach collection="dataList" item="data" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="data" item="value" index="key">
                    #{value},
                </foreach>
            </trim>
        </foreach>
        ON DUPLICATE KEY UPDATE
        <foreach collection="dataList[0].keySet()" item="key" separator=",">
            ${key} = VALUES(${key})
        </foreach>

    </insert>



    <insert id="batchInsertTableDataByCondition">
        INSERT INTO ${syncInfo.deleteTableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <foreach collection="dataList[0].keySet()" item="key">
                ${key},
            </foreach>
        </trim>
        VALUES
        <foreach collection="dataList" item="data" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="data" item="value" index="key">
                    #{value},
                </foreach>
            </trim>
        </foreach>
        ON DUPLICATE KEY UPDATE
        <foreach collection="dataList[0].keySet()" item="key" separator=",">
            ${key} = VALUES(${key})
        </foreach>
    </insert>

    <select id="executeSqlToLocal">
        ${executeSql}
    </select>

    <select id="selectTableDataByCondition" resultType="java.util.Map">
        select *
        from ${syncInfo.insertTableName}
        <where>
            <if test="syncInfo.insertTableCondition != null and syncInfo.insertTableCondition != ''">
                ${syncInfo.insertTableCondition}
            </if>
            <if test="syncInfo.insertStartId != null and syncInfo.insertStartId != ''">
                and id >= #{syncInfo.insertStartId}
            </if>
            <if test="syncInfo.insertStartUpdateTime != null and syncInfo.insertStartUpdateTime != ''">
                and update_time >= #{syncInfo.insertStartUpdateTime}
            </if>
        </where>
        limit ${offset}, ${batchSize}
    </select>

    <select id="listTableColumns" resultType="java.lang.String">
        SELECT COLUMN_NAME
        FROM information_schema.COLUMNS
        WHERE TABLE_SCHEMA = #{schemaName,jdbcType=VARCHAR}
        AND TABLE_NAME = #{tableName,jdbcType=VARCHAR}
    </select>

    <select id="selectTableDataByDataRange" resultType="java.util.Map">
        SELECT *
        FROM ${tableConfig.dataSchemaName}.${tableConfig.dataTableName}
        <choose>
            <when test="quartzSyncType != null and quartzSyncType == true ">
                <if test="null != updateTime">
                    WHERE update_time >= #{updateTime,jdbcType=TIMESTAMP}
                </if>
                order by update_time asc
            </when>
            <otherwise>
                <if test="null != updateTime">
                    WHERE update_time >= #{updateTime,jdbcType=TIMESTAMP}
                </if>
            </otherwise>
        </choose>

        limit ${offset}, ${batchSize}
    </select>

    <select id="getTableGroupDataCount" resultType="TypeCountVO">

        select f.${tableGroupByColumn} as typeName, ifnull(count(0),0) as count
        from ${schemaName}.${tableName} f
        group by f.${tableGroupByColumn} ;

    </select>

    <select id="getTableGroupByDataCountTierTwo" resultType="TypeCountVO">
        select f.${tableGroupByColumnTierTwo} as typeName, ifnull(count(0),0) as count
        from ${schemaName}.${tableName} f
        where f.${tableGroupByColumnTierOne} = #{filteredGroupByColumnValue,jdbcType=VARCHAR}
        group by f.${tableGroupByColumnTierTwo}
        order by f.${tableGroupByColumnTierTwo};
    </select>

    <select id="getFieldNameValueList" resultType="java.lang.String">
        SELECT
        f.FiledName
        FROM
        ${schemaName}.${tableName} f
        GROUP BY
        f.FiledName;
    </select>

    <delete id="deleteRedundantFiledNameData">
        delete
        from ${schemaName}.${tableName}
        where FiledName in
        <if test="redundantFiledNameList != null and redundantFiledNameList.size() != 0">
            <foreach collection="redundantFiledNameList" item="redundantFiledName" open="(" separator="," close=")">
                #{redundantFiledName}
            </foreach>
        </if>
    </delete>

    <insert id="insertBatchXyzqBdzq">
        insert into credit.t_rzrq_xyzq_bdzq
        ( sec_code, sec_name, market, sse_date, rzbd, rz_rate, rqbd, rq_rate, mark, date, source, create_time,
        update_time, enable_status, if_market_checked)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.secCode}, #{item.secName}, #{item.market}, #{item.sseDate}, #{item.rzbd}, #{item.rzRate},
            #{item.rqbd}, #{item.rqRate}, #{item.mark}, #{item.date}, #{item.source}, #{item.createTime},
            #{item.updateTime},
            #{item.enableStatus}, #{item.ifMarketChecked})
        </foreach>
    </insert>

    <insert id="insertBatchXyzqZsl">
        insert into credit.t_rzrq_xyzq_zsl
        (sec_code, sec_name, market, sse_date, rate, `group`, status, date, source, create_time, update_time,
         enable_status, if_market_checked)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.secCode}, #{item.secName}, #{item.market}, #{item.sseDate}, #{item.rate}, #{item.group},
             #{item.status}, #{item.date}, #{item.source}, #{item.createTime}, #{item.updateTime},
             #{item.enableStatus}, #{item.ifMarketChecked})
        </foreach>
    </insert>

    <insert id="insertBatchXyzqMrgTrdFlag">
        insert into credit.t_xyzq_rzrq_flag
        (id, date, table_name, flag, source, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.date}, #{item.tableName}, #{item.flag}, #{item.source}, #{item.createTime},
            #{item.updateTime})
        </foreach>
    </insert>

    <delete id="testDeleteDistributedLock">
        delete
        from tj_middle_ground.distributed_lock
        where id = 1;
    </delete>

    <delete id="removeXyzqMrgTrdFlagByIds">
        delete
        from credit.t_xyzq_rzrq_flag
        where id in
        <foreach collection="increRecordIdList" item="increRecordId" open="(" separator="," close=")">
            #{increRecordId,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>