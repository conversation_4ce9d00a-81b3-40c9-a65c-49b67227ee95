<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- ========================================
         📋 数据同步服务日志配置文件 (修复版)
         🎯 功能：分离WARN和ERROR级别日志到不同文件
         📅 更新：2025-06-30
         🔧 修复：解决${spring.profiles.active}无法读取问题
         ======================================== -->

    <include resource="org/springframework/boot/logging/logback/base.xml"/>

    <!-- ========================================
         🔧 全局配置参数 (统一管理所有配置)
         ======================================== -->
    <property name="SERVICE_NAME" value="datasync-service"/>
    <property name="LOG_PATTERN" value="%date %level [%thread] %logger{10} [%file:%line] %msg%n"/>
    <property name="LOG_CHARSET" value="UTF-8"/>
    <property name="MAX_FILE_SIZE" value="10MB"/>
    <property name="MAX_HISTORY" value="30"/>
    <property name="TOTAL_SIZE_CAP" value="5GB"/>
    <property name="LOG_DIR" value="./logs"/>

    <!-- 🎯 环境配置：如果获取不到spring.profiles.active，则默认使用dev -->
    <property name="PROFILE" value="${spring.profiles.active:-dev}"/>

    <!-- ========================================
         🔇 第三方库日志级别控制
         ======================================== -->
    <logger name="c.n.d.s.r.aws.ConfigClusterResolver" level="ERROR"/>
    <logger name="org.springframework.web" level="ERROR"/>
    <logger name="org.springboot.sample" level="ERROR"/>

    <!-- ========================================
         📁 日志输出器定义 (使用默认值方案)
         ======================================== -->

    <!-- 🔴 ERROR级别日志输出器 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${SERVICE_NAME}-${PROFILE}_error.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${LOG_CHARSET}</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${SERVICE_NAME}/${SERVICE_NAME}-${PROFILE}_error-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 🟡 WARN级别日志输出器 -->
    <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${SERVICE_NAME}-${PROFILE}_warn.log</file>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>${LOG_CHARSET}</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${SERVICE_NAME}/${SERVICE_NAME}-${PROFILE}_warn-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>${TOTAL_SIZE_CAP}</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- ========================================
         📝 统一日志配置
         ======================================== -->
    <root level="WARN">
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="WARN_FILE"/>
    </root>

    <!-- ========================================
         📊 默认值方案说明
         ========================================

         🎯 解决方案：使用默认值语法
         - 语法：${spring.profiles.active:-dev}
         - 含义：如果获取不到spring.profiles.active，则使用dev
         - 优点：简洁、向后兼容、降级友好

         🔧 工作原理：
         1. 优先尝试读取${spring.profiles.active}
         2. 如果读取失败或为空，自动使用默认值dev
         3. 确保在任何情况下都有有效的环境标识

         📋 日志文件输出：
         ├── 🔴 ERROR: ${SERVICE_NAME}-${PROFILE}_error.log
         └── 🟡 WARN:  ${SERVICE_NAME}-${PROFILE}_warn.log

         🔄 环境映射：
         ├── 🛠️ dev/test/local → datasync-service-dev_*.log
         ├── 🏭 prod → datasync-service-prod_*.log
         └── 🔧 未设置/获取失败 → datasync-service-dev_*.log (默认)

         ⚠️ 注意事项：
         - 确保application.yml中正确设置spring.profiles.active
         - 或在启动时使用-Dspring.profiles.active=xxx
         - 默认值dev适用于开发环境，生产环境务必明确设置

         ======================================== -->

</configuration>
