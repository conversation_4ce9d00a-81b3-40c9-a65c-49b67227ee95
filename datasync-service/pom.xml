<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tjsj</groupId>
        <artifactId>sync-common-pom</artifactId>
        <version>1.0</version>
        <relativePath>../sync-common-pom/pom.xml</relativePath>
    </parent>

    <artifactId>datasync-service</artifactId>
    <version>1.0
    </version>
    <name>datasync-service</name>
    <packaging>jar</packaging>
    <dependencies>

        <dependency>
            <groupId>com.tjsj</groupId>
            <artifactId>sync-common-code</artifactId>
            <version>1.0</version>
        </dependency>
        <!-- 校验注解 -->
        <dependency>
            <groupId>jakarta.platform</groupId>
            <artifactId>jakarta.jakartaee-web-api</artifactId>
            <version>9.1.0</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <!-- devtools依赖此配置（否则，devtools不生效）。 -->
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>